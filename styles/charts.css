/* Professional Chart Styling */

/* Chart Container Styling */
.chart-container {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.chart-container:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Chart Title Styling */
.chart-title {
  font-weight: 600;
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Canvas Styling */
.chart-container canvas {
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
}

/* Chart Legend Styling */
.chart-container .chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Professional Grid Lines */
.chart-grid {
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 1;
  stroke-dasharray: 2, 2;
}

/* Chart Tooltips */
.chart-tooltip {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 0.875rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

/* Pie Chart Enhancements */
.pie-chart-container {
  position: relative;
}

.pie-chart-center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.pie-chart-total {
  font-size: 1.5rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
}

.pie-chart-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* Bar Chart Enhancements */
.bar-chart-container .bar {
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.bar-chart-container .bar:hover {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  transform: translateY(-1px);
}

/* Line Chart Enhancements */
.line-chart-container .line {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.line-chart-container .point {
  transition: all 0.2s ease;
}

.line-chart-container .point:hover {
  transform: scale(1.2);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Chart Loading States */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Chart Export Menu */
.chart-export-menu {
  position: absolute;
  top: 16px;
  right: 16px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chart-container:hover .chart-export-menu {
  opacity: 1;
}

/* Responsive Chart Styling */
@media (max-width: 768px) {
  .chart-container {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .chart-title {
    font-size: 1rem;
  }
  
  .chart-legend {
    gap: 8px;
  }
  
  .chart-legend-item {
    font-size: 0.8rem;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .chart-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.15);
  }
  
  .chart-container:hover {
    border-color: rgba(255, 255, 255, 0.25);
  }
  
  .chart-grid {
    stroke: rgba(255, 255, 255, 0.15);
  }
}

/* Chart Animation Classes */
.chart-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.chart-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Professional Color Gradients for Charts */
.gradient-primary {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.gradient-tertiary {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}
