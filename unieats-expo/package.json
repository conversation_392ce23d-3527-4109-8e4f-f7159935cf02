{"name": "unieats-expo", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "~2.1.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@supabase/supabase-js": "^2.39.0", "expo": "~53.0.0", "expo-font": "~13.0.1", "expo-image": "~2.0.0", "expo-linear-gradient": "~14.0.1", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.0.0", "react": "18.2.0", "react-dom": "^19.0.0", "react-native": "0.76.3", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.0.3", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}