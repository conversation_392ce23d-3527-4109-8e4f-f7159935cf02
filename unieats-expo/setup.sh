#!/bin/bash

echo "🚀 Setting up UniEats Expo React Native App..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install Expo CLI globally if not already installed
if ! command -v expo &> /dev/null; then
    echo "📦 Installing Expo CLI globally..."
    npm install -g @expo/cli
else
    echo "✅ Expo CLI is already installed"
fi

# Install project dependencies
echo "📦 Installing project dependencies..."
npm install

# Create assets directories if they don't exist
echo "📁 Creating assets directories..."
mkdir -p assets/fonts
mkdir -p assets/images

# Create placeholder files for required assets
echo "🖼️ Creating placeholder asset files..."

# Create a simple icon.png placeholder (you'll need to replace this with actual images)
echo "Please add the following files to the assets directory:"
echo "  - assets/icon.png (1024x1024)"
echo "  - assets/splash.png (1242x2436)" 
echo "  - assets/adaptive-icon.png (1024x1024)"
echo "  - assets/favicon.png (48x48)"
echo ""
echo "Please add the following font files to assets/fonts/:"
echo "  - Inter-Regular.ttf"
echo "  - Inter-Medium.ttf"
echo "  - Inter-SemiBold.ttf"
echo "  - Inter-Bold.ttf"
echo ""
echo "You can download Inter fonts from: https://fonts.google.com/specimen/Inter"

echo ""
echo "🎉 Setup complete! You can now run the app with:"
echo "   npm start"
echo ""
echo "📱 To run on iOS Simulator:"
echo "   npm run ios"
echo ""
echo "📱 To run on Android:"
echo "   npm run android"
