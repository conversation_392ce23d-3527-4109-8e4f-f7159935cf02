# UniEats Expo React Native App

This is the iOS version of the UniEats app built with Expo React Native, designed to work alongside the Flutter Android version.

## Features

- 🍽️ Browse cafeterias and menu items
- 🛒 Shopping cart functionality
- ❤️ Favorites management
- 📱 Modern orange-themed UI matching Flutter app
- 🔐 Supabase authentication
- 📊 Real-time data synchronization
- 🔍 Search functionality
- 📋 Order history

## Setup Instructions

### Prerequisites

- Node.js (v16 or higher)
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (for development)

### Installation

1. Navigate to the expo directory:
```bash
cd unieatsappv0/unieats-expo
```

2. Install dependencies:
```bash
npm install
```

3. Download and place Inter fonts in `assets/fonts/`:
   - Inter-Regular.ttf
   - Inter-Medium.ttf
   - Inter-SemiBold.ttf
   - Inter-Bold.ttf

4. Add placeholder images in `assets/`:
   - icon.png (1024x1024)
   - splash.png (1242x2436)
   - adaptive-icon.png (1024x1024)
   - favicon.png (48x48)

### Running the App

1. Start the development server:
```bash
npm start
```

2. Run on iOS Simulator:
```bash
npm run ios
```

3. Run on physical device:
   - Install Expo Go app on your iOS device
   - Scan the QR code from the terminal

## Project Structure

```
src/
├── config/
│   └── supabase.js          # Supabase configuration
├── providers/
│   ├── AuthProvider.js      # Authentication state management
│   ├── SupabaseProvider.js  # Supabase data management
│   ├── CartProvider.js      # Shopping cart state
│   └── FavoritesProvider.js # Favorites management
├── screens/
│   ├── HomeScreen.js        # Main cafeterias screen
│   ├── CafeteriaScreen.js   # Menu items for cafeteria
│   ├── ItemDetailsScreen.js # Individual item details
│   ├── CartScreen.js        # Shopping cart
│   ├── FavoritesScreen.js   # User favorites
│   ├── SearchScreen.js      # Search functionality
│   ├── ProfileScreen.js     # User profile
│   ├── LoginScreen.js       # Authentication
│   ├── RegisterScreen.js    # User registration
│   └── OrderHistoryScreen.js # Order history
└── theme/
    └── ModernTheme.js       # App theme and styling
```

## Supabase Integration

The app connects to the same Supabase backend as the Flutter app:
- URL: `https://lqtnaxvqkoynaziiinqh.supabase.co`
- Shared database tables and authentication
- Real-time synchronization between platforms

## Key Features

### Modern Orange Theme
- Consistent with Flutter app design
- Orange accent color (#FF6B35)
- Modern gradients and shadows
- Smooth animations and transitions

### Cross-Platform Compatibility
- Shares Supabase backend with Flutter Android app
- Synchronized user data and preferences
- Consistent business logic across platforms

### iOS-Optimized
- Native iOS navigation patterns
- iOS-specific UI components
- Optimized for iPhone and iPad

## Development Notes

- Uses Expo SDK 50
- React Navigation 6 for navigation
- Expo Image for optimized image loading
- Linear gradients for modern UI
- AsyncStorage for local data persistence

## Building for Production

1. Configure app.json with your bundle identifier
2. Build for iOS:
```bash
eas build --platform ios
```

3. Submit to App Store:
```bash
eas submit --platform ios
```

## Troubleshooting

### Common Issues

1. **Metro bundler issues**: Clear cache with `npx expo start --clear`
2. **Font loading errors**: Ensure fonts are in correct directory
3. **Supabase connection**: Check network connectivity and API keys
4. **iOS Simulator**: Restart simulator if app doesn't load

### Support

For issues specific to this Expo app, check:
- Expo documentation: https://docs.expo.dev/
- React Navigation: https://reactnavigation.org/
- Supabase React Native: https://supabase.com/docs/guides/getting-started/quickstarts/reactnative
