import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import 'react-native-url-polyfill/auto';

// Supabase configuration - same as Flutter app
const supabaseUrl = 'https://lqtnaxvqkoynaziiinqh.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxxdG5heHZxa295bmF6aWlpbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI5NzQsImV4cCI6MjA1MDU0ODk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database table names
export const TABLES = {
  CAFETERIAS: 'cafeterias',
  MENU_ITEMS: 'menu_items',
  ORDERS: 'orders',
  ORDER_ITEMS: 'order_items',
  USERS: 'users',
  FAVORITES: 'favorites',
  RATINGS: 'ratings',
  CAFETERIA_FEEDBACK: 'cafeteria_feedback',
  USER_FEEDBACK: 'user_feedback',
  CHAT_CONVERSATIONS: 'chat_conversations',
  CHAT_MESSAGES: 'chat_messages',
  SUPPORT_TICKETS: 'support_tickets',
};

// Storage buckets
export const BUCKETS = {
  CAFETERIA_IMAGES: 'cafeteria_images',
  MENU_ITEM_IMAGES: 'menu_item_images',
  PROFILE_IMAGES: 'profile_images',
};

// Helper functions for common operations
export const supabaseHelpers = {
  // Get public URL for storage files
  getPublicUrl: (bucket, path) => {
    if (!path) return null;
    const { data } = supabase.storage.from(bucket).getPublicUrl(path);
    return data?.publicUrl || null;
  },

  // Upload file to storage
  uploadFile: async (bucket, path, file) => {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  },

  // Delete file from storage
  deleteFile: async (bucket, path) => {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([path]);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  },

  // Format error messages
  formatError: (error) => {
    if (error?.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const session = supabase.auth.getSession();
    return !!session?.data?.session?.user;
  },

  // Get current user
  getCurrentUser: () => {
    const session = supabase.auth.getSession();
    return session?.data?.session?.user || null;
  },
};
