import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { ModernTheme } from '../theme/ModernTheme';
import { useCart } from '../providers/CartProvider';
import { useFavorites } from '../providers/FavoritesProvider';

const ItemDetailsScreen = ({ navigation, route }) => {
  const { item } = route.params;
  const { addItem } = useCart();
  const { isInFavorites, toggleFavorite } = useFavorites();
  const [quantity, setQuantity] = useState(1);

  const handleAddToCart = () => {
    addItem(item, quantity);
    Alert.alert(
      'Added to Cart',
      `${item.name} has been added to your cart`,
      [
        { text: 'Continue Shopping', style: 'cancel' },
        { text: 'View Cart', onPress: () => navigation.navigate('Cart') }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={ModernTheme.colors.textPrimary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => toggleFavorite(item.id)}
        >
          <Ionicons
            name={isInFavorites(item.id) ? "heart" : "heart-outline"}
            size={24}
            color={isInFavorites(item.id) ? "#FF6B6B" : ModernTheme.colors.textPrimary}
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Item Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: item.image_url || 'https://via.placeholder.com/400x300' }}
            style={styles.itemImage}
            contentFit="cover"
          />
        </View>

        {/* Item Info */}
        <View style={styles.itemInfo}>
          <Text style={styles.itemName}>{item.name}</Text>
          <Text style={styles.cafeteriaName}>
            {item.cafeteria?.name || 'Unknown Cafeteria'}
          </Text>
          
          {item.description && (
            <Text style={styles.description}>{item.description}</Text>
          )}

          <Text style={styles.price}>{item.price?.toFixed(2)} EGP</Text>

          {/* Quantity Selector */}
          <View style={styles.quantitySection}>
            <Text style={styles.quantityLabel}>Quantity</Text>
            <View style={styles.quantityControls}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(Math.max(1, quantity - 1))}
              >
                <Ionicons name="remove" size={20} color={ModernTheme.colors.accent} />
              </TouchableOpacity>
              
              <Text style={styles.quantityText}>{quantity}</Text>
              
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(quantity + 1)}
              >
                <Ionicons name="add" size={20} color={ModernTheme.colors.accent} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Add to Cart Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.addToCartButton}
          onPress={handleAddToCart}
          disabled={!item.is_available}
        >
          <LinearGradient
            colors={item.is_available ? ModernTheme.gradients.accent : ['#ccc', '#ccc']}
            style={styles.addToCartGradient}
          >
            <Ionicons name="bag-add" size={20} color="white" />
            <Text style={styles.addToCartText}>
              {item.is_available ? `Add to Cart • ${(item.price * quantity).toFixed(2)} EGP` : 'Unavailable'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.s,
    backgroundColor: ModernTheme.colors.surface,
  },
  backButton: {
    padding: ModernTheme.spacing.s,
  },
  favoriteButton: {
    padding: ModernTheme.spacing.s,
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    height: 300,
  },
  itemImage: {
    width: '100%',
    height: '100%',
  },
  itemInfo: {
    backgroundColor: ModernTheme.colors.surface,
    padding: ModernTheme.spacing.l,
    borderTopLeftRadius: ModernTheme.radius.xl,
    borderTopRightRadius: ModernTheme.radius.xl,
    marginTop: -20,
  },
  itemName: {
    ...ModernTheme.typography.headingLarge,
    marginBottom: ModernTheme.spacing.s,
  },
  cafeteriaName: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginBottom: ModernTheme.spacing.m,
  },
  description: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginBottom: ModernTheme.spacing.l,
    lineHeight: 24,
  },
  price: {
    ...ModernTheme.typography.headingMedium,
    color: ModernTheme.colors.accent,
    fontWeight: '700',
    marginBottom: ModernTheme.spacing.l,
  },
  quantitySection: {
    marginBottom: ModernTheme.spacing.l,
  },
  quantityLabel: {
    ...ModernTheme.typography.labelLarge,
    marginBottom: ModernTheme.spacing.s,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${ModernTheme.colors.accent}15`,
    borderRadius: ModernTheme.radius.medium,
    borderWidth: 1,
    borderColor: `${ModernTheme.colors.accent}30`,
    alignSelf: 'flex-start',
  },
  quantityButton: {
    padding: ModernTheme.spacing.m,
  },
  quantityText: {
    ...ModernTheme.typography.headingSmall,
    color: ModernTheme.colors.accent,
    fontWeight: '600',
    paddingHorizontal: ModernTheme.spacing.l,
  },
  footer: {
    backgroundColor: ModernTheme.colors.surface,
    padding: ModernTheme.spacing.m,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.colors.border,
  },
  addToCartButton: {
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.medium,
  },
  addToCartGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
  },
  addToCartText: {
    ...ModernTheme.typography.labelLarge,
    color: 'white',
    fontWeight: '600',
    marginLeft: ModernTheme.spacing.s,
  },
});

export default ItemDetailsScreen;
