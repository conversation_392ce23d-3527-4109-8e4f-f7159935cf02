import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { ModernTheme } from '../theme/ModernTheme';
import { useAuth } from '../providers/AuthProvider';
import { useSupabase } from '../providers/SupabaseProvider';

const OrderHistoryScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { getUserOrders } = useSupabase();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadOrders();
    }
  }, [user]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const userOrders = await getUserOrders(user.id);
      setOrders(userOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return ModernTheme.colors.warning;
      case 'confirmed':
        return ModernTheme.colors.info;
      case 'preparing':
        return ModernTheme.colors.accent;
      case 'ready':
        return ModernTheme.colors.success;
      case 'delivered':
        return ModernTheme.colors.success;
      case 'cancelled':
        return ModernTheme.colors.error;
      default:
        return ModernTheme.colors.textSecondary;
    }
  };

  const renderOrderItem = (order) => (
    <TouchableOpacity key={order.id} style={styles.orderItem}>
      <View style={styles.orderHeader}>
        <Text style={styles.orderNumber}>Order #{order.id.slice(-6)}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
          <Text style={styles.statusText}>{order.status.toUpperCase()}</Text>
        </View>
      </View>

      <Text style={styles.cafeteriaName}>{order.cafeteria?.name}</Text>
      <Text style={styles.orderDate}>
        {new Date(order.created_at).toLocaleDateString()}
      </Text>

      <View style={styles.orderFooter}>
        <Text style={styles.orderTotal}>{order.total_amount?.toFixed(2)} EGP</Text>
        <Text style={styles.itemCount}>
          {order.order_items?.length || 0} items
        </Text>
      </View>
    </TouchableOpacity>
  );

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={ModernTheme.colors.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Order History</Text>
        </View>
        
        <View style={styles.emptyContainer}>
          <Ionicons name="person-outline" size={64} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyTitle}>Login Required</Text>
          <Text style={styles.emptySubtitle}>
            Please login to view your order history
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={ModernTheme.colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Order History</Text>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.colors.accent} />
          <Text style={styles.loadingText}>Loading orders...</Text>
        </View>
      ) : orders.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="receipt-outline" size={64} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyTitle}>No orders yet</Text>
          <Text style={styles.emptySubtitle}>
            Your order history will appear here
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <View style={styles.ordersContainer}>
            {orders.map(renderOrderItem)}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.m,
    backgroundColor: ModernTheme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  backButton: {
    padding: ModernTheme.spacing.s,
    marginRight: ModernTheme.spacing.s,
  },
  headerTitle: {
    ...ModernTheme.typography.headingMedium,
  },
  scrollView: {
    flex: 1,
  },
  ordersContainer: {
    padding: ModernTheme.spacing.m,
  },
  orderItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.radius.large,
    padding: ModernTheme.spacing.m,
    marginBottom: ModernTheme.spacing.m,
    ...ModernTheme.shadows.medium,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: ModernTheme.spacing.s,
  },
  orderNumber: {
    ...ModernTheme.typography.headingSmall,
  },
  statusBadge: {
    paddingHorizontal: ModernTheme.spacing.s,
    paddingVertical: 4,
    borderRadius: ModernTheme.radius.small,
  },
  statusText: {
    ...ModernTheme.typography.labelSmall,
    color: 'white',
    fontSize: 10,
  },
  cafeteriaName: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 4,
  },
  orderDate: {
    ...ModernTheme.typography.bodySmall,
    color: ModernTheme.colors.textSecondary,
    marginBottom: ModernTheme.spacing.s,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderTotal: {
    ...ModernTheme.typography.headingSmall,
    color: ModernTheme.colors.accent,
    fontWeight: '700',
  },
  itemCount: {
    ...ModernTheme.typography.bodySmall,
    color: ModernTheme.colors.textSecondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginTop: ModernTheme.spacing.m,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ModernTheme.spacing.l,
  },
  emptyTitle: {
    ...ModernTheme.typography.headingMedium,
    marginTop: ModernTheme.spacing.l,
    marginBottom: ModernTheme.spacing.s,
  },
  emptySubtitle: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
  },
});

export default OrderHistoryScreen;
