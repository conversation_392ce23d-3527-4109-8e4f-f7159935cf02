import React, { useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';

import { ModernTheme } from '../theme/ModernTheme';
import { useFavorites } from '../providers/FavoritesProvider';
import { useCart } from '../providers/CartProvider';
import { useAuth } from '../providers/AuthProvider';

const FavoritesScreen = ({ navigation }) => {
  const { 
    favorites, 
    loading, 
    loadFavorites, 
    toggleFavorite, 
    getFavoriteMenuItems 
  } = useFavorites();
  const { addItem, getQuantity, updateQuantity, removeItem } = useCart();
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      loadFavorites();
    }
  }, [user]);

  const favoriteMenuItems = getFavoriteMenuItems();

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>Favorites</Text>
      <Text style={styles.headerSubtitle}>
        {favoriteMenuItems.length} {favoriteMenuItems.length === 1 ? 'item' : 'items'}
      </Text>
    </View>
  );

  const renderQuantityControls = (item) => {
    const quantity = getQuantity(item.id);
    const isAvailable = item.is_available;

    if (!isAvailable) {
      return (
        <View style={styles.unavailableButton}>
          <Text style={styles.unavailableText}>Unavailable</Text>
        </View>
      );
    }

    if (quantity === 0) {
      return (
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => addItem(item, 1)}
        >
          <LinearGradient
            colors={ModernTheme.gradients.accent}
            style={styles.addButtonGradient}
          >
            <Ionicons name="add" size={20} color="white" />
          </LinearGradient>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.quantityControls}>
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => {
            if (quantity > 1) {
              updateQuantity(item.id, quantity - 1);
            } else {
              removeItem(item.id);
            }
          }}
        >
          <Ionicons 
            name={quantity > 1 ? "remove" : "trash-outline"} 
            size={16} 
            color={ModernTheme.colors.accent} 
          />
        </TouchableOpacity>
        
        <Text style={styles.quantityText}>{quantity}</Text>
        
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(item.id, quantity + 1)}
        >
          <Ionicons name="add" size={16} color={ModernTheme.colors.accent} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderFavoriteItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={styles.favoriteItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
    >
      <View style={styles.itemContent}>
        <View style={styles.itemImage}>
          <Image
            source={{ uri: item.image_url || 'https://via.placeholder.com/80x80' }}
            style={styles.image}
            contentFit="cover"
          />
        </View>

        <View style={styles.itemInfo}>
          <View style={styles.itemHeader}>
            <Text style={styles.itemName} numberOfLines={2}>{item.name}</Text>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={() => toggleFavorite(item.id)}
            >
              <Ionicons
                name="heart"
                size={20}
                color="#FF6B6B"
              />
            </TouchableOpacity>
          </View>

          <Text style={styles.cafeteriaName}>
            {item.cafeteria?.name || 'Unknown Cafeteria'}
          </Text>

          {item.description && (
            <Text style={styles.itemDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          <View style={styles.itemFooter}>
            <Text style={styles.itemPrice}>
              {item.price?.toFixed(2)} EGP
            </Text>
            {renderQuantityControls(item)}
          </View>

          {!item.is_available && (
            <View style={styles.unavailableIndicator}>
              <Text style={styles.unavailableIndicatorText}>
                Currently unavailable
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={64} color={ModernTheme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>No favorites yet</Text>
      <Text style={styles.emptySubtitle}>
        Start adding items to your favorites by tapping the heart icon
      </Text>
      
      <TouchableOpacity
        style={styles.browseButton}
        onPress={() => navigation.navigate('Home')}
      >
        <LinearGradient
          colors={ModernTheme.gradients.accent}
          style={styles.browseButtonGradient}
        >
          <Text style={styles.browseButtonText}>Browse Menu</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  const renderLoginPrompt = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="person-outline" size={64} color={ModernTheme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>Login Required</Text>
      <Text style={styles.emptySubtitle}>
        Please login to view and manage your favorites
      </Text>
      
      <TouchableOpacity
        style={styles.browseButton}
        onPress={() => navigation.navigate('Login')}
      >
        <LinearGradient
          colors={ModernTheme.gradients.primary}
          style={styles.browseButtonGradient}
        >
          <Text style={styles.browseButtonText}>Login</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        {renderLoginPrompt()}
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.colors.accent} />
          <Text style={styles.loadingText}>Loading favorites...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (favoriteMenuItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        {renderEmptyState()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={loadFavorites}
            colors={[ModernTheme.colors.accent]}
            tintColor={ModernTheme.colors.accent}
          />
        }
      >
        <View style={styles.favoritesContainer}>
          {favoriteMenuItems.map(renderFavoriteItem)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  header: {
    backgroundColor: ModernTheme.colors.surface,
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.l,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  headerTitle: {
    ...ModernTheme.typography.headingMedium,
    marginBottom: 4,
  },
  headerSubtitle: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
  },
  scrollView: {
    flex: 1,
  },
  favoritesContainer: {
    padding: ModernTheme.spacing.m,
  },
  favoriteItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.radius.large,
    marginBottom: ModernTheme.spacing.m,
    ...ModernTheme.shadows.medium,
  },
  itemContent: {
    flexDirection: 'row',
    padding: ModernTheme.spacing.m,
  },
  itemImage: {
    marginRight: ModernTheme.spacing.m,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: ModernTheme.radius.medium,
  },
  itemInfo: {
    flex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemName: {
    ...ModernTheme.typography.headingSmall,
    flex: 1,
    marginRight: ModernTheme.spacing.s,
  },
  favoriteButton: {
    backgroundColor: '#FF6B6B15',
    borderRadius: ModernTheme.radius.small,
    padding: 8,
  },
  cafeteriaName: {
    ...ModernTheme.typography.bodySmall,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 4,
  },
  itemDescription: {
    ...ModernTheme.typography.bodySmall,
    color: ModernTheme.colors.textSecondary,
    marginBottom: ModernTheme.spacing.s,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    ...ModernTheme.typography.headingSmall,
    color: ModernTheme.colors.accent,
    fontWeight: '700',
  },
  addButton: {
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.soft,
  },
  addButtonGradient: {
    padding: 12,
    borderRadius: ModernTheme.radius.medium,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${ModernTheme.colors.accent}15`,
    borderRadius: ModernTheme.radius.medium,
    borderWidth: 1,
    borderColor: `${ModernTheme.colors.accent}30`,
  },
  quantityButton: {
    padding: 8,
  },
  quantityText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.accent,
    fontWeight: '600',
    paddingHorizontal: ModernTheme.spacing.s,
  },
  unavailableButton: {
    backgroundColor: ModernTheme.colors.surfaceVariant,
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.s,
    borderRadius: ModernTheme.radius.medium,
  },
  unavailableText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.textSecondary,
  },
  unavailableIndicator: {
    backgroundColor: '#FF444415',
    paddingHorizontal: ModernTheme.spacing.s,
    paddingVertical: 4,
    borderRadius: ModernTheme.radius.small,
    marginTop: ModernTheme.spacing.s,
    alignSelf: 'flex-start',
  },
  unavailableIndicatorText: {
    ...ModernTheme.typography.labelSmall,
    color: ModernTheme.colors.error,
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginTop: ModernTheme.spacing.m,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ModernTheme.spacing.l,
  },
  emptyTitle: {
    ...ModernTheme.typography.headingMedium,
    marginTop: ModernTheme.spacing.l,
    marginBottom: ModernTheme.spacing.s,
  },
  emptySubtitle: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: ModernTheme.spacing.xl,
  },
  browseButton: {
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.medium,
  },
  browseButtonGradient: {
    paddingHorizontal: ModernTheme.spacing.xl,
    paddingVertical: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
    alignItems: 'center',
  },
  browseButtonText: {
    ...ModernTheme.typography.labelLarge,
    color: 'white',
    fontWeight: '600',
  },
});

export default FavoritesScreen;
