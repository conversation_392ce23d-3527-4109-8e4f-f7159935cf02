import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { ModernTheme } from '../theme/ModernTheme';
import { useSupabase } from '../providers/SupabaseProvider';

const SearchScreen = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const { searchMenuItems } = useSupabase();

  const handleSearch = (query) => {
    setSearchQuery(query);
    if (query.trim()) {
      const results = searchMenuItems(query);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={ModernTheme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for food..."
            value={searchQuery}
            onChangeText={handleSearch}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch('')}>
              <Ionicons name="close" size={20} color={ModernTheme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.content}>
        {searchResults.length > 0 ? (
          <View style={styles.results}>
            <Text style={styles.resultsTitle}>
              {searchResults.length} results found
            </Text>
            {/* Add search results rendering here */}
          </View>
        ) : searchQuery.length > 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No results found</Text>
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>Start typing to search</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  header: {
    backgroundColor: ModernTheme.colors.surface,
    padding: ModernTheme.spacing.m,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surfaceVariant,
    borderRadius: ModernTheme.radius.medium,
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.s,
  },
  searchInput: {
    flex: 1,
    ...ModernTheme.typography.bodyMedium,
    marginLeft: ModernTheme.spacing.s,
  },
  content: {
    flex: 1,
  },
  results: {
    padding: ModernTheme.spacing.m,
  },
  resultsTitle: {
    ...ModernTheme.typography.headingSmall,
    marginBottom: ModernTheme.spacing.m,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
  },
});

export default SearchScreen;
