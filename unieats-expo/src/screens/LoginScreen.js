import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { ModernTheme } from '../theme/ModernTheme';
import { useAuth } from '../providers/AuthProvider';

const LoginScreen = ({ navigation }) => {
  const { signIn, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      const { data, error } = await signIn(email, password);
      
      if (error) {
        Alert.alert('Login Failed', error);
      } else if (data?.user) {
        // Navigation will be handled by auth state change
        navigation.replace('Main');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Welcome Back!</Text>
            <Text style={styles.subtitle}>Sign in to continue to UniEats</Text>
          </View>

          {/* Login Form */}
          <View style={styles.form}>
            {/* Email Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <View style={styles.inputWrapper}>
                <Ionicons name="mail-outline" size={20} color={ModernTheme.colors.textSecondary} />
                <TextInput
                  style={styles.textInput}
                  placeholder="Enter your email"
                  placeholderTextColor={ModernTheme.colors.textSecondary}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>

            {/* Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Password</Text>
              <View style={styles.inputWrapper}>
                <Ionicons name="lock-closed-outline" size={20} color={ModernTheme.colors.textSecondary} />
                <TextInput
                  style={styles.textInput}
                  placeholder="Enter your password"
                  placeholderTextColor={ModernTheme.colors.textSecondary}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeButton}
                >
                  <Ionicons
                    name={showPassword ? "eye-outline" : "eye-off-outline"}
                    size={20}
                    color={ModernTheme.colors.textSecondary}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Forgot Password */}
            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>

            {/* Login Button */}
            <TouchableOpacity
              style={styles.loginButton}
              onPress={handleLogin}
              disabled={loading}
            >
              <LinearGradient
                colors={ModernTheme.gradients.accent}
                style={styles.loginButtonGradient}
              >
                <Text style={styles.loginButtonText}>
                  {loading ? 'Signing In...' : 'Sign In'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            {/* Divider */}
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>

            {/* Guest Login */}
            <TouchableOpacity
              style={styles.guestButton}
              onPress={() => navigation.replace('Main')}
            >
              <Text style={styles.guestButtonText}>Continue as Guest</Text>
            </TouchableOpacity>
          </View>

          {/* Sign Up Link */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.signUpText}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: ModernTheme.spacing.l,
  },
  header: {
    alignItems: 'center',
    marginBottom: ModernTheme.spacing.xl,
  },
  title: {
    ...ModernTheme.typography.headingLarge,
    marginBottom: ModernTheme.spacing.s,
    textAlign: 'center',
  },
  subtitle: {
    ...ModernTheme.typography.bodyLarge,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
  },
  form: {
    marginBottom: ModernTheme.spacing.xl,
  },
  inputContainer: {
    marginBottom: ModernTheme.spacing.l,
  },
  inputLabel: {
    ...ModernTheme.typography.labelMedium,
    marginBottom: ModernTheme.spacing.s,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.radius.medium,
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.s,
    borderWidth: 1,
    borderColor: ModernTheme.colors.border,
    ...ModernTheme.shadows.soft,
  },
  textInput: {
    flex: 1,
    ...ModernTheme.typography.bodyMedium,
    marginLeft: ModernTheme.spacing.s,
    color: ModernTheme.colors.textPrimary,
  },
  eyeButton: {
    padding: 4,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: ModernTheme.spacing.l,
  },
  forgotPasswordText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.accent,
  },
  loginButton: {
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.medium,
    marginBottom: ModernTheme.spacing.l,
  },
  loginButtonGradient: {
    paddingVertical: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
    alignItems: 'center',
  },
  loginButtonText: {
    ...ModernTheme.typography.labelLarge,
    color: 'white',
    fontWeight: '600',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: ModernTheme.spacing.l,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: ModernTheme.colors.border,
  },
  dividerText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    paddingHorizontal: ModernTheme.spacing.m,
  },
  guestButton: {
    backgroundColor: ModernTheme.colors.surface,
    paddingVertical: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ModernTheme.colors.border,
    ...ModernTheme.shadows.soft,
  },
  guestButtonText: {
    ...ModernTheme.typography.labelLarge,
    color: ModernTheme.colors.textPrimary,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
  },
  signUpText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.accent,
    fontWeight: '600',
  },
});

export default LoginScreen;
