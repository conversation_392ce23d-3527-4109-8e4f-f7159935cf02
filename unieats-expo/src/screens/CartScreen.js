import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';

import { ModernTheme } from '../theme/ModernTheme';
import { useCart } from '../providers/CartProvider';
import { useAuth } from '../providers/AuthProvider';
import { useSupabase } from '../providers/SupabaseProvider';

const CartScreen = ({ navigation }) => {
  const { 
    items, 
    totalItems, 
    totalPrice, 
    isEmpty, 
    updateQuantity, 
    removeItem, 
    clearCart,
    getItemsByCafeteria 
  } = useCart();
  const { user } = useAuth();
  const { createOrder } = useSupabase();

  const handleCheckout = async () => {
    if (!user) {
      Alert.alert(
        'Login Required',
        'Please login to place an order',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => navigation.navigate('Login') }
        ]
      );
      return;
    }

    if (isEmpty) {
      Alert.alert('Empty Cart', 'Please add items to your cart first');
      return;
    }

    try {
      const cafeteriaGroups = getItemsByCafeteria();
      
      // For now, we'll create separate orders for each cafeteria
      for (const group of cafeteriaGroups) {
        const orderData = {
          userId: user.id,
          cafeteriaId: group.cafeteriaId,
          totalAmount: group.total,
          items: group.items.map(item => ({
            menuItemId: item.id,
            quantity: item.quantity,
            price: item.price,
            customizations: item.customizations || {},
            notes: item.notes || '',
          })),
          notes: '',
          deliveryAddress: '',
        };

        await createOrder(orderData);
      }

      Alert.alert(
        'Order Placed!',
        'Your order has been placed successfully',
        [
          { 
            text: 'OK', 
            onPress: () => {
              clearCart();
              navigation.navigate('OrderHistory');
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error placing order:', error);
      Alert.alert('Error', 'Failed to place order. Please try again.');
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color={ModernTheme.colors.textPrimary} />
      </TouchableOpacity>
      
      <Text style={styles.headerTitle}>Cart ({totalItems})</Text>
      
      {!isEmpty && (
        <TouchableOpacity
          style={styles.clearButton}
          onPress={() => {
            Alert.alert(
              'Clear Cart',
              'Are you sure you want to remove all items?',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Clear', style: 'destructive', onPress: clearCart }
              ]
            );
          }}
        >
          <Ionicons name="trash-outline" size={20} color={ModernTheme.colors.error} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderCartItem = (item) => (
    <View key={item.id} style={styles.cartItem}>
      <View style={styles.itemImage}>
        <Image
          source={{ uri: item.image || 'https://via.placeholder.com/60x60' }}
          style={styles.image}
          contentFit="cover"
        />
      </View>

      <View style={styles.itemDetails}>
        <Text style={styles.itemName} numberOfLines={2}>{item.name}</Text>
        <Text style={styles.cafeteriaName}>{item.cafeteriaName}</Text>
        
        {item.notes && (
          <Text style={styles.itemNotes} numberOfLines={1}>
            Note: {item.notes}
          </Text>
        )}

        <View style={styles.itemFooter}>
          <Text style={styles.itemPrice}>
            {(item.price * item.quantity).toFixed(2)} EGP
          </Text>
          
          <View style={styles.quantityControls}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => {
                if (item.quantity > 1) {
                  updateQuantity(item.id, item.quantity - 1);
                } else {
                  removeItem(item.id);
                }
              }}
            >
              <Ionicons 
                name={item.quantity > 1 ? "remove" : "trash-outline"} 
                size={16} 
                color={ModernTheme.colors.accent} 
              />
            </TouchableOpacity>
            
            <Text style={styles.quantityText}>{item.quantity}</Text>
            
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => updateQuantity(item.id, item.quantity + 1)}
            >
              <Ionicons name="add" size={16} color={ModernTheme.colors.accent} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );

  const renderEmptyCart = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="bag-outline" size={64} color={ModernTheme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>Your cart is empty</Text>
      <Text style={styles.emptySubtitle}>Add some delicious items to get started</Text>
      
      <TouchableOpacity
        style={styles.browseButton}
        onPress={() => navigation.navigate('Home')}
      >
        <LinearGradient
          colors={ModernTheme.gradients.accent}
          style={styles.browseButtonGradient}
        >
          <Text style={styles.browseButtonText}>Browse Cafeterias</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  const renderOrderSummary = () => (
    <View style={styles.orderSummary}>
      <Text style={styles.summaryTitle}>Order Summary</Text>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Subtotal ({totalItems} items)</Text>
        <Text style={styles.summaryValue}>{totalPrice.toFixed(2)} EGP</Text>
      </View>
      
      <View style={styles.summaryRow}>
        <Text style={styles.summaryLabel}>Delivery Fee</Text>
        <Text style={styles.summaryValue}>Free</Text>
      </View>
      
      <View style={styles.summaryDivider} />
      
      <View style={styles.summaryRow}>
        <Text style={styles.totalLabel}>Total</Text>
        <Text style={styles.totalValue}>{totalPrice.toFixed(2)} EGP</Text>
      </View>
    </View>
  );

  const renderCheckoutButton = () => (
    <View style={styles.checkoutContainer}>
      <TouchableOpacity
        style={styles.checkoutButton}
        onPress={handleCheckout}
        disabled={isEmpty}
      >
        <LinearGradient
          colors={isEmpty ? ['#ccc', '#ccc'] : ModernTheme.gradients.accent}
          style={styles.checkoutButtonGradient}
        >
          <Text style={styles.checkoutButtonText}>
            Place Order • {totalPrice.toFixed(2)} EGP
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  if (isEmpty) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        {renderEmptyCart()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.cartItems}>
          {items.map(renderCartItem)}
        </View>
        
        {renderOrderSummary()}
      </ScrollView>
      
      {renderCheckoutButton()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.m,
    backgroundColor: ModernTheme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  backButton: {
    padding: ModernTheme.spacing.s,
  },
  headerTitle: {
    ...ModernTheme.typography.headingMedium,
    flex: 1,
    textAlign: 'center',
  },
  clearButton: {
    padding: ModernTheme.spacing.s,
  },
  scrollView: {
    flex: 1,
  },
  cartItems: {
    padding: ModernTheme.spacing.m,
  },
  cartItem: {
    flexDirection: 'row',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.radius.large,
    padding: ModernTheme.spacing.m,
    marginBottom: ModernTheme.spacing.m,
    ...ModernTheme.shadows.medium,
  },
  itemImage: {
    marginRight: ModernTheme.spacing.m,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: ModernTheme.radius.medium,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    ...ModernTheme.typography.headingSmall,
    marginBottom: 4,
  },
  cafeteriaName: {
    ...ModernTheme.typography.bodySmall,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 4,
  },
  itemNotes: {
    ...ModernTheme.typography.bodySmall,
    color: ModernTheme.colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: ModernTheme.spacing.s,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    ...ModernTheme.typography.headingSmall,
    color: ModernTheme.colors.accent,
    fontWeight: '700',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${ModernTheme.colors.accent}15`,
    borderRadius: ModernTheme.radius.medium,
    borderWidth: 1,
    borderColor: `${ModernTheme.colors.accent}30`,
  },
  quantityButton: {
    padding: 8,
  },
  quantityText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.accent,
    fontWeight: '600',
    paddingHorizontal: ModernTheme.spacing.s,
  },
  orderSummary: {
    backgroundColor: ModernTheme.colors.surface,
    margin: ModernTheme.spacing.m,
    padding: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.large,
    ...ModernTheme.shadows.medium,
  },
  summaryTitle: {
    ...ModernTheme.typography.headingSmall,
    marginBottom: ModernTheme.spacing.m,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: ModernTheme.spacing.s,
  },
  summaryLabel: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
  },
  summaryValue: {
    ...ModernTheme.typography.labelMedium,
  },
  summaryDivider: {
    height: 1,
    backgroundColor: ModernTheme.colors.border,
    marginVertical: ModernTheme.spacing.s,
  },
  totalLabel: {
    ...ModernTheme.typography.headingSmall,
  },
  totalValue: {
    ...ModernTheme.typography.headingSmall,
    color: ModernTheme.colors.accent,
    fontWeight: '700',
  },
  checkoutContainer: {
    padding: ModernTheme.spacing.m,
    backgroundColor: ModernTheme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.colors.border,
  },
  checkoutButton: {
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.medium,
  },
  checkoutButtonGradient: {
    paddingVertical: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
    alignItems: 'center',
  },
  checkoutButtonText: {
    ...ModernTheme.typography.labelLarge,
    color: 'white',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ModernTheme.spacing.l,
  },
  emptyTitle: {
    ...ModernTheme.typography.headingMedium,
    marginTop: ModernTheme.spacing.l,
    marginBottom: ModernTheme.spacing.s,
  },
  emptySubtitle: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: ModernTheme.spacing.xl,
  },
  browseButton: {
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.medium,
  },
  browseButtonGradient: {
    paddingHorizontal: ModernTheme.spacing.xl,
    paddingVertical: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
    alignItems: 'center',
  },
  browseButtonText: {
    ...ModernTheme.typography.labelLarge,
    color: 'white',
    fontWeight: '600',
  },
});

export default CartScreen;
