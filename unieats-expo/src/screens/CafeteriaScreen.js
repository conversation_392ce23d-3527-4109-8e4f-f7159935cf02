import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';

import { ModernTheme } from '../theme/ModernTheme';
import { useSupabase } from '../providers/SupabaseProvider';
import { useCart } from '../providers/CartProvider';
import { useFavorites } from '../providers/FavoritesProvider';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const CafeteriaScreen = ({ navigation, route }) => {
  const { cafeteria } = route.params;
  const { getMenuItemsByCafeteria } = useSupabase();
  const { addItem, getQuantity, updateQuantity, removeItem } = useCart();
  const { isInFavorites, toggleFavorite } = useFavorites();
  
  const [menuItems, setMenuItems] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMenuItems();
  }, [cafeteria.id]);

  const loadMenuItems = async () => {
    try {
      setLoading(true);
      const items = getMenuItemsByCafeteria(cafeteria.id);
      setMenuItems(items);
      console.log(`Loaded ${items.length} menu items for cafeteria ${cafeteria.name}`);
    } catch (error) {
      console.error('Error loading menu items:', error);
    } finally {
      setLoading(false);
    }
  };

  const categories = ['All', ...new Set(menuItems.map(item => item.category).filter(Boolean))];
  
  const filteredItems = selectedCategory === 'All' 
    ? menuItems 
    : menuItems.filter(item => item.category === selectedCategory);

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: cafeteria.image_url || 'https://via.placeholder.com/400x200' }}
          style={styles.headerImage}
          contentFit="cover"
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.2)']}
          style={styles.headerOverlay}
        />
        
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <View style={styles.backButtonBackground}>
            <Ionicons name="arrow-back" size={24} color={ModernTheme.colors.textPrimary} />
          </View>
        </TouchableOpacity>
      </View>

      <View style={styles.cafeteriaInfo}>
        <View style={styles.cafeteriaHeader}>
          <View style={styles.cafeteriaDetails}>
            <Text style={styles.cafeteriaName}>{cafeteria.name}</Text>
            <View style={styles.locationContainer}>
              <Ionicons name="location-outline" size={16} color={ModernTheme.colors.textSecondary} />
              <Text style={styles.location}>{cafeteria.location}</Text>
            </View>
          </View>
          
          <View style={[
            styles.statusBadge,
            { backgroundColor: cafeteria.is_open ? ModernTheme.colors.success : ModernTheme.colors.error }
          ]}>
            <Text style={styles.statusText}>
              {cafeteria.is_open ? 'Open' : 'Closed'}
            </Text>
          </View>
        </View>

        {cafeteria.description && (
          <Text style={styles.description}>{cafeteria.description}</Text>
        )}

        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={styles.infoText}>
              {cafeteria.average_rating ? cafeteria.average_rating.toFixed(1) : '4.5'}
            </Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="time-outline" size={16} color={ModernTheme.colors.textSecondary} />
            <Text style={styles.infoText}>15-30 min</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="restaurant-outline" size={16} color={ModernTheme.colors.textSecondary} />
            <Text style={styles.infoText}>{menuItems.length} items</Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderCategories = () => (
    <View style={styles.categoriesContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryChip,
              selectedCategory === category && styles.categoryChipActive
            ]}
            onPress={() => setSelectedCategory(category)}
          >
            <Text style={[
              styles.categoryText,
              selectedCategory === category && styles.categoryTextActive
            ]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderQuantityControls = (item) => {
    const quantity = getQuantity(item.id);
    const isAvailable = item.is_available;

    if (!isAvailable) {
      return (
        <View style={styles.unavailableButton}>
          <Text style={styles.unavailableText}>Unavailable</Text>
        </View>
      );
    }

    if (quantity === 0) {
      return (
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => addItem(item, 1)}
        >
          <LinearGradient
            colors={ModernTheme.gradients.accent}
            style={styles.addButtonGradient}
          >
            <Ionicons name="add" size={20} color="white" />
          </LinearGradient>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.quantityControls}>
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => {
            if (quantity > 1) {
              updateQuantity(item.id, quantity - 1);
            } else {
              removeItem(item.id);
            }
          }}
        >
          <Ionicons 
            name={quantity > 1 ? "remove" : "trash-outline"} 
            size={16} 
            color={ModernTheme.colors.accent} 
          />
        </TouchableOpacity>
        
        <Text style={styles.quantityText}>{quantity}</Text>
        
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(item.id, quantity + 1)}
        >
          <Ionicons name="add" size={16} color={ModernTheme.colors.accent} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderMenuItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={styles.menuItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
    >
      <View style={styles.menuItemContent}>
        <View style={styles.menuItemImage}>
          <Image
            source={{ uri: item.image_url || 'https://via.placeholder.com/80x80' }}
            style={styles.itemImage}
            contentFit="cover"
          />
        </View>

        <View style={styles.menuItemInfo}>
          <View style={styles.menuItemHeader}>
            <Text style={styles.itemName} numberOfLines={2}>{item.name}</Text>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={() => toggleFavorite(item.id)}
            >
              <Ionicons
                name={isInFavorites(item.id) ? "heart" : "heart-outline"}
                size={20}
                color={isInFavorites(item.id) ? "#FF6B6B" : ModernTheme.colors.textSecondary}
              />
            </TouchableOpacity>
          </View>

          {item.description && (
            <Text style={styles.itemDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          <View style={styles.menuItemFooter}>
            <Text style={styles.itemPrice}>
              {item.price?.toFixed(2)} EGP
            </Text>
            {renderQuantityControls(item)}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMenuItems = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.colors.accent} />
          <Text style={styles.loadingText}>Loading menu items...</Text>
        </View>
      );
    }

    if (filteredItems.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="restaurant-outline" size={48} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyText}>
            {selectedCategory === 'All' ? 'No menu items available' : `No items in ${selectedCategory}`}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.menuItemsContainer}>
        {filteredItems.map(renderMenuItem)}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderHeader()}
        {renderCategories()}
        {renderMenuItems()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: ModernTheme.colors.surface,
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  headerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
  },
  backButton: {
    position: 'absolute',
    top: ModernTheme.spacing.m,
    left: ModernTheme.spacing.m,
  },
  backButtonBackground: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: ModernTheme.spacing.s,
    ...ModernTheme.shadows.soft,
  },
  cafeteriaInfo: {
    padding: ModernTheme.spacing.m,
  },
  cafeteriaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: ModernTheme.spacing.s,
  },
  cafeteriaDetails: {
    flex: 1,
  },
  cafeteriaName: {
    ...ModernTheme.typography.headingMedium,
    marginBottom: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  location: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginLeft: 4,
  },
  statusBadge: {
    paddingHorizontal: ModernTheme.spacing.s,
    paddingVertical: 4,
    borderRadius: ModernTheme.radius.small,
  },
  statusText: {
    ...ModernTheme.typography.labelSmall,
    color: 'white',
    fontSize: 12,
  },
  description: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginBottom: ModernTheme.spacing.m,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    ...ModernTheme.typography.labelMedium,
    marginLeft: 4,
  },
  categoriesContainer: {
    backgroundColor: ModernTheme.colors.surface,
    paddingVertical: ModernTheme.spacing.m,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  categoriesContent: {
    paddingHorizontal: ModernTheme.spacing.m,
  },
  categoryChip: {
    backgroundColor: ModernTheme.colors.surfaceVariant,
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.s,
    borderRadius: ModernTheme.radius.large,
    marginRight: ModernTheme.spacing.s,
  },
  categoryChipActive: {
    backgroundColor: ModernTheme.colors.accent,
  },
  categoryText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.textSecondary,
  },
  categoryTextActive: {
    color: 'white',
  },
  menuItemsContainer: {
    padding: ModernTheme.spacing.m,
  },
  menuItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.radius.large,
    marginBottom: ModernTheme.spacing.m,
    ...ModernTheme.shadows.medium,
  },
  menuItemContent: {
    flexDirection: 'row',
    padding: ModernTheme.spacing.m,
  },
  menuItemImage: {
    marginRight: ModernTheme.spacing.m,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: ModernTheme.radius.medium,
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemName: {
    ...ModernTheme.typography.headingSmall,
    flex: 1,
    marginRight: ModernTheme.spacing.s,
  },
  favoriteButton: {
    padding: 4,
  },
  itemDescription: {
    ...ModernTheme.typography.bodySmall,
    color: ModernTheme.colors.textSecondary,
    marginBottom: ModernTheme.spacing.s,
  },
  menuItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    ...ModernTheme.typography.headingSmall,
    color: ModernTheme.colors.accent,
    fontWeight: '700',
  },
  addButton: {
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.soft,
  },
  addButtonGradient: {
    padding: 12,
    borderRadius: ModernTheme.radius.medium,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${ModernTheme.colors.accent}15`,
    borderRadius: ModernTheme.radius.medium,
    borderWidth: 1,
    borderColor: `${ModernTheme.colors.accent}30`,
  },
  quantityButton: {
    padding: 8,
  },
  quantityText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.accent,
    fontWeight: '600',
    paddingHorizontal: ModernTheme.spacing.s,
  },
  unavailableButton: {
    backgroundColor: ModernTheme.colors.surfaceVariant,
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.s,
    borderRadius: ModernTheme.radius.medium,
  },
  unavailableText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.textSecondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ModernTheme.spacing.xl,
  },
  loadingText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginTop: ModernTheme.spacing.m,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ModernTheme.spacing.xl,
  },
  emptyText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginTop: ModernTheme.spacing.m,
    textAlign: 'center',
  },
});

export default CafeteriaScreen;
