import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';

import { ModernTheme } from '../theme/ModernTheme';
import { useSupabase } from '../providers/SupabaseProvider';
import { useAuth } from '../providers/AuthProvider';
import { useCart } from '../providers/CartProvider';

const { width: screenWidth } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const { cafeterias, loading, error, loadCafeterias } = useSupabase();
  const { user, profile } = useAuth();
  const { totalItems } = useCart();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCafeterias();
    setRefreshing(false);
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerContent}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>
            Hello, {profile?.full_name || user?.email?.split('@')[0] || 'Guest'}! 👋
          </Text>
          <Text style={styles.subtitle}>What would you like to eat today?</Text>
        </View>
        
        <TouchableOpacity
          style={styles.cartButton}
          onPress={() => navigation.navigate('Cart')}
        >
          <Ionicons name="bag-outline" size={24} color={ModernTheme.colors.textPrimary} />
          {totalItems > 0 && (
            <View style={styles.cartBadge}>
              <Text style={styles.cartBadgeText}>{totalItems}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('Search')}
      >
        <LinearGradient
          colors={ModernTheme.gradients.primary}
          style={styles.quickActionGradient}
        >
          <Ionicons name="search" size={24} color="white" />
        </LinearGradient>
        <Text style={styles.quickActionText}>Search</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('Favorites')}
      >
        <LinearGradient
          colors={ModernTheme.gradients.accent}
          style={styles.quickActionGradient}
        >
          <Ionicons name="heart" size={24} color="white" />
        </LinearGradient>
        <Text style={styles.quickActionText}>Favorites</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('OrderHistory')}
      >
        <LinearGradient
          colors={ModernTheme.gradients.secondary}
          style={styles.quickActionGradient}
        >
          <Ionicons name="time" size={24} color="white" />
        </LinearGradient>
        <Text style={styles.quickActionText}>Orders</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCafeteriaCard = (cafeteria) => (
    <TouchableOpacity
      key={cafeteria.id}
      style={styles.cafeteriaCard}
      onPress={() => navigation.navigate('Cafeteria', { cafeteria })}
    >
      <View style={styles.cafeteriaImageContainer}>
        <Image
          source={{ uri: cafeteria.image_url || 'https://via.placeholder.com/300x200' }}
          style={styles.cafeteriaImage}
          contentFit="cover"
          transition={200}
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.3)']}
          style={styles.cafeteriaImageOverlay}
        />
        
        {/* Status indicators */}
        <View style={styles.statusContainer}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: cafeteria.is_open ? ModernTheme.colors.success : ModernTheme.colors.error }
          ]}>
            <Text style={styles.statusText}>
              {cafeteria.is_open ? 'Open' : 'Closed'}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.cafeteriaInfo}>
        <Text style={styles.cafeteriaName}>{cafeteria.name}</Text>
        <View style={styles.cafeteriaDetails}>
          <Ionicons name="location-outline" size={14} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.cafeteriaLocation}>{cafeteria.location}</Text>
        </View>
        
        {cafeteria.description && (
          <Text style={styles.cafeteriaDescription} numberOfLines={2}>
            {cafeteria.description}
          </Text>
        )}

        <View style={styles.cafeteriaFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color="#FFD700" />
            <Text style={styles.ratingText}>
              {cafeteria.average_rating ? cafeteria.average_rating.toFixed(1) : '4.5'}
            </Text>
          </View>
          
          <View style={styles.deliveryInfo}>
            <Ionicons name="time-outline" size={14} color={ModernTheme.colors.textSecondary} />
            <Text style={styles.deliveryText}>15-30 min</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCafeterias = () => {
    if (loading && cafeterias.length === 0) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.colors.accent} />
          <Text style={styles.loadingText}>Loading cafeterias...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color={ModernTheme.colors.error} />
          <Text style={styles.errorText}>Failed to load cafeterias</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadCafeterias}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (cafeterias.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="restaurant-outline" size={48} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyText}>No cafeterias available</Text>
        </View>
      );
    }

    return (
      <View style={styles.cafeteriasSection}>
        <Text style={styles.sectionTitle}>Available Cafeterias</Text>
        <View style={styles.cafeteriasGrid}>
          {cafeterias.map(renderCafeteriaCard)}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ModernTheme.colors.accent]}
            tintColor={ModernTheme.colors.accent}
          />
        }
      >
        {renderQuickActions()}
        {renderCafeterias()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  header: {
    backgroundColor: ModernTheme.colors.surface,
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.l,
    ...ModernTheme.shadows.soft,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    ...ModernTheme.typography.headingMedium,
    marginBottom: 4,
  },
  subtitle: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
  },
  cartButton: {
    position: 'relative',
    padding: ModernTheme.spacing.s,
  },
  cartBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: ModernTheme.colors.accent,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    ...ModernTheme.typography.labelSmall,
    color: 'white',
    fontSize: 10,
  },
  scrollView: {
    flex: 1,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: ModernTheme.spacing.m,
    paddingVertical: ModernTheme.spacing.l,
  },
  quickActionButton: {
    alignItems: 'center',
  },
  quickActionGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: ModernTheme.spacing.s,
    ...ModernTheme.shadows.medium,
  },
  quickActionText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.textSecondary,
  },
  cafeteriasSection: {
    paddingHorizontal: ModernTheme.spacing.m,
    paddingBottom: ModernTheme.spacing.xl,
  },
  sectionTitle: {
    ...ModernTheme.typography.headingMedium,
    marginBottom: ModernTheme.spacing.m,
  },
  cafeteriasGrid: {
    gap: ModernTheme.spacing.m,
  },
  cafeteriaCard: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.radius.large,
    overflow: 'hidden',
    ...ModernTheme.shadows.medium,
  },
  cafeteriaImageContainer: {
    position: 'relative',
    height: 160,
  },
  cafeteriaImage: {
    width: '100%',
    height: '100%',
  },
  cafeteriaImageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  statusContainer: {
    position: 'absolute',
    top: ModernTheme.spacing.s,
    right: ModernTheme.spacing.s,
  },
  statusBadge: {
    paddingHorizontal: ModernTheme.spacing.s,
    paddingVertical: 4,
    borderRadius: ModernTheme.radius.small,
  },
  statusText: {
    ...ModernTheme.typography.labelSmall,
    color: 'white',
    fontSize: 10,
  },
  cafeteriaInfo: {
    padding: ModernTheme.spacing.m,
  },
  cafeteriaName: {
    ...ModernTheme.typography.headingSmall,
    marginBottom: 4,
  },
  cafeteriaDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: ModernTheme.spacing.s,
  },
  cafeteriaLocation: {
    ...ModernTheme.typography.bodySmall,
    marginLeft: 4,
  },
  cafeteriaDescription: {
    ...ModernTheme.typography.bodySmall,
    marginBottom: ModernTheme.spacing.s,
  },
  cafeteriaFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    ...ModernTheme.typography.labelMedium,
    marginLeft: 4,
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryText: {
    ...ModernTheme.typography.bodySmall,
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ModernTheme.spacing.xl,
  },
  loadingText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginTop: ModernTheme.spacing.m,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ModernTheme.spacing.xl,
  },
  errorText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.error,
    marginTop: ModernTheme.spacing.m,
    marginBottom: ModernTheme.spacing.m,
  },
  retryButton: {
    backgroundColor: ModernTheme.colors.accent,
    paddingHorizontal: ModernTheme.spacing.l,
    paddingVertical: ModernTheme.spacing.s,
    borderRadius: ModernTheme.radius.medium,
  },
  retryButtonText: {
    ...ModernTheme.typography.labelMedium,
    color: 'white',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ModernTheme.spacing.xl,
  },
  emptyText: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
    marginTop: ModernTheme.spacing.m,
  },
});

export default HomeScreen;
