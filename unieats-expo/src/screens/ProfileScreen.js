import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ModernTheme } from '../theme/ModernTheme';
import { useAuth } from '../providers/AuthProvider';

const ProfileScreen = ({ navigation }) => {
  const { user, profile, signOut } = useAuth();

  const menuItems = [
    {
      icon: 'person-outline',
      title: 'Account Settings',
      onPress: () => {},
    },
    {
      icon: 'time-outline',
      title: 'Order History',
      onPress: () => navigation.navigate('OrderHistory'),
    },
    {
      icon: 'heart-outline',
      title: 'Favorites',
      onPress: () => navigation.navigate('Favorites'),
    },
    {
      icon: 'help-circle-outline',
      title: 'Help & Support',
      onPress: () => {},
    },
    {
      icon: 'information-circle-outline',
      title: 'About Us',
      onPress: () => {},
    },
  ];

  const renderMenuItem = (item, index) => (
    <TouchableOpacity
      key={index}
      style={styles.menuItem}
      onPress={item.onPress}
    >
      <View style={styles.menuItemIcon}>
        <Ionicons name={item.icon} size={20} color={ModernTheme.colors.accent} />
      </View>
      <Text style={styles.menuItemText}>{item.title}</Text>
      <Ionicons name="chevron-forward" size={16} color={ModernTheme.colors.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>
              {profile?.full_name || user?.email?.split('@')[0] || 'Guest'}
            </Text>
            <Text style={styles.profileEmail}>
              {user?.email || 'Not logged in'}
            </Text>
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>Menu</Text>
          {menuItems.map(renderMenuItem)}
        </View>

        {/* Logout Button */}
        {user && (
          <TouchableOpacity style={styles.logoutButton} onPress={signOut}>
            <Text style={styles.logoutText}>Sign Out</Text>
          </TouchableOpacity>
        )}

        {/* Login Button for guests */}
        {!user && (
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate('Login')}
          >
            <LinearGradient
              colors={ModernTheme.gradients.accent}
              style={styles.loginButtonGradient}
            >
              <Text style={styles.loginButtonText}>Sign In</Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    backgroundColor: ModernTheme.colors.surface,
    padding: ModernTheme.spacing.l,
    alignItems: 'center',
  },
  profileInfo: {
    alignItems: 'center',
  },
  profileName: {
    ...ModernTheme.typography.headingMedium,
    marginBottom: 4,
  },
  profileEmail: {
    ...ModernTheme.typography.bodyMedium,
    color: ModernTheme.colors.textSecondary,
  },
  menuSection: {
    backgroundColor: ModernTheme.colors.surface,
    margin: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.large,
    padding: ModernTheme.spacing.m,
    ...ModernTheme.shadows.medium,
  },
  sectionTitle: {
    ...ModernTheme.typography.headingSmall,
    marginBottom: ModernTheme.spacing.m,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: ModernTheme.spacing.m,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  menuItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${ModernTheme.colors.accent}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: ModernTheme.spacing.m,
  },
  menuItemText: {
    ...ModernTheme.typography.bodyMedium,
    flex: 1,
  },
  logoutButton: {
    margin: ModernTheme.spacing.m,
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.radius.medium,
    padding: ModernTheme.spacing.m,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ModernTheme.colors.error,
  },
  logoutText: {
    ...ModernTheme.typography.labelMedium,
    color: ModernTheme.colors.error,
  },
  loginButton: {
    margin: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
    ...ModernTheme.shadows.medium,
  },
  loginButtonGradient: {
    padding: ModernTheme.spacing.m,
    borderRadius: ModernTheme.radius.medium,
    alignItems: 'center',
  },
  loginButtonText: {
    ...ModernTheme.typography.labelLarge,
    color: 'white',
    fontWeight: '600',
  },
});

export default ProfileScreen;
