import { Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const ModernTheme = {
  // Colors - matching Flutter theme
  colors: {
    // Primary colors
    primary: '#2563EB',
    primaryLight: '#3B82F6',
    primaryDark: '#1D4ED8',
    
    // Accent colors (Orange theme)
    accent: '#FF6B35',
    accentLight: '#FF8A65',
    accentDark: '#E55722',
    
    // Secondary colors
    secondary: '#10B981',
    secondaryLight: '#34D399',
    secondaryDark: '#059669',
    
    // Surface colors
    background: '#F8FAFC',
    surface: '#FFFFFF',
    surfaceVariant: '#F1F5F9',
    
    // Text colors
    textPrimary: '#0F172A',
    textSecondary: '#64748B',
    textTertiary: '#94A3B8',
    
    // Status colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    
    // Border colors
    border: '#E2E8F0',
    borderLight: '#F1F5F9',
    
    // Glass effect
    glassBorder: 'rgba(255, 255, 255, 0.2)',
    glassBackground: 'rgba(255, 255, 255, 0.1)',
  },

  // Gradients
  gradients: {
    primary: ['#3B82F6', '#2563EB'],
    accent: ['#FF8A65', '#FF6B35'],
    secondary: ['#34D399', '#10B981'],
    glass: ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)'],
  },

  // Spacing
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
    xxl: 48,
  },

  // Border radius
  radius: {
    small: 8,
    medium: 12,
    large: 16,
    xl: 24,
  },

  // Typography
  typography: {
    // Headings
    headingLarge: {
      fontSize: 32,
      fontWeight: '700',
      lineHeight: 40,
      fontFamily: 'System',
      color: '#0F172A',
    },
    headingMedium: {
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 32,
      fontFamily: 'System',
      color: '#0F172A',
    },
    headingSmall: {
      fontSize: 20,
      fontWeight: '600',
      lineHeight: 28,
      fontFamily: 'System',
      color: '#0F172A',
    },

    // Body text
    bodyLarge: {
      fontSize: 18,
      fontWeight: '400',
      lineHeight: 28,
      fontFamily: 'System',
      color: '#0F172A',
    },
    bodyMedium: {
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 24,
      fontFamily: 'System',
      color: '#0F172A',
    },
    bodySmall: {
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 20,
      fontFamily: 'System',
      color: '#64748B',
    },

    // Labels
    labelLarge: {
      fontSize: 16,
      fontWeight: '500',
      lineHeight: 24,
      fontFamily: 'System',
      color: '#0F172A',
    },
    labelMedium: {
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 20,
      fontFamily: 'System',
      color: '#0F172A',
    },
    labelSmall: {
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 16,
      fontFamily: 'System',
      color: '#64748B',
    },
  },

  // Shadows
  shadows: {
    light: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.02,
      shadowRadius: 4,
      elevation: 1,
    },
    soft: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.04,
      shadowRadius: 8,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.08,
      shadowRadius: 16,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.12,
      shadowRadius: 24,
      elevation: 8,
    },
  },

  // Screen dimensions
  dimensions: {
    screenWidth,
    screenHeight,
    isSmallScreen: screenWidth < 375,
    isMediumScreen: screenWidth >= 375 && screenWidth < 414,
    isLargeScreen: screenWidth >= 414,
  },

  // Animation durations
  animations: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
};
