import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const CartContext = createContext({});

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

// Cart reducer
const cartReducer = (state, action) => {
  switch (action.type) {
    case 'LOAD_CART':
      return {
        ...state,
        items: action.payload || [],
      };

    case 'ADD_ITEM': {
      const { item } = action.payload;
      const existingItemIndex = state.items.findIndex(
        cartItem => cartItem.id === item.id
      );

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const updatedItems = [...state.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + item.quantity,
        };
        return {
          ...state,
          items: updatedItems,
        };
      } else {
        // Add new item
        return {
          ...state,
          items: [...state.items, item],
        };
      }
    }

    case 'UPDATE_QUANTITY': {
      const { itemId, quantity } = action.payload;
      if (quantity <= 0) {
        return {
          ...state,
          items: state.items.filter(item => item.id !== itemId),
        };
      }
      
      return {
        ...state,
        items: state.items.map(item =>
          item.id === itemId ? { ...item, quantity } : item
        ),
      };
    }

    case 'REMOVE_ITEM':
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.payload.itemId),
      };

    case 'CLEAR_CART':
      return {
        ...state,
        items: [],
      };

    default:
      return state;
  }
};

const initialState = {
  items: [],
};

export const CartProvider = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Load cart from storage on app start
  useEffect(() => {
    loadCartFromStorage();
  }, []);

  // Save cart to storage whenever it changes
  useEffect(() => {
    saveCartToStorage();
  }, [state.items]);

  // Load cart from AsyncStorage
  const loadCartFromStorage = async () => {
    try {
      const cartData = await AsyncStorage.getItem('cart');
      if (cartData) {
        const parsedCart = JSON.parse(cartData);
        dispatch({ type: 'LOAD_CART', payload: parsedCart });
      }
    } catch (error) {
      console.error('Error loading cart from storage:', error);
    }
  };

  // Save cart to AsyncStorage
  const saveCartToStorage = async () => {
    try {
      await AsyncStorage.setItem('cart', JSON.stringify(state.items));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  };

  // Add item to cart
  const addItem = (menuItem, quantity = 1, customizations = {}, notes = '') => {
    const cartItem = {
      id: menuItem.id,
      name: menuItem.name,
      price: menuItem.price,
      image: menuItem.image_url,
      quantity,
      customizations,
      notes,
      cafeteriaId: menuItem.cafeteria_id,
      cafeteriaName: menuItem.cafeteria?.name || 'Unknown Cafeteria',
      isAvailable: menuItem.is_available,
    };

    dispatch({
      type: 'ADD_ITEM',
      payload: { item: cartItem },
    });
  };

  // Update item quantity
  const updateQuantity = (itemId, quantity) => {
    dispatch({
      type: 'UPDATE_QUANTITY',
      payload: { itemId, quantity },
    });
  };

  // Remove item from cart
  const removeItem = (itemId) => {
    dispatch({
      type: 'REMOVE_ITEM',
      payload: { itemId },
    });
  };

  // Clear entire cart
  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  // Get item quantity
  const getQuantity = (itemId) => {
    const item = state.items.find(item => item.id === itemId);
    return item ? item.quantity : 0;
  };

  // Calculate totals
  const getTotalItems = () => {
    return state.items.reduce((total, item) => total + item.quantity, 0);
  };

  const getTotalPrice = () => {
    return state.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  // Get items by cafeteria
  const getItemsByCafeteria = () => {
    const cafeteriaGroups = {};
    state.items.forEach(item => {
      const cafeteriaId = item.cafeteriaId;
      if (!cafeteriaGroups[cafeteriaId]) {
        cafeteriaGroups[cafeteriaId] = {
          cafeteriaId,
          cafeteriaName: item.cafeteriaName,
          items: [],
          total: 0,
        };
      }
      cafeteriaGroups[cafeteriaId].items.push(item);
      cafeteriaGroups[cafeteriaId].total += item.price * item.quantity;
    });
    return Object.values(cafeteriaGroups);
  };

  // Check if cart has items from multiple cafeterias
  const hasMultipleCafeterias = () => {
    const cafeteriaIds = new Set(state.items.map(item => item.cafeteriaId));
    return cafeteriaIds.size > 1;
  };

  // Get cart summary for order
  const getOrderSummary = () => {
    return {
      items: state.items.map(item => ({
        menuItemId: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        customizations: item.customizations,
        notes: item.notes,
      })),
      totalItems: getTotalItems(),
      totalPrice: getTotalPrice(),
      cafeterias: getItemsByCafeteria(),
    };
  };

  const value = {
    // State
    items: state.items,
    totalItems: getTotalItems(),
    totalPrice: getTotalPrice(),
    isEmpty: state.items.length === 0,
    
    // Methods
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    getQuantity,
    getTotalItems,
    getTotalPrice,
    getItemsByCafeteria,
    hasMultipleCafeterias,
    getOrderSummary,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
