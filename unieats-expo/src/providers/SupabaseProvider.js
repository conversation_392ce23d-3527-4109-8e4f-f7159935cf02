import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase, TABLES, supabaseHelpers } from '../config/supabase';

const SupabaseContext = createContext({});

export const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (!context) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
};

export const SupabaseProvider = ({ children }) => {
  const [cafeterias, setCafeterias] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load cafeterias
  const loadCafeterias = async () => {
    try {
      console.log('Loading cafeterias...');
      const { data, error } = await supabase
        .from(TABLES.CAFETERIAS)
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      console.log(`Loaded ${data?.length || 0} cafeterias`);
      setCafeterias(data || []);
      return data || [];
    } catch (error) {
      console.error('Error loading cafeterias:', error);
      setError(supabaseHelpers.formatError(error));
      return [];
    }
  };

  // Load menu items
  const loadMenuItems = async () => {
    try {
      console.log('Loading menu items...');
      const { data, error } = await supabase
        .from(TABLES.MENU_ITEMS)
        .select(`
          *,
          cafeteria:cafeteria_id (
            id,
            name,
            location
          )
        `)
        .order('name');

      if (error) throw error;

      console.log(`Loaded ${data?.length || 0} menu items`);
      setMenuItems(data || []);
      return data || [];
    } catch (error) {
      console.error('Error loading menu items:', error);
      setError(supabaseHelpers.formatError(error));
      return [];
    }
  };

  // Get menu items for a specific cafeteria
  const getMenuItemsByCafeteria = (cafeteriaId) => {
    return menuItems.filter(item => item.cafeteria_id === cafeteriaId);
  };

  // Get cafeteria by ID
  const getCafeteriaById = (id) => {
    return cafeterias.find(cafeteria => cafeteria.id === id);
  };

  // Get menu item by ID
  const getMenuItemById = (id) => {
    return menuItems.find(item => item.id === id);
  };

  // Search menu items
  const searchMenuItems = (query) => {
    if (!query || query.trim() === '') return [];
    
    const searchTerm = query.toLowerCase().trim();
    return menuItems.filter(item => 
      item.name?.toLowerCase().includes(searchTerm) ||
      item.description?.toLowerCase().includes(searchTerm) ||
      item.cafeteria?.name?.toLowerCase().includes(searchTerm)
    );
  };

  // Create order
  const createOrder = async (orderData) => {
    try {
      console.log('Creating order:', orderData);
      
      // Create the order
      const { data: order, error: orderError } = await supabase
        .from(TABLES.ORDERS)
        .insert([{
          user_id: orderData.userId,
          cafeteria_id: orderData.cafeteriaId,
          total_amount: orderData.totalAmount,
          status: 'pending',
          notes: orderData.notes,
          delivery_address: orderData.deliveryAddress,
        }])
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        menu_item_id: item.menuItemId,
        quantity: item.quantity,
        price: item.price,
        customizations: item.customizations,
        notes: item.notes,
      }));

      const { error: itemsError } = await supabase
        .from(TABLES.ORDER_ITEMS)
        .insert(orderItems);

      if (itemsError) throw itemsError;

      console.log('Order created successfully:', order.id);
      return order;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  };

  // Get user orders
  const getUserOrders = async (userId) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.ORDERS)
        .select(`
          *,
          cafeteria:cafeteria_id (
            id,
            name,
            location
          ),
          order_items (
            *,
            menu_item:menu_item_id (
              id,
              name,
              image_url
            )
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error loading user orders:', error);
      throw error;
    }
  };

  // Initialize data
  const initialize = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await Promise.all([
        loadCafeterias(),
        loadMenuItems(),
      ]);
    } catch (error) {
      console.error('Error initializing Supabase provider:', error);
      setError(supabaseHelpers.formatError(error));
    } finally {
      setLoading(false);
    }
  };

  // Set up real-time subscriptions
  useEffect(() => {
    // Subscribe to cafeterias changes
    const cafeteriasSubscription = supabase
      .channel('cafeterias_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: TABLES.CAFETERIAS },
        (payload) => {
          console.log('Cafeterias change received:', payload);
          loadCafeterias();
        }
      )
      .subscribe();

    // Subscribe to menu items changes
    const menuItemsSubscription = supabase
      .channel('menu_items_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: TABLES.MENU_ITEMS },
        (payload) => {
          console.log('Menu items change received:', payload);
          loadMenuItems();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(cafeteriasSubscription);
      supabase.removeChannel(menuItemsSubscription);
    };
  }, []);

  // Initialize on mount
  useEffect(() => {
    initialize();
  }, []);

  const value = {
    // Data
    cafeterias,
    menuItems,
    loading,
    error,
    
    // Methods
    loadCafeterias,
    loadMenuItems,
    getMenuItemsByCafeteria,
    getCafeteriaById,
    getMenuItemById,
    searchMenuItems,
    createOrder,
    getUserOrders,
    initialize,
  };

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  );
};
