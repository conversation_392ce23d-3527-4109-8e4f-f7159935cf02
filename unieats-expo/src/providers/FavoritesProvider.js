import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, TABLES, supabaseHelpers } from '../config/supabase';
import { useAuth } from './AuthProvider';

const FavoritesContext = createContext({});

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
};

export const FavoritesProvider = ({ children }) => {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load user favorites
  const loadFavorites = async () => {
    if (!user?.id) {
      setFavorites([]);
      return;
    }

    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from(TABLES.FAVORITES)
        .select(`
          *,
          menu_item:menu_item_id (
            *,
            cafeteria:cafeteria_id (
              id,
              name,
              location
            )
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;

      console.log(`Loaded ${data?.length || 0} favorites for user ${user.id}`);
      setFavorites(data || []);
    } catch (error) {
      console.error('Error loading favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  // Add item to favorites
  const addToFavorites = async (menuItemId) => {
    if (!user?.id) {
      throw new Error('User must be logged in to add favorites');
    }

    try {
      // Check if already in favorites
      const existingFavorite = favorites.find(
        fav => fav.menu_item_id === menuItemId
      );

      if (existingFavorite) {
        console.log('Item already in favorites');
        return;
      }

      const { data, error } = await supabase
        .from(TABLES.FAVORITES)
        .insert([{
          user_id: user.id,
          menu_item_id: menuItemId,
          created_at: new Date().toISOString(),
        }])
        .select(`
          *,
          menu_item:menu_item_id (
            *,
            cafeteria:cafeteria_id (
              id,
              name,
              location
            )
          )
        `)
        .single();

      if (error) throw error;

      setFavorites(prev => [...prev, data]);
      console.log('Added to favorites:', menuItemId);
    } catch (error) {
      console.error('Error adding to favorites:', error);
      throw error;
    }
  };

  // Remove item from favorites
  const removeFromFavorites = async (menuItemId) => {
    if (!user?.id) {
      throw new Error('User must be logged in to remove favorites');
    }

    try {
      const { error } = await supabase
        .from(TABLES.FAVORITES)
        .delete()
        .eq('user_id', user.id)
        .eq('menu_item_id', menuItemId);

      if (error) throw error;

      setFavorites(prev => 
        prev.filter(fav => fav.menu_item_id !== menuItemId)
      );
      console.log('Removed from favorites:', menuItemId);
    } catch (error) {
      console.error('Error removing from favorites:', error);
      throw error;
    }
  };

  // Toggle favorite status
  const toggleFavorite = async (menuItemId) => {
    const isFavorite = isInFavorites(menuItemId);
    
    if (isFavorite) {
      await removeFromFavorites(menuItemId);
    } else {
      await addToFavorites(menuItemId);
    }
  };

  // Check if item is in favorites
  const isInFavorites = (menuItemId) => {
    return favorites.some(fav => fav.menu_item_id === menuItemId);
  };

  // Get favorite menu items
  const getFavoriteMenuItems = () => {
    return favorites
      .map(fav => fav.menu_item)
      .filter(item => item !== null);
  };

  // Get favorites count
  const getFavoritesCount = () => {
    return favorites.length;
  };

  // Clear all favorites (for logout)
  const clearFavorites = () => {
    setFavorites([]);
  };

  // Load favorites when user changes
  useEffect(() => {
    if (user?.id) {
      loadFavorites();
    } else {
      clearFavorites();
    }
  }, [user?.id]);

  // Set up real-time subscription for favorites
  useEffect(() => {
    if (!user?.id) return;

    const subscription = supabase
      .channel('user_favorites')
      .on('postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: TABLES.FAVORITES,
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          console.log('Favorites change received:', payload);
          loadFavorites();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [user?.id]);

  const value = {
    // State
    favorites,
    loading,
    favoritesCount: getFavoritesCount(),
    
    // Methods
    loadFavorites,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isInFavorites,
    getFavoriteMenuItems,
    getFavoritesCount,
    clearFavorites,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};
