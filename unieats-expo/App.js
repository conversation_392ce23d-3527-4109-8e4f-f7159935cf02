import React, { useState, useEffect, createContext, useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image, FlatList, RefreshControl, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { createClient } from '@supabase/supabase-js';

// Supabase Configuration (from your original app)
const SUPABASE_URL = 'https://lqtnaxvqkoynaziiinqh.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Modern Theme (matching your original app)
const ModernTheme = {
  colors: {
    primary: '#FF6B35',
    primaryDark: '#E55A2B',
    primaryLight: '#FFE5DC',
    secondary: '#4ECDC4',
    background: '#F8F9FA',
    surface: '#FFFFFF',
    text: '#2C3E50',
    textSecondary: '#7F8C8D',
    border: '#E9ECEF',
    warning: '#F39C12',
    success: '#27AE60',
    error: '#E74C3C',
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
  },
};

// Context for Supabase data
const SupabaseContext = createContext();
const AuthContext = createContext();
const CartContext = createContext();
const FavoritesContext = createContext();

// Supabase Provider
const SupabaseProvider = ({ children }) => {
  const [cafeterias, setCafeterias] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadCafeterias = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('cafeterias')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setCafeterias(data || []);
      console.log('Loaded cafeterias:', data?.length);
    } catch (error) {
      console.error('Error loading cafeterias:', error);
      Alert.alert('Error', 'Failed to load cafeterias');
    } finally {
      setLoading(false);
    }
  };

  const loadMenuItems = async (cafeteriaId) => {
    try {
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .eq('cafeteria_id', cafeteriaId)
        .eq('is_available', true)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error loading menu items:', error);
      return [];
    }
  };

  useEffect(() => {
    loadCafeterias();
  }, []);

  const value = {
    cafeterias,
    menuItems,
    loading,
    loadCafeterias,
    loadMenuItems,
    supabase,
  };

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  );
};

// Auth Provider
const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Simple auth for demo (you can enhance this)
  const login = (userData) => {
    setCurrentUser(userData);
    setIsAuthenticated(true);
  };

  const logout = () => {
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const value = {
    currentUser,
    isAuthenticated,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Cart Provider
const CartProvider = ({ children }) => {
  const [items, setItems] = useState([]);

  const addToCart = (item) => {
    setItems(prev => {
      const existingItem = prev.find(i => i.id === item.id);
      if (existingItem) {
        return prev.map(i =>
          i.id === item.id
            ? { ...i, quantity: i.quantity + 1 }
            : i
        );
      }
      return [...prev, { ...item, quantity: 1 }];
    });
  };

  const removeFromCart = (itemId) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    setItems(prev =>
      prev.map(item =>
        item.id === itemId
          ? { ...item, quantity }
          : item
      )
    );
  };

  const clearCart = () => {
    setItems([]);
  };

  const getTotalPrice = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const value = {
    items,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getTotalPrice,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

// Favorites Provider
const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);

  const addToFavorites = (item) => {
    setFavorites(prev => {
      if (prev.find(fav => fav.id === item.id)) {
        return prev;
      }
      return [...prev, item];
    });
  };

  const removeFromFavorites = (itemId) => {
    setFavorites(prev => prev.filter(item => item.id !== itemId));
  };

  const isFavorite = (itemId) => {
    return favorites.some(item => item.id === itemId);
  };

  const value = {
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};

// Custom hooks
const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (!context) {
    throw new Error('useSupabase must be used within SupabaseProvider');
  }
  return context;
};

const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within CartProvider');
  }
  return context;
};

const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within FavoritesProvider');
  }
  return context;
};

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Sample data
const sampleCafeterias = [
  { id: 1, name: 'Campus Café', image: '🏪', rating: 4.5, deliveryTime: '15-20 min' },
  { id: 2, name: 'Student Bistro', image: '🍽️', rating: 4.3, deliveryTime: '20-25 min' },
  { id: 3, name: 'Quick Bites', image: '🍔', rating: 4.7, deliveryTime: '10-15 min' },
  { id: 4, name: 'Healthy Corner', image: '🥗', rating: 4.4, deliveryTime: '15-20 min' },
];

const sampleCategories = [
  { id: 1, name: 'Pizza', icon: '🍕' },
  { id: 2, name: 'Burgers', icon: '🍔' },
  { id: 3, name: 'Salads', icon: '🥗' },
  { id: 4, name: 'Drinks', icon: '🥤' },
];

// Dashboard Screen (Home) - Exact replica of your Flutter app
function DashboardScreen({ navigation }) {
  const { cafeterias, loading, loadCafeterias } = useSupabase();
  const { currentUser } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCafeterias();
    setRefreshing(false);
  };

  const userName = currentUser?.fullName || 'there';

  const renderCafeteria = ({ item }) => (
    <TouchableOpacity
      style={styles.cafeteriaCard}
      onPress={() => navigation.navigate('Cafeteria', {
        cafeteriaId: item.id,
        cafeteriaName: item.name,
        cafeteria: item
      })}
    >
      <View style={styles.cafeteriaImageContainer}>
        <View style={styles.cafeteriaImage}>
          <Text style={styles.cafeteriaEmoji}>🏪</Text>
        </View>
      </View>
      <View style={styles.cafeteriaInfo}>
        <Text style={styles.cafeteriaName}>{item.name}</Text>
        <Text style={styles.cafeteriaDescription} numberOfLines={2}>
          {item.description || 'Delicious food awaits you'}
        </Text>
        <View style={styles.cafeteriaDetails}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={ModernTheme.colors.warning} />
            <Text style={styles.ratingText}>4.5</Text>
          </View>
          <Text style={styles.deliveryTime}>15-20 min</Text>
        </View>
      </View>
      <View style={styles.cafeteriaStatus}>
        <View style={[styles.statusDot, { backgroundColor: item.is_active ? ModernTheme.colors.success : ModernTheme.colors.error }]} />
        <Text style={styles.statusText}>{item.is_active ? 'Open' : 'Closed'}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.greeting}>Good Morning!</Text>
            <Text style={styles.headerTitle}>What would you like to eat, {userName}?</Text>
          </View>
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => navigation.navigate('Profile')}
          >
            <Ionicons name="person-circle" size={40} color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ModernTheme.colors.primary]}
            tintColor={ModernTheme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Search Bar */}
        <TouchableOpacity
          style={styles.searchBar}
          onPress={() => navigation.navigate('Search')}
        >
          <Ionicons name="search" size={20} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.searchPlaceholder}>Search for food, cafeterias...</Text>
        </TouchableOpacity>

        {/* Cafeterias Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Cafeterias</Text>
            <TouchableOpacity onPress={() => navigation.navigate('CafeteriaList')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <Text>Loading cafeterias...</Text>
            </View>
          ) : cafeterias.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No cafeterias available</Text>
            </View>
          ) : (
            <FlatList
              data={cafeterias}
              renderItem={renderCafeteria}
              keyExtractor={(item) => item.id.toString()}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.cafeteriasList}
            />
          )}
        </View>

        {/* Reorder Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Reorder</Text>
            <TouchableOpacity onPress={() => navigation.navigate('OrderHistory')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No previous orders yet. Order something to see your favorites here!</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

// Cafeteria Screen - Exact replica of your Flutter app
function CafeteriaScreen({ route, navigation }) {
  const { cafeteriaId, cafeteriaName, cafeteria } = route.params;
  const { loadMenuItems } = useSupabase();
  const { addToCart } = useCart();
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMenu();
  }, [cafeteriaId]);

  const loadMenu = async () => {
    try {
      setLoading(true);
      const items = await loadMenuItems(cafeteriaId);
      setMenuItems(items);
    } catch (error) {
      console.error('Error loading menu:', error);
      Alert.alert('Error', 'Failed to load menu items');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMenu();
    setRefreshing(false);
  };

  const handleAddToCart = (item) => {
    addToCart(item);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`);
  };

  const renderMenuItem = ({ item }) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
    >
      <View style={styles.menuItemImageContainer}>
        <View style={styles.menuItemImage}>
          <Text style={styles.menuItemEmoji}>🍽️</Text>
        </View>
      </View>
      <View style={styles.menuItemInfo}>
        <Text style={styles.menuItemName}>{item.name}</Text>
        <Text style={styles.menuItemDescription} numberOfLines={2}>
          {item.description || 'Delicious food item'}
        </Text>
        <View style={styles.menuItemFooter}>
          <Text style={styles.menuItemPrice}>{item.price.toFixed(2)} EGP</Text>
          <View style={styles.availabilityContainer}>
            <View style={[styles.statusDot, {
              backgroundColor: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
            }]} />
            <Text style={styles.availabilityText}>
              {item.is_available ? 'Available' : 'Unavailable'}
            </Text>
          </View>
        </View>
      </View>
      <TouchableOpacity
        style={[styles.addButton, { opacity: item.is_available ? 1 : 0.5 }]}
        onPress={() => item.is_available && handleAddToCart(item)}
        disabled={!item.is_available}
      >
        <Ionicons name="add" size={20} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{cafeteriaName}</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Search')}>
            <Ionicons name="search" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Cafeteria Info */}
      <View style={styles.cafeteriaInfoHeader}>
        <Text style={styles.cafeteriaInfoName}>{cafeteriaName}</Text>
        <Text style={styles.cafeteriaInfoDescription}>
          {cafeteria?.description || 'Delicious food awaits you'}
        </Text>
        <View style={styles.cafeteriaInfoDetails}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={ModernTheme.colors.warning} />
            <Text style={styles.ratingText}>4.5</Text>
          </View>
          <Text style={styles.deliveryTime}>15-20 min</Text>
          <View style={[styles.statusDot, {
            backgroundColor: cafeteria?.is_active ? ModernTheme.colors.success : ModernTheme.colors.error
          }]} />
          <Text style={styles.statusText}>
            {cafeteria?.is_active ? 'Open' : 'Closed'}
          </Text>
        </View>
      </View>

      {/* Menu Items */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <Text>Loading menu...</Text>
        </View>
      ) : menuItems.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No menu items available</Text>
        </View>
      ) : (
        <FlatList
          data={menuItems}
          renderItem={renderMenuItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.menuList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[ModernTheme.colors.primary]}
              tintColor={ModernTheme.colors.primary}
            />
          }
        />
      )}
    </View>
  );
}

// Item Details Screen - Exact replica of your Flutter app
function ItemDetailsScreen({ route, navigation }) {
  const { item } = route.params;
  const { addToCart } = useCart();
  const { favorites, addToFavorites, removeFromFavorites, isFavorite } = useFavorites();
  const [quantity, setQuantity] = useState(1);

  const handleAddToCart = () => {
    for (let i = 0; i < quantity; i++) {
      addToCart(item);
    }
    Alert.alert('Added to Cart', `${quantity} x ${item.name} added to cart`);
    navigation.goBack();
  };

  const handleFavoriteToggle = () => {
    if (isFavorite(item.id)) {
      removeFromFavorites(item.id);
    } else {
      addToFavorites(item);
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Item Details</Text>
          <TouchableOpacity onPress={handleFavoriteToggle}>
            <Ionicons
              name={isFavorite(item.id) ? "heart" : "heart-outline"}
              size={24}
              color="white"
            />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.itemDetailsContainer}>
          {/* Item Image */}
          <View style={styles.itemImageLarge}>
            <Text style={styles.itemEmojiLarge}>🍽️</Text>
          </View>

          {/* Item Info */}
          <Text style={styles.itemNameLarge}>{item.name}</Text>
          <Text style={styles.itemDescriptionLarge}>
            {item.description || 'Delicious food item'}
          </Text>

          {/* Price and Availability */}
          <View style={styles.itemPriceContainer}>
            <Text style={styles.itemPriceLarge}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
            <View style={styles.availabilityContainer}>
              <View style={[styles.statusDot, {
                backgroundColor: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
              }]} />
              <Text style={styles.availabilityText}>
                {item.is_available ? 'Available' : 'Unavailable'}
              </Text>
            </View>
          </View>

          {/* Quantity Selector */}
          <View style={styles.quantityContainer}>
            <Text style={styles.quantityLabel}>Quantity</Text>
            <View style={styles.quantitySelector}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(Math.max(1, quantity - 1))}
              >
                <Ionicons name="remove" size={20} color={ModernTheme.colors.primary} />
              </TouchableOpacity>
              <Text style={styles.quantityText}>{quantity}</Text>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => setQuantity(quantity + 1)}
              >
                <Ionicons name="add" size={20} color={ModernTheme.colors.primary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={[styles.addToCartButton, { opacity: item.is_available ? 1 : 0.5 }]}
          onPress={handleAddToCart}
          disabled={!item.is_available}
        >
          <Text style={styles.addToCartText}>
            Add to Cart - {(item.price * quantity).toFixed(2)} EGP
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

// Cart Screen - Exact replica of your Flutter app
function CartScreen({ navigation }) {
  const { items, removeFromCart, updateQuantity, clearCart, getTotalPrice } = useCart();

  const renderCartItem = ({ item }) => (
    <View style={styles.cartItem}>
      <View style={styles.cartItemImage}>
        <Text style={styles.cartItemEmoji}>🍽️</Text>
      </View>
      <View style={styles.cartItemInfo}>
        <Text style={styles.cartItemName}>{item.name}</Text>
        <Text style={styles.cartItemPrice}>{item.price.toFixed(2)} EGP</Text>
      </View>
      <View style={styles.cartItemControls}>
        <TouchableOpacity
          style={styles.cartQuantityButton}
          onPress={() => updateQuantity(item.id, item.quantity - 1)}
        >
          <Ionicons name="remove" size={16} color={ModernTheme.colors.primary} />
        </TouchableOpacity>
        <Text style={styles.cartQuantityText}>{item.quantity}</Text>
        <TouchableOpacity
          style={styles.cartQuantityButton}
          onPress={() => updateQuantity(item.id, item.quantity + 1)}
        >
          <Ionicons name="add" size={16} color={ModernTheme.colors.primary} />
        </TouchableOpacity>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => removeFromCart(item.id)}
      >
        <Ionicons name="trash" size={20} color={ModernTheme.colors.error} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Cart</Text>
          {items.length > 0 && (
            <TouchableOpacity onPress={clearCart}>
              <Text style={styles.clearCartText}>Clear All</Text>
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>

      {items.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="bag-outline" size={80} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyTitle}>Your cart is empty</Text>
          <Text style={styles.emptyText}>Add some delicious items to get started</Text>
          <TouchableOpacity
            style={styles.browseButton}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.browseButtonText}>Browse Cafeterias</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <FlatList
            data={items}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.cartList}
            showsVerticalScrollIndicator={false}
          />

          {/* Total and Checkout */}
          <View style={styles.cartFooter}>
            <View style={styles.totalContainer}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalPrice}>{getTotalPrice().toFixed(2)} EGP</Text>
            </View>
            <TouchableOpacity style={styles.checkoutButton}>
              <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );
}

// Favorites Screen - Exact replica of your Flutter app
function FavoritesScreen({ navigation }) {
  const { favorites, removeFromFavorites } = useFavorites();
  const { addToCart } = useCart();

  const handleAddToCart = (item) => {
    addToCart(item);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`);
  };

  const renderFavoriteItem = ({ item }) => (
    <View style={styles.favoriteItem}>
      <TouchableOpacity
        style={styles.favoriteItemContent}
        onPress={() => navigation.navigate('ItemDetails', { item })}
      >
        <View style={styles.favoriteItemImage}>
          <Text style={styles.favoriteItemEmoji}>🍽️</Text>
        </View>
        <View style={styles.favoriteItemInfo}>
          <Text style={styles.favoriteItemName}>{item.name}</Text>
          <Text style={styles.favoriteItemDescription} numberOfLines={2}>
            {item.description || 'Delicious food item'}
          </Text>
          <Text style={styles.favoriteItemPrice}>{item.price.toFixed(2)} EGP</Text>
        </View>
      </TouchableOpacity>
      <View style={styles.favoriteItemActions}>
        <TouchableOpacity
          style={styles.favoriteAddButton}
          onPress={() => handleAddToCart(item)}
        >
          <Ionicons name="add" size={20} color="white" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.favoriteRemoveButton}
          onPress={() => removeFromFavorites(item.id)}
        >
          <Ionicons name="heart" size={20} color={ModernTheme.colors.error} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Favorites</Text>
        </View>
      </LinearGradient>

      {favorites.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart-outline" size={80} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyTitle}>No favorites yet</Text>
          <Text style={styles.emptyText}>Add items to your favorites to see them here</Text>
          <TouchableOpacity
            style={styles.browseButton}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={styles.browseButtonText}>Browse Cafeterias</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={favorites}
          renderItem={renderFavoriteItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.favoritesList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

// Search Screen
function SearchScreen() {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Search</Text>
        </View>
      </LinearGradient>
      <View style={styles.emptyContainer}>
        <Ionicons name="search-outline" size={80} color={ModernTheme.colors.textSecondary} />
        <Text style={styles.emptyTitle}>Search for food</Text>
        <Text style={styles.emptyText}>Find your favorite dishes and restaurants</Text>
      </View>
    </View>
  );
}

// Profile Screen
function ProfileScreen() {
  const { currentUser, logout } = useAuth();

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Profile</Text>
        </View>
      </LinearGradient>
      <View style={styles.profileContainer}>
        <View style={styles.profileHeader}>
          <View style={styles.profileAvatar}>
            <Ionicons name="person" size={40} color={ModernTheme.colors.primary} />
          </View>
          <Text style={styles.profileName}>{currentUser?.fullName || 'Guest User'}</Text>
          <Text style={styles.profileEmail}>{currentUser?.email || '<EMAIL>'}</Text>
        </View>

        <View style={styles.profileOptions}>
          <TouchableOpacity style={styles.profileOption}>
            <Ionicons name="person-outline" size={24} color={ModernTheme.colors.text} />
            <Text style={styles.profileOptionText}>Account Settings</Text>
            <Ionicons name="chevron-forward" size={20} color={ModernTheme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.profileOption}>
            <Ionicons name="time-outline" size={24} color={ModernTheme.colors.text} />
            <Text style={styles.profileOptionText}>Order History</Text>
            <Ionicons name="chevron-forward" size={20} color={ModernTheme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.profileOption}>
            <Ionicons name="help-circle-outline" size={24} color={ModernTheme.colors.text} />
            <Text style={styles.profileOptionText}>Help & Support</Text>
            <Ionicons name="chevron-forward" size={20} color={ModernTheme.colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.profileOption}>
            <Ionicons name="information-circle-outline" size={24} color={ModernTheme.colors.text} />
            <Text style={styles.profileOptionText}>About Us</Text>
            <Ionicons name="chevron-forward" size={20} color={ModernTheme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

// Main Tab Navigator - Exact replica of your Flutter app
function MainTabNavigator() {
  const { items } = useCart();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'Cart') {
            iconName = focused ? 'bag' : 'bag-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          // Cart badge
          if (route.name === 'Cart' && items.length > 0) {
            return (
              <View style={styles.tabIconContainer}>
                <Ionicons name={iconName} size={size} color={color} />
                <View style={styles.cartBadge}>
                  <Text style={styles.cartBadgeText}>
                    {items.length > 99 ? '99+' : items.length}
                  </Text>
                </View>
              </View>
            );
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: ModernTheme.colors.primary,
        tabBarInactiveTintColor: ModernTheme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: ModernTheme.colors.surface,
          borderTopWidth: 0,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          height: 65,
          paddingBottom: 10,
          paddingTop: 8,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        },
        headerShown: false,
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
      })}
    >
      <Tab.Screen name="Home" component={DashboardScreen} options={{ title: 'Home' }} />
      <Tab.Screen name="Favorites" component={FavoritesScreen} options={{ title: 'Favorites' }} />
      <Tab.Screen name="Cart" component={CartScreen} options={{ title: 'Cart' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
}

// Main Stack Navigator
function AppNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: ModernTheme.colors.background },
        gestureEnabled: true,
        gestureDirection: 'horizontal',
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      <Stack.Screen name="Cafeteria" component={CafeteriaScreen} />
      <Stack.Screen name="ItemDetails" component={ItemDetailsScreen} />
      <Stack.Screen name="Search" component={SearchScreen} />
    </Stack.Navigator>
  );
}

// Main App Component
export default function App() {
  // Initialize demo user
  useEffect(() => {
    console.log('UniEats App Starting...');
    console.log('Supabase URL:', SUPABASE_URL);
  }, []);

  return (
    <SafeAreaProvider>
      <SupabaseProvider>
        <AuthProvider>
          <CartProvider>
            <FavoritesProvider>
              <NavigationContainer>
                <AppNavigator />
                <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />
              </NavigationContainer>
            </FavoritesProvider>
          </CartProvider>
        </AuthProvider>
      </SupabaseProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  // Base styles
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },

  // Header styles
  modernHeader: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
    marginBottom: 4,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  profileButton: {
    padding: 4,
  },
  clearCartText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Content styles
  content: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },

  // Search bar
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginHorizontal: 20,
    marginTop: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchPlaceholder: {
    marginLeft: 10,
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
  },

  // Section styles
  section: {
    marginVertical: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
  },
  seeAllText: {
    fontSize: 16,
    color: ModernTheme.colors.primary,
    fontWeight: '600',
  },

  // Cafeteria styles
  cafeteriasList: {
    paddingHorizontal: 20,
  },
  cafeteriaCard: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cafeteriaImageContainer: {
    marginRight: 15,
  },
  cafeteriaImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cafeteriaEmoji: {
    fontSize: 30,
  },
  cafeteriaInfo: {
    flex: 1,
  },
  cafeteriaName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  cafeteriaDescription: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 8,
  },
  cafeteriaDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  ratingText: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginLeft: 4,
  },
  deliveryTime: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginRight: 15,
  },
  cafeteriaStatus: {
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    color: ModernTheme.colors.textSecondary,
  },

  // Cafeteria info header
  cafeteriaInfoHeader: {
    backgroundColor: ModernTheme.colors.surface,
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  cafeteriaInfoName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 8,
  },
  cafeteriaInfoDescription: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 12,
  },
  cafeteriaInfoDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  // Menu item styles
  menuList: {
    padding: 20,
  },
  menuItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuItemImageContainer: {
    marginRight: 15,
  },
  menuItemImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuItemEmoji: {
    fontSize: 30,
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  menuItemDescription: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 8,
  },
  menuItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },
  availabilityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  availabilityText: {
    fontSize: 12,
    color: ModernTheme.colors.textSecondary,
    marginLeft: 4,
  },
  addButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Item details styles
  itemDetailsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  itemImageLarge: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  itemEmojiLarge: {
    fontSize: 80,
  },
  itemNameLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 10,
    textAlign: 'center',
  },
  itemDescriptionLarge: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  itemPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 30,
  },
  itemPriceLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },

  // Quantity selector
  quantityContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  quantityLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 15,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginHorizontal: 20,
  },

  // Bottom container
  bottomContainer: {
    padding: 20,
    backgroundColor: ModernTheme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.colors.border,
  },
  addToCartButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  addToCartText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Tab navigation
  tabIconContainer: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: ModernTheme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },

  // Loading and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginTop: 20,
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  browseButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 25,
    paddingHorizontal: 30,
    paddingVertical: 15,
  },
  browseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },

  // Cart styles
  cartList: {
    padding: 20,
  },
  cartItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cartItemImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  cartItemEmoji: {
    fontSize: 24,
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  cartItemPrice: {
    fontSize: 14,
    color: ModernTheme.colors.primary,
    fontWeight: '600',
  },
  cartItemControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  cartQuantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartQuantityText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginHorizontal: 15,
  },
  removeButton: {
    padding: 5,
  },
  cartFooter: {
    backgroundColor: ModernTheme.colors.surface,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.colors.border,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
  },
  totalPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },
  checkoutButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  checkoutButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Favorites styles
  favoritesList: {
    padding: 20,
  },
  favoriteItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  favoriteItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  favoriteItemImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  favoriteItemEmoji: {
    fontSize: 24,
  },
  favoriteItemInfo: {
    flex: 1,
  },
  favoriteItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  favoriteItemDescription: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 4,
  },
  favoriteItemPrice: {
    fontSize: 14,
    color: ModernTheme.colors.primary,
    fontWeight: '600',
  },
  favoriteItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  favoriteAddButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  favoriteRemoveButton: {
    padding: 5,
  },

  // Profile styles
  profileContainer: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  profileHeader: {
    backgroundColor: ModernTheme.colors.surface,
    padding: 30,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  profileAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 5,
  },
  profileEmail: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
  },
  profileOptions: {
    padding: 20,
  },
  profileOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  profileOptionText: {
    flex: 1,
    fontSize: 16,
    color: ModernTheme.colors.text,
    marginLeft: 15,
    fontWeight: '500',
  },
});
