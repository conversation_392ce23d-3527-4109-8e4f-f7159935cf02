import React, { useState, useEffect, createContext, useContext } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  FlatList, 
  RefreshControl, 
  Alert,
  TextInput,
  Dimensions,
  ActivityIndicator,
  SafeAreaView,
  Image
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Supabase Configuration (exact from your Flutter app)
const SUPABASE_URL = 'https://lqtnaxvqkoynaziiinqh.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const { width, height } = Dimensions.get('window');

// Modern Theme (exact match to your Flutter app)
const ModernTheme = {
  primaryColor: '#FF6B35',
  primaryDark: '#E55A2B',
  primaryLight: '#FFE5DC',
  secondaryColor: '#4ECDC4',
  backgroundColor: '#F8F9FA',
  surfaceColor: '#FFFFFF',
  textColor: '#2C3E50',
  textSecondary: '#7F8C8D',
  borderColor: '#E9ECEF',
  warningColor: '#F39C12',
  successColor: '#27AE60',
  errorColor: '#E74C3C',
  cardShadow: 'rgba(0, 0, 0, 0.1)',
  spaceXS: 4,
  spaceS: 8,
  spaceM: 16,
  spaceL: 24,
  spaceXL: 32,
  borderRadiusS: 8,
  borderRadiusM: 12,
  borderRadiusL: 16,
  borderRadiusXL: 20,
  elevationLow: 2,
  elevationMedium: 4,
  elevationHigh: 8,
};

// Context for providers
const SupabaseContext = createContext();
const AuthContext = createContext();
const CartContext = createContext();
const FavoritesContext = createContext();
const OrderContext = createContext();

// Supabase Provider with full functionality
const SupabaseProvider = ({ children }) => {
  const [cafeterias, setCafeterias] = useState([]);
  const [menuItems, setMenuItems] = useState({});
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState(null);

  const loadCafeterias = async () => {
    try {
      console.log('🔄 Loading cafeterias from Supabase...');
      setLoading(true);
      
      const { data, error } = await supabase
        .from('cafeterias')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) {
        console.error('❌ Supabase error:', error);
        throw error;
      }
      
      setCafeterias(data || []);
      console.log('✅ Loaded cafeterias:', data?.length);
      return data || [];
    } catch (error) {
      console.error('❌ Error loading cafeterias:', error);
      Alert.alert('Connection Error', 'Failed to load cafeterias. Please check your internet connection.');
      return [];
    } finally {
      setLoading(false);
    }
  };

  const loadMenuItems = async (cafeteriaId) => {
    try {
      console.log('🔄 Loading menu items for cafeteria:', cafeteriaId);
      
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .eq('cafeteria_id', cafeteriaId)
        .order('name');

      if (error) {
        console.error('❌ Menu items error:', error);
        throw error;
      }
      
      const items = data || [];
      setMenuItems(prev => ({ ...prev, [cafeteriaId]: items }));
      console.log('✅ Loaded menu items:', items.length);
      return items;
    } catch (error) {
      console.error('❌ Error loading menu items:', error);
      Alert.alert('Error', 'Failed to load menu items');
      return [];
    }
  };

  const searchMenuItems = async (query) => {
    try {
      const { data, error } = await supabase
        .from('menu_items')
        .select(`
          *,
          cafeterias (
            id,
            name,
            description
          )
        `)
        .ilike('name', `%${query}%`)
        .eq('is_available', true)
        .limit(20);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Error searching menu items:', error);
      return [];
    }
  };

  const createOrder = async (orderData) => {
    try {
      const { data, error } = await supabase
        .from('orders')
        .insert([orderData])
        .select();

      if (error) throw error;
      return data[0];
    } catch (error) {
      console.error('❌ Error creating order:', error);
      throw error;
    }
  };

  useEffect(() => {
    loadCafeterias();
    // Set demo user
    setCurrentUser({
      id: 'demo-user-123',
      fullName: 'Demo User',
      email: '<EMAIL>',
      phone: '+20 ************'
    });
  }, []);

  const value = {
    cafeterias,
    menuItems,
    loading,
    currentUser,
    loadCafeterias,
    loadMenuItems,
    searchMenuItems,
    createOrder,
    supabase,
  };

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  );
};

// Auth Provider
const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(true); // Demo mode

  const login = (userData) => {
    setIsAuthenticated(true);
  };

  const logout = () => {
    setIsAuthenticated(false);
  };

  const value = {
    isAuthenticated,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Cart Provider with full persistence
const CartProvider = ({ children }) => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCart();
  }, []);

  useEffect(() => {
    if (!loading) {
      saveCart();
    }
  }, [items, loading]);

  const loadCart = async () => {
    try {
      const savedCart = await AsyncStorage.getItem('unieats_cart');
      if (savedCart) {
        setItems(JSON.parse(savedCart));
      }
    } catch (error) {
      console.error('❌ Error loading cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveCart = async () => {
    try {
      await AsyncStorage.setItem('unieats_cart', JSON.stringify(items));
    } catch (error) {
      console.error('❌ Error saving cart:', error);
    }
  };

  const addToCart = (item, quantity = 1) => {
    setItems(prev => {
      const existingItem = prev.find(i => i.id === item.id);
      if (existingItem) {
        return prev.map(i => 
          i.id === item.id 
            ? { ...i, quantity: i.quantity + quantity }
            : i
        );
      }
      return [...prev, { ...item, quantity }];
    });
  };

  const removeFromCart = (itemId) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    setItems(prev => 
      prev.map(item => 
        item.id === itemId 
          ? { ...item, quantity }
          : item
      )
    );
  };

  const clearCart = () => {
    setItems([]);
  };

  const getTotalPrice = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0);
  };

  const value = {
    items,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getTotalPrice,
    getTotalItems,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

// Favorites Provider with persistence
const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadFavorites();
  }, []);

  useEffect(() => {
    if (!loading) {
      saveFavorites();
    }
  }, [favorites, loading]);

  const loadFavorites = async () => {
    try {
      const savedFavorites = await AsyncStorage.getItem('unieats_favorites');
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites));
      }
    } catch (error) {
      console.error('❌ Error loading favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveFavorites = async () => {
    try {
      await AsyncStorage.setItem('unieats_favorites', JSON.stringify(favorites));
    } catch (error) {
      console.error('❌ Error saving favorites:', error);
    }
  };

  const addToFavorites = (item) => {
    setFavorites(prev => {
      if (prev.find(fav => fav.id === item.id)) {
        return prev;
      }
      return [...prev, item];
    });
  };

  const removeFromFavorites = (itemId) => {
    setFavorites(prev => prev.filter(item => item.id !== itemId));
  };

  const isFavorite = (itemId) => {
    return favorites.some(item => item.id === itemId);
  };

  const toggleFavorite = (item) => {
    if (isFavorite(item.id)) {
      removeFromFavorites(item.id);
      return false;
    } else {
      addToFavorites(item);
      return true;
    }
  };

  const value = {
    favorites,
    loading,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};

// Order Provider
const OrderProvider = ({ children }) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);

  const createOrder = async (orderData) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('orders')
        .insert([orderData])
        .select();

      if (error) throw error;

      setOrders(prev => [data[0], ...prev]);
      return data[0];
    } catch (error) {
      console.error('❌ Error creating order:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loadOrders = async (userId) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setOrders(data || []);
    } catch (error) {
      console.error('❌ Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const value = {
    orders,
    loading,
    createOrder,
    loadOrders,
  };

  return (
    <OrderContext.Provider value={value}>
      {children}
    </OrderContext.Provider>
  );
};

// Custom hooks
const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (!context) {
    throw new Error('useSupabase must be used within SupabaseProvider');
  }
  return context;
};

const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within CartProvider');
  }
  return context;
};

const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within FavoritesProvider');
  }
  return context;
};

const useOrders = () => {
  const context = useContext(OrderContext);
  if (!context) {
    throw new Error('useOrders must be used within OrderProvider');
  }
  return context;
};

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Dashboard Screen - Exact replica of your Flutter app
function DashboardScreen({ navigation }) {
  const { cafeterias, loading, loadCafeterias, currentUser } = useSupabase();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCafeterias();
    setRefreshing(false);
  };

  const userName = currentUser?.fullName?.split(' ')[0] || 'there';

  const renderCafeteria = ({ item, index }) => (
    <TouchableOpacity
      style={[styles.cafeteriaCard, { marginRight: index === cafeterias.length - 1 ? 20 : 15 }]}
      onPress={() => navigation.navigate('Cafeteria', {
        cafeteriaId: item.id,
        cafeteriaName: item.name,
        cafeteria: item
      })}
      activeOpacity={0.8}
    >
      <View style={styles.cafeteriaImageContainer}>
        <LinearGradient
          colors={[ModernTheme.primaryColor, '#FF8A65']}
          style={styles.cafeteriaImageGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.cafeteriaImageIcon}>
            <Ionicons name="restaurant" size={32} color="white" />
          </View>
        </LinearGradient>
      </View>

      <View style={styles.cafeteriaContent}>
        <Text style={styles.cafeteriaName} numberOfLines={1}>{item.name}</Text>
        <Text style={styles.cafeteriaDescription} numberOfLines={2}>
          {item.description || 'Delicious food awaits you'}
        </Text>

        <View style={styles.cafeteriaFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={ModernTheme.warningColor} />
            <Text style={styles.ratingText}>4.5</Text>
          </View>
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, {
              backgroundColor: item.is_active ? ModernTheme.successColor : ModernTheme.errorColor
            }]} />
            <Text style={[styles.statusText, {
              color: item.is_active ? ModernTheme.successColor : ModernTheme.errorColor
            }]}>{item.is_active ? 'Open' : 'Closed'}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />

      {/* Modern Header with Gradient */}
      <LinearGradient
        colors={[ModernTheme.primaryColor, ModernTheme.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.greeting}>Good Morning!</Text>
            <Text style={styles.welcomeText}>What would you like to eat, {userName}?</Text>
          </View>
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => navigation.navigate('Profile')}
            activeOpacity={0.8}
          >
            <View style={styles.profileAvatar}>
              <Ionicons name="person" size={24} color={ModernTheme.primaryColor} />
            </View>
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ModernTheme.primaryColor]}
            tintColor={ModernTheme.primaryColor}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Modern Search Bar */}
        <TouchableOpacity
          style={styles.searchBar}
          onPress={() => navigation.navigate('Search')}
          activeOpacity={0.8}
        >
          <Ionicons name="search" size={20} color={ModernTheme.textSecondary} />
          <Text style={styles.searchPlaceholder}>Search for food, cafeterias...</Text>
          <Ionicons name="options" size={20} color={ModernTheme.textSecondary} />
        </TouchableOpacity>

        {/* Cafeterias Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Cafeterias</Text>
            <TouchableOpacity onPress={() => {}}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={ModernTheme.primaryColor} />
              <Text style={styles.loadingText}>Loading cafeterias...</Text>
            </View>
          ) : cafeterias.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="restaurant-outline" size={60} color={ModernTheme.textSecondary} />
              <Text style={styles.emptyTitle}>No cafeterias available</Text>
              <Text style={styles.emptyText}>Check back later for available cafeterias</Text>
            </View>
          ) : (
            <FlatList
              data={cafeterias}
              renderItem={renderCafeteria}
              keyExtractor={(item) => item.id.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.cafeteriasList}
            />
          )}
        </View>

        {/* Reorder Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Reorder</Text>
            <TouchableOpacity onPress={() => navigation.navigate('OrderHistory')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.emptyContainer}>
            <Ionicons name="time-outline" size={60} color={ModernTheme.textSecondary} />
            <Text style={styles.emptyTitle}>No previous orders</Text>
            <Text style={styles.emptyText}>Order something to see your favorites here!</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

// Cafeteria Screen - Exact replica of your Flutter app
function CafeteriaScreen({ route, navigation }) {
  const { cafeteriaId, cafeteriaName, cafeteria } = route.params;
  const { loadMenuItems } = useSupabase();
  const { addToCart } = useCart();
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMenu();
  }, [cafeteriaId]);

  const loadMenu = async () => {
    try {
      setLoading(true);
      const items = await loadMenuItems(cafeteriaId);
      setMenuItems(items);
    } catch (error) {
      console.error('❌ Error loading menu:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMenu();
    setRefreshing(false);
  };

  const handleAddToCart = (item) => {
    if (!item.is_available) {
      Alert.alert('Unavailable', 'This item is currently unavailable');
      return;
    }
    addToCart(item);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`, [
      { text: 'OK', style: 'default' }
    ]);
  };

  const renderMenuItem = ({ item }) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
      activeOpacity={0.8}
    >
      <View style={styles.menuItemImageContainer}>
        <LinearGradient
          colors={[ModernTheme.primaryColor, '#FF8A65']}
          style={styles.menuItemImageGradient}
        >
          <Ionicons name="restaurant" size={24} color="white" />
        </LinearGradient>
      </View>

      <View style={styles.menuItemContent}>
        <View style={styles.menuItemInfo}>
          <Text style={styles.menuItemName} numberOfLines={1}>{item.name}</Text>
          <Text style={styles.menuItemDescription} numberOfLines={2}>
            {item.description || 'Delicious food item'}
          </Text>

          <View style={styles.menuItemFooter}>
            <Text style={styles.menuItemPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
            <View style={styles.availabilityBadge}>
              <View style={[styles.statusDot, {
                backgroundColor: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]} />
              <Text style={[styles.availabilityText, {
                color: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]}>
                {item.is_available ? 'Available' : 'Unavailable'}
              </Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.addButton, {
            opacity: item.is_available ? 1 : 0.5,
            backgroundColor: item.is_available ? ModernTheme.primaryColor : ModernTheme.textSecondary
          }]}
          onPress={() => handleAddToCart(item)}
          disabled={!item.is_available}
          activeOpacity={0.8}
        >
          <Ionicons name="add" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.primaryColor, ModernTheme.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{cafeteriaName}</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('Search')}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="search" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Cafeteria Info Card */}
      <View style={styles.cafeteriaInfoCard}>
        <Text style={styles.cafeteriaInfoName}>{cafeteriaName}</Text>
        <Text style={styles.cafeteriaInfoDescription}>
          {cafeteria?.description || 'Delicious food awaits you'}
        </Text>
        <View style={styles.cafeteriaInfoDetails}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={ModernTheme.warningColor} />
            <Text style={styles.ratingText}>4.5</Text>
          </View>
          <View style={styles.deliveryTimeContainer}>
            <Ionicons name="time" size={16} color={ModernTheme.textSecondary} />
            <Text style={styles.deliveryTime}>15-20 min</Text>
          </View>
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, {
              backgroundColor: cafeteria?.is_active ? ModernTheme.successColor : ModernTheme.errorColor
            }]} />
            <Text style={[styles.statusText, {
              color: cafeteria?.is_active ? ModernTheme.successColor : ModernTheme.errorColor
            }]}>
              {cafeteria?.is_active ? 'Open' : 'Closed'}
            </Text>
          </View>
        </View>
      </View>

      {/* Menu Items */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.primaryColor} />
          <Text style={styles.loadingText}>Loading menu...</Text>
        </View>
      ) : menuItems.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="restaurant-outline" size={60} color={ModernTheme.textSecondary} />
          <Text style={styles.emptyTitle}>No menu items available</Text>
          <Text style={styles.emptyText}>This cafeteria doesn't have any items right now</Text>
        </View>
      ) : (
        <FlatList
          data={menuItems}
          renderItem={renderMenuItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.menuList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[ModernTheme.primaryColor]}
              tintColor={ModernTheme.primaryColor}
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

// Item Details Screen - Exact replica of your Flutter app
function ItemDetailsScreen({ route, navigation }) {
  const { item } = route.params;
  const { addToCart } = useCart();
  const { isFavorite, toggleFavorite } = useFavorites();
  const [quantity, setQuantity] = useState(1);

  const handleAddToCart = () => {
    if (!item.is_available) return;

    addToCart(item, quantity);
    Alert.alert(
      'Added to Cart',
      `${quantity} x ${item.name} added to cart`,
      [
        { text: 'Continue Shopping', style: 'default' },
        { text: 'View Cart', onPress: () => navigation.navigate('Cart') }
      ]
    );
  };

  const handleFavoriteToggle = () => {
    const added = toggleFavorite(item);
    Alert.alert(
      added ? 'Added to Favorites' : 'Removed from Favorites',
      added ? `${item.name} has been added to your favorites` : `${item.name} has been removed from your favorites`
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.primaryColor, ModernTheme.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Item Details</Text>
          <TouchableOpacity
            onPress={handleFavoriteToggle}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons
              name={isFavorite(item.id) ? "heart" : "heart-outline"}
              size={24}
              color="white"
            />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.itemDetailsContainer}>
          {/* Item Image */}
          <View style={styles.itemImageContainer}>
            <LinearGradient
              colors={[ModernTheme.primaryColor, '#FF8A65']}
              style={styles.itemImageLarge}
            >
              <Ionicons name="restaurant" size={60} color="white" />
            </LinearGradient>
          </View>

          {/* Item Info */}
          <Text style={styles.itemNameLarge}>{item.name}</Text>
          <Text style={styles.itemDescriptionLarge}>
            {item.description || 'Delicious food item with amazing taste and quality ingredients.'}
          </Text>

          {/* Price and Availability */}
          <View style={styles.itemPriceContainer}>
            <Text style={styles.itemPriceLarge}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
            <View style={styles.availabilityBadge}>
              <View style={[styles.statusDot, {
                backgroundColor: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]} />
              <Text style={[styles.availabilityText, {
                color: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]}>
                {item.is_available ? 'Available' : 'Unavailable'}
              </Text>
            </View>
          </View>

          {/* Quantity Selector */}
          {item.is_available && (
            <View style={styles.quantityContainer}>
              <Text style={styles.quantityLabel}>Quantity</Text>
              <View style={styles.quantitySelector}>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => setQuantity(Math.max(1, quantity - 1))}
                  activeOpacity={0.8}
                >
                  <Ionicons name="remove" size={20} color={ModernTheme.primaryColor} />
                </TouchableOpacity>
                <View style={styles.quantityDisplay}>
                  <Text style={styles.quantityText}>{quantity}</Text>
                </View>
                <TouchableOpacity
                  style={styles.quantityButton}
                  onPress={() => setQuantity(quantity + 1)}
                  activeOpacity={0.8}
                >
                  <Ionicons name="add" size={20} color={ModernTheme.primaryColor} />
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* Additional Info */}
          <View style={styles.additionalInfo}>
            <View style={styles.infoRow}>
              <Ionicons name="time-outline" size={20} color={ModernTheme.textSecondary} />
              <Text style={styles.infoText}>Preparation time: 10-15 minutes</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="restaurant-outline" size={20} color={ModernTheme.textSecondary} />
              <Text style={styles.infoText}>Fresh ingredients daily</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View style={styles.bottomContainer}>
        <View style={styles.totalPriceContainer}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalPrice}>{((item.price || 0) * quantity).toFixed(2)} EGP</Text>
        </View>
        <TouchableOpacity
          style={[styles.addToCartButton, {
            opacity: item.is_available ? 1 : 0.5,
            backgroundColor: item.is_available ? ModernTheme.primaryColor : ModernTheme.textSecondary
          }]}
          onPress={handleAddToCart}
          disabled={!item.is_available}
          activeOpacity={0.8}
        >
          <Ionicons name="bag-add" size={20} color="white" style={{ marginRight: 8 }} />
          <Text style={styles.addToCartText}>
            {item.is_available ? 'Add to Cart' : 'Unavailable'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

// Cart Screen - Exact replica of your Flutter app
function CartScreen({ navigation }) {
  const { items, removeFromCart, updateQuantity, clearCart, getTotalPrice, getTotalItems } = useCart();

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', style: 'destructive', onPress: clearCart }
      ]
    );
  };

  const handleRemoveItem = (item) => {
    Alert.alert(
      'Remove Item',
      `Remove ${item.name} from cart?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => removeFromCart(item.id) }
      ]
    );
  };

  const handleCheckout = () => {
    Alert.alert(
      'Checkout',
      'Proceed to checkout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Proceed', onPress: () => {
          Alert.alert('Success', 'Order placed successfully!');
          clearCart();
        }}
      ]
    );
  };

  const renderCartItem = ({ item }) => (
    <View style={styles.cartItem}>
      <View style={styles.cartItemImageContainer}>
        <LinearGradient
          colors={[ModernTheme.primaryColor, '#FF8A65']}
          style={styles.cartItemImage}
        >
          <Ionicons name="restaurant" size={20} color="white" />
        </LinearGradient>
      </View>

      <View style={styles.cartItemContent}>
        <View style={styles.cartItemInfo}>
          <Text style={styles.cartItemName} numberOfLines={1}>{item.name}</Text>
          <Text style={styles.cartItemPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
          <Text style={styles.cartItemSubtotal}>
            Subtotal: {((item.price || 0) * item.quantity).toFixed(2)} EGP
          </Text>
        </View>

        <View style={styles.cartItemActions}>
          <View style={styles.cartQuantityControls}>
            <TouchableOpacity
              style={styles.cartQuantityButton}
              onPress={() => updateQuantity(item.id, item.quantity - 1)}
              activeOpacity={0.8}
            >
              <Ionicons name="remove" size={16} color={ModernTheme.primaryColor} />
            </TouchableOpacity>
            <View style={styles.cartQuantityDisplay}>
              <Text style={styles.cartQuantityText}>{item.quantity}</Text>
            </View>
            <TouchableOpacity
              style={styles.cartQuantityButton}
              onPress={() => updateQuantity(item.id, item.quantity + 1)}
              activeOpacity={0.8}
            >
              <Ionicons name="add" size={16} color={ModernTheme.primaryColor} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveItem(item)}
            activeOpacity={0.8}
          >
            <Ionicons name="trash-outline" size={20} color={ModernTheme.errorColor} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.primaryColor, ModernTheme.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Cart ({getTotalItems()})</Text>
          {items.length > 0 && (
            <TouchableOpacity
              onPress={handleClearCart}
              style={styles.headerButton}
              activeOpacity={0.8}
            >
              <Text style={styles.clearCartText}>Clear All</Text>
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>

      {items.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="bag-outline" size={80} color={ModernTheme.textSecondary} />
          <Text style={styles.emptyTitle}>Your cart is empty</Text>
          <Text style={styles.emptyText}>Add some delicious items to get started</Text>
          <TouchableOpacity
            style={styles.browseButton}
            onPress={() => navigation.navigate('Home')}
            activeOpacity={0.8}
          >
            <Ionicons name="restaurant" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={styles.browseButtonText}>Browse Cafeterias</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <FlatList
            data={items}
            renderItem={renderCartItem}
            keyExtractor={(item) => `${item.id}-${Math.random()}`}
            contentContainerStyle={styles.cartList}
            showsVerticalScrollIndicator={false}
          />

          {/* Cart Summary and Checkout */}
          <View style={styles.cartFooter}>
            <View style={styles.cartSummary}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Items ({getTotalItems()})</Text>
                <Text style={styles.summaryValue}>{getTotalPrice().toFixed(2)} EGP</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Delivery Fee</Text>
                <Text style={styles.summaryValue}>5.00 EGP</Text>
              </View>
              <View style={[styles.summaryRow, styles.totalRow]}>
                <Text style={styles.totalLabel}>Total</Text>
                <Text style={styles.totalPrice}>{(getTotalPrice() + 5).toFixed(2)} EGP</Text>
              </View>
            </View>

            <TouchableOpacity
              style={styles.checkoutButton}
              onPress={handleCheckout}
              activeOpacity={0.8}
            >
              <Ionicons name="card" size={20} color="white" style={{ marginRight: 8 }} />
              <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </SafeAreaView>
  );
}

// Favorites Screen - Exact replica of your Flutter app
function FavoritesScreen({ navigation }) {
  const { favorites, removeFromFavorites } = useFavorites();
  const { addToCart } = useCart();

  const handleAddToCart = (item) => {
    if (!item.is_available) {
      Alert.alert('Unavailable', 'This item is currently unavailable');
      return;
    }
    addToCart(item);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`);
  };

  const handleRemoveFromFavorites = (item) => {
    Alert.alert(
      'Remove from Favorites',
      `Remove ${item.name} from favorites?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => removeFromFavorites(item.id) }
      ]
    );
  };

  const renderFavoriteItem = ({ item }) => (
    <TouchableOpacity
      style={styles.favoriteItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
      activeOpacity={0.8}
    >
      <View style={styles.favoriteItemImageContainer}>
        <LinearGradient
          colors={[ModernTheme.primaryColor, '#FF8A65']}
          style={styles.favoriteItemImage}
        >
          <Ionicons name="restaurant" size={20} color="white" />
        </LinearGradient>
      </View>

      <View style={styles.favoriteItemContent}>
        <View style={styles.favoriteItemInfo}>
          <Text style={styles.favoriteItemName} numberOfLines={1}>{item.name}</Text>
          <Text style={styles.favoriteItemDescription} numberOfLines={2}>
            {item.description || 'Delicious food item'}
          </Text>
          <View style={styles.favoriteItemFooter}>
            <Text style={styles.favoriteItemPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
            <View style={styles.availabilityBadge}>
              <View style={[styles.statusDot, {
                backgroundColor: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]} />
              <Text style={[styles.availabilityText, {
                color: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]}>
                {item.is_available ? 'Available' : 'Unavailable'}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.favoriteItemActions}>
          <TouchableOpacity
            style={[styles.favoriteAddButton, {
              opacity: item.is_available ? 1 : 0.5,
              backgroundColor: item.is_available ? ModernTheme.primaryColor : ModernTheme.textSecondary
            }]}
            onPress={() => handleAddToCart(item)}
            disabled={!item.is_available}
            activeOpacity={0.8}
          >
            <Ionicons name="bag-add" size={18} color="white" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.favoriteRemoveButton}
            onPress={() => handleRemoveFromFavorites(item)}
            activeOpacity={0.8}
          >
            <Ionicons name="heart" size={18} color={ModernTheme.errorColor} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.primaryColor, ModernTheme.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Favorites ({favorites.length})</Text>
        </View>
      </LinearGradient>

      {favorites.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart-outline" size={80} color={ModernTheme.textSecondary} />
          <Text style={styles.emptyTitle}>No favorites yet</Text>
          <Text style={styles.emptyText}>Add items to your favorites to see them here</Text>
          <TouchableOpacity
            style={styles.browseButton}
            onPress={() => navigation.navigate('Home')}
            activeOpacity={0.8}
          >
            <Ionicons name="restaurant" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={styles.browseButtonText}>Browse Cafeterias</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={favorites}
          renderItem={renderFavoriteItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.favoritesList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

// Search Screen - Exact replica of your Flutter app
function SearchScreen({ navigation }) {
  const { searchMenuItems } = useSupabase();
  const { addToCart } = useCart();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleSearch = async (query) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const results = await searchMenuItems(query);
      setSearchResults(results);
    } catch (error) {
      console.error('❌ Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (item) => {
    if (!item.is_available) {
      Alert.alert('Unavailable', 'This item is currently unavailable');
      return;
    }
    addToCart(item);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`);
  };

  const renderSearchResult = ({ item }) => (
    <TouchableOpacity
      style={styles.searchResultItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
      activeOpacity={0.8}
    >
      <View style={styles.searchResultImageContainer}>
        <LinearGradient
          colors={[ModernTheme.primaryColor, '#FF8A65']}
          style={styles.searchResultImage}
        >
          <Ionicons name="restaurant" size={20} color="white" />
        </LinearGradient>
      </View>

      <View style={styles.searchResultContent}>
        <View style={styles.searchResultInfo}>
          <Text style={styles.searchResultName} numberOfLines={1}>{item.name}</Text>
          <Text style={styles.searchResultCafeteria} numberOfLines={1}>
            {item.cafeterias?.name || 'Unknown Cafeteria'}
          </Text>
          <Text style={styles.searchResultDescription} numberOfLines={2}>
            {item.description || 'Delicious food item'}
          </Text>
          <View style={styles.searchResultFooter}>
            <Text style={styles.searchResultPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
            <View style={styles.availabilityBadge}>
              <View style={[styles.statusDot, {
                backgroundColor: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]} />
              <Text style={[styles.availabilityText, {
                color: item.is_available ? ModernTheme.successColor : ModernTheme.errorColor
              }]}>
                {item.is_available ? 'Available' : 'Unavailable'}
              </Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.searchResultAddButton, {
            opacity: item.is_available ? 1 : 0.5,
            backgroundColor: item.is_available ? ModernTheme.primaryColor : ModernTheme.textSecondary
          }]}
          onPress={() => handleAddToCart(item)}
          disabled={!item.is_available}
          activeOpacity={0.8}
        >
          <Ionicons name="add" size={18} color="white" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.primaryColor, ModernTheme.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Search</Text>
          <View style={styles.headerButton} />
        </View>
      </LinearGradient>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={ModernTheme.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for food, dishes..."
            placeholderTextColor={ModernTheme.textSecondary}
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text);
              handleSearch(text);
            }}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery('');
                setSearchResults([]);
              }}
              activeOpacity={0.8}
            >
              <Ionicons name="close-circle" size={20} color={ModernTheme.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Results */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.primaryColor} />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      ) : searchQuery.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="search-outline" size={80} color={ModernTheme.textSecondary} />
          <Text style={styles.emptyTitle}>Search for food</Text>
          <Text style={styles.emptyText}>Find your favorite dishes and restaurants</Text>
        </View>
      ) : searchResults.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="sad-outline" size={80} color={ModernTheme.textSecondary} />
          <Text style={styles.emptyTitle}>No results found</Text>
          <Text style={styles.emptyText}>Try searching for something else</Text>
        </View>
      ) : (
        <FlatList
          data={searchResults}
          renderItem={renderSearchResult}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.searchResultsList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

// Profile Screen - Exact replica of your Flutter app
function ProfileScreen({ navigation }) {
  const { currentUser } = useSupabase();
  const { favorites } = useFavorites();

  const profileOptions = [
    {
      id: 'account',
      title: 'Account Settings',
      icon: 'person-outline',
      onPress: () => Alert.alert('Account Settings', 'Feature coming soon!')
    },
    {
      id: 'orders',
      title: 'Order History',
      icon: 'time-outline',
      onPress: () => Alert.alert('Order History', 'Feature coming soon!')
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: 'notifications-outline',
      onPress: () => Alert.alert('Notifications', 'Feature coming soon!')
    },
    {
      id: 'payment',
      title: 'Payment Methods',
      icon: 'card-outline',
      onPress: () => Alert.alert('Payment Methods', 'Feature coming soon!')
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => Alert.alert('Help & Support', 'Contact <NAME_EMAIL>')
    },
    {
      id: 'about',
      title: 'About Us',
      icon: 'information-circle-outline',
      onPress: () => Alert.alert('About UniEats', 'UniEats - Your modern food delivery experience')
    }
  ];

  const renderProfileOption = ({ item }) => (
    <TouchableOpacity
      style={styles.profileOption}
      onPress={item.onPress}
      activeOpacity={0.8}
    >
      <View style={styles.profileOptionIcon}>
        <Ionicons name={item.icon} size={24} color={ModernTheme.primaryColor} />
      </View>
      <Text style={styles.profileOptionText}>{item.title}</Text>
      <Ionicons name="chevron-forward" size={20} color={ModernTheme.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.primaryColor, ModernTheme.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Profile</Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <LinearGradient
            colors={[ModernTheme.primaryColor, '#FF8A65']}
            style={styles.profileAvatarContainer}
          >
            <Ionicons name="person" size={40} color="white" />
          </LinearGradient>
          <Text style={styles.profileName}>{currentUser?.fullName || 'Demo User'}</Text>
          <Text style={styles.profileEmail}>{currentUser?.email || '<EMAIL>'}</Text>

          {/* Stats */}
          <View style={styles.profileStats}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>Orders</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{favorites.length}</Text>
              <Text style={styles.statLabel}>Favorites</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>4.8</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('Favorites')}
              activeOpacity={0.8}
            >
              <Ionicons name="heart" size={24} color={ModernTheme.primaryColor} />
              <Text style={styles.quickActionText}>Favorites</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('Cart')}
              activeOpacity={0.8}
            >
              <Ionicons name="bag" size={24} color={ModernTheme.primaryColor} />
              <Text style={styles.quickActionText}>Cart</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => Alert.alert('Order History', 'Feature coming soon!')}
              activeOpacity={0.8}
            >
              <Ionicons name="time" size={24} color={ModernTheme.primaryColor} />
              <Text style={styles.quickActionText}>Orders</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Profile Options */}
        <View style={styles.profileOptionsContainer}>
          <Text style={styles.sectionTitle}>Settings</Text>
          <FlatList
            data={profileOptions}
            renderItem={renderProfileOption}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        </View>

        {/* App Info */}
        <View style={styles.appInfoContainer}>
          <Text style={styles.appVersion}>UniEats v1.0.0</Text>
          <Text style={styles.appDescription}>Modern food delivery experience</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

// Main Tab Navigator - Exact replica of your Flutter app
function MainTabNavigator() {
  const { getTotalItems } = useCart();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'Cart') {
            iconName = focused ? 'bag' : 'bag-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          // Cart badge
          if (route.name === 'Cart' && getTotalItems() > 0) {
            return (
              <View style={styles.tabIconContainer}>
                <Ionicons name={iconName} size={size} color={color} />
                <View style={styles.cartBadge}>
                  <Text style={styles.cartBadgeText}>
                    {getTotalItems() > 99 ? '99+' : getTotalItems()}
                  </Text>
                </View>
              </View>
            );
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: ModernTheme.primaryColor,
        tabBarInactiveTintColor: ModernTheme.textSecondary,
        tabBarStyle: {
          backgroundColor: ModernTheme.surfaceColor,
          borderTopWidth: 0,
          elevation: ModernTheme.elevationHigh,
          shadowColor: ModernTheme.cardShadow,
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.15,
          shadowRadius: 8,
          height: 65,
          paddingBottom: 10,
          paddingTop: 8,
          borderTopLeftRadius: ModernTheme.borderRadiusXL,
          borderTopRightRadius: ModernTheme.borderRadiusXL,
        },
        headerShown: false,
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarItemStyle: {
          paddingVertical: 5,
        },
      })}
    >
      <Tab.Screen name="Home" component={DashboardScreen} options={{ title: 'Home' }} />
      <Tab.Screen name="Favorites" component={FavoritesScreen} options={{ title: 'Favorites' }} />
      <Tab.Screen name="Cart" component={CartScreen} options={{ title: 'Cart' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
}

// Main Stack Navigator
function AppNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: ModernTheme.backgroundColor },
        gestureEnabled: true,
        gestureDirection: 'horizontal',
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      <Stack.Screen name="Cafeteria" component={CafeteriaScreen} />
      <Stack.Screen name="ItemDetails" component={ItemDetailsScreen} />
      <Stack.Screen name="Search" component={SearchScreen} />
    </Stack.Navigator>
  );
}

// Main App Component
export default function App() {
  useEffect(() => {
    console.log('🍽️ UniEats App Starting...');
    console.log('📡 Supabase URL:', SUPABASE_URL);
    console.log('🎨 Modern Theme Loaded');
  }, []);

  return (
    <SafeAreaProvider>
      <SupabaseProvider>
        <AuthProvider>
          <CartProvider>
            <FavoritesProvider>
              <OrderProvider>
                <NavigationContainer>
                  <AppNavigator />
                  <StatusBar style="light" backgroundColor={ModernTheme.primaryColor} />
                </NavigationContainer>
              </OrderProvider>
            </FavoritesProvider>
          </CartProvider>
        </AuthProvider>
      </SupabaseProvider>
    </SafeAreaProvider>
  );
}

// Modern Styles - Exact match to your Flutter app
const styles = StyleSheet.create({
  // Base styles
  container: {
    flex: 1,
    backgroundColor: ModernTheme.backgroundColor,
  },

  // Header styles
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  greeting: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
    marginBottom: 4,
    fontWeight: '500',
  },
  welcomeText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    lineHeight: 24,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  profileButton: {
    padding: 4,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearCartText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Content styles
  content: {
    flex: 1,
    backgroundColor: ModernTheme.backgroundColor,
  },

  // Search bar
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusXL,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginHorizontal: 20,
    marginTop: 20,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchPlaceholder: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: ModernTheme.textSecondary,
    fontWeight: '500',
  },

  // Search Screen styles
  searchContainer: {
    padding: 20,
    backgroundColor: ModernTheme.surfaceColor,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.backgroundColor,
    borderRadius: ModernTheme.borderRadiusL,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: ModernTheme.textColor,
    marginLeft: 12,
    fontWeight: '500',
  },

  // Section styles
  section: {
    marginVertical: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    letterSpacing: 0.5,
  },
  seeAllText: {
    fontSize: 16,
    color: ModernTheme.primaryColor,
    fontWeight: '600',
  },

  // Cafeteria styles
  cafeteriasList: {
    paddingLeft: 20,
  },
  cafeteriaCard: {
    width: 280,
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusL,
    elevation: ModernTheme.elevationMedium,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    overflow: 'hidden',
  },
  cafeteriaImageContainer: {
    height: 120,
  },
  cafeteriaImageGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cafeteriaImageIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cafeteriaContent: {
    padding: 16,
  },
  cafeteriaName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 6,
  },
  cafeteriaDescription: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  cafeteriaFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    marginLeft: 4,
    fontWeight: '600',
  },
  deliveryTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryTime: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    marginLeft: 4,
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },

  // Cafeteria info card
  cafeteriaInfoCard: {
    backgroundColor: ModernTheme.surfaceColor,
    padding: 20,
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: ModernTheme.borderRadiusL,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cafeteriaInfoName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 8,
  },
  cafeteriaInfoDescription: {
    fontSize: 16,
    color: ModernTheme.textSecondary,
    marginBottom: 12,
    lineHeight: 22,
  },
  cafeteriaInfoDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  // Menu item styles
  menuList: {
    padding: 20,
  },
  menuItem: {
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusL,
    marginBottom: 15,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  menuItemImageContainer: {
    width: 80,
    height: 80,
    margin: 15,
  },
  menuItemImageGradient: {
    flex: 1,
    borderRadius: ModernTheme.borderRadiusM,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  menuItemInfo: {
    flex: 1,
    marginLeft: 15,
  },
  menuItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 4,
  },
  menuItemDescription: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  menuItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.primaryColor,
  },
  availabilityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  availabilityText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 15,
  },

  // Item details styles
  itemDetailsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  itemImageContainer: {
    marginBottom: 20,
  },
  itemImageLarge: {
    width: 150,
    height: 150,
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemNameLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 10,
    textAlign: 'center',
  },
  itemDescriptionLarge: {
    fontSize: 16,
    color: ModernTheme.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  itemPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 30,
  },
  itemPriceLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.primaryColor,
  },

  // Quantity selector
  quantityContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  quantityLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 15,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 10,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ModernTheme.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityDisplay: {
    backgroundColor: ModernTheme.backgroundColor,
    borderRadius: 15,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginHorizontal: 15,
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
  },

  // Additional info
  additionalInfo: {
    width: '100%',
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    marginLeft: 12,
  },

  // Bottom container
  bottomContainer: {
    backgroundColor: ModernTheme.surfaceColor,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.borderColor,
  },
  totalPriceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
  },
  totalPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.primaryColor,
  },
  addToCartButton: {
    flexDirection: 'row',
    backgroundColor: ModernTheme.primaryColor,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addToCartText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Tab navigation
  tabIconContainer: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: ModernTheme.errorColor,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },

  // Loading and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: ModernTheme.textSecondary,
    marginTop: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginTop: 20,
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 16,
    color: ModernTheme.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  browseButton: {
    flexDirection: 'row',
    backgroundColor: ModernTheme.primaryColor,
    borderRadius: 25,
    paddingHorizontal: 30,
    paddingVertical: 15,
    alignItems: 'center',
  },
  browseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },

  // Cart styles
  cartList: {
    padding: 20,
  },
  cartItem: {
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusL,
    marginBottom: 15,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  cartItemImageContainer: {
    width: 60,
    height: 60,
    margin: 15,
  },
  cartItemImage: {
    flex: 1,
    borderRadius: ModernTheme.borderRadiusM,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  cartItemInfo: {
    flex: 1,
    marginLeft: 15,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 4,
  },
  cartItemPrice: {
    fontSize: 14,
    color: ModernTheme.primaryColor,
    fontWeight: '600',
    marginBottom: 4,
  },
  cartItemSubtotal: {
    fontSize: 12,
    color: ModernTheme.textSecondary,
  },
  cartItemActions: {
    alignItems: 'center',
  },
  cartQuantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.backgroundColor,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginBottom: 8,
  },
  cartQuantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: ModernTheme.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartQuantityDisplay: {
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginHorizontal: 8,
  },
  cartQuantityText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
  },
  removeButton: {
    padding: 5,
  },
  cartFooter: {
    backgroundColor: ModernTheme.surfaceColor,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.borderColor,
  },
  cartSummary: {
    marginBottom: 20,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: ModernTheme.textSecondary,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: ModernTheme.textColor,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: ModernTheme.borderColor,
    paddingTop: 12,
    marginTop: 8,
  },
  checkoutButton: {
    flexDirection: 'row',
    backgroundColor: ModernTheme.primaryColor,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkoutButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Favorites styles
  favoritesList: {
    padding: 20,
  },
  favoriteItem: {
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusL,
    marginBottom: 15,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  favoriteItemImageContainer: {
    width: 60,
    height: 60,
    margin: 15,
  },
  favoriteItemImage: {
    flex: 1,
    borderRadius: ModernTheme.borderRadiusM,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  favoriteItemInfo: {
    flex: 1,
    marginLeft: 15,
  },
  favoriteItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 4,
  },
  favoriteItemDescription: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  favoriteItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  favoriteItemPrice: {
    fontSize: 14,
    color: ModernTheme.primaryColor,
    fontWeight: '600',
  },
  favoriteItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  favoriteAddButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  favoriteRemoveButton: {
    padding: 5,
  },

  // Search results styles
  searchResultsList: {
    padding: 20,
  },
  searchResultItem: {
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusL,
    marginBottom: 15,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  searchResultImageContainer: {
    width: 60,
    height: 60,
    margin: 15,
  },
  searchResultImage: {
    flex: 1,
    borderRadius: ModernTheme.borderRadiusM,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchResultContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  searchResultInfo: {
    flex: 1,
    marginLeft: 15,
  },
  searchResultName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 2,
  },
  searchResultCafeteria: {
    fontSize: 12,
    color: ModernTheme.primaryColor,
    fontWeight: '600',
    marginBottom: 4,
  },
  searchResultDescription: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  searchResultFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  searchResultPrice: {
    fontSize: 14,
    color: ModernTheme.primaryColor,
    fontWeight: '600',
  },
  searchResultAddButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 15,
  },

  // Profile styles
  profileHeader: {
    backgroundColor: ModernTheme.surfaceColor,
    padding: 30,
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: ModernTheme.borderRadiusL,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  profileAvatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.textColor,
    marginBottom: 5,
  },
  profileEmail: {
    fontSize: 16,
    color: ModernTheme.textSecondary,
    marginBottom: 20,
  },
  profileStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.primaryColor,
  },
  statLabel: {
    fontSize: 12,
    color: ModernTheme.textSecondary,
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: ModernTheme.borderColor,
    marginHorizontal: 20,
  },
  quickActionsContainer: {
    marginHorizontal: 20,
    marginTop: 30,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickActionButton: {
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusL,
    padding: 20,
    alignItems: 'center',
    minWidth: 80,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActionText: {
    fontSize: 12,
    color: ModernTheme.textColor,
    marginTop: 8,
    fontWeight: '600',
  },
  profileOptionsContainer: {
    marginHorizontal: 20,
    marginTop: 30,
  },
  profileOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.surfaceColor,
    borderRadius: ModernTheme.borderRadiusL,
    padding: 20,
    marginBottom: 15,
    elevation: ModernTheme.elevationLow,
    shadowColor: ModernTheme.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  profileOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ModernTheme.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  profileOptionText: {
    flex: 1,
    fontSize: 16,
    color: ModernTheme.textColor,
    fontWeight: '500',
  },
  appInfoContainer: {
    alignItems: 'center',
    padding: 30,
  },
  appVersion: {
    fontSize: 14,
    color: ModernTheme.textSecondary,
    fontWeight: '600',
  },
  appDescription: {
    fontSize: 12,
    color: ModernTheme.textSecondary,
    marginTop: 4,
  },
});
