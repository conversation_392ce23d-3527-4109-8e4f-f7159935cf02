import React, { useState, useEffect, createContext, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  RefreshControl,
  Alert,
  TextInput,
  Dimensions,
  ActivityIndicator,
  SafeAreaView
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Supabase Configuration (from your original app)
const SUPABASE_URL = 'https://lqtnaxvqkoynaziiinqh.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const { width, height } = Dimensions.get('window');

// Modern Theme (exact match to your Flutter app)
const ModernTheme = {
  colors: {
    primary: '#FF6B35',
    primaryDark: '#E55A2B',
    primaryLight: '#FFE5DC',
    secondary: '#4ECDC4',
    background: '#F8F9FA',
    surface: '#FFFFFF',
    text: '#2C3E50',
    textSecondary: '#7F8C8D',
    border: '#E9ECEF',
    warning: '#F39C12',
    success: '#27AE60',
    error: '#E74C3C',
    cardShadow: 'rgba(0, 0, 0, 0.1)',
  },
  spacing: {
    xs: 4,
    s: 8,
    m: 16,
    l: 24,
    xl: 32,
  },
  borderRadius: {
    s: 8,
    m: 12,
    l: 16,
    xl: 20,
  },
  elevation: {
    low: 2,
    medium: 4,
    high: 8,
  },
};

// Context for Supabase data
const SupabaseContext = createContext();
const AuthContext = createContext();
const CartContext = createContext();
const FavoritesContext = createContext();

// Supabase Provider with full functionality
const SupabaseProvider = ({ children }) => {
  const [cafeterias, setCafeterias] = useState([]);
  const [menuItems, setMenuItems] = useState({});
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState(null);

  const loadCafeterias = async () => {
    try {
      console.log('Loading cafeterias...');
      setLoading(true);
      const { data, error } = await supabase
        .from('cafeterias')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      setCafeterias(data || []);
      console.log('Loaded cafeterias:', data?.length);
      return data || [];
    } catch (error) {
      console.error('Error loading cafeterias:', error);
      // Don't show alert, just log error
      return [];
    } finally {
      setLoading(false);
    }
  };

  const loadMenuItems = async (cafeteriaId) => {
    try {
      console.log('Loading menu items for cafeteria:', cafeteriaId);
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .eq('cafeteria_id', cafeteriaId)
        .order('name');

      if (error) {
        console.error('Menu items error:', error);
        throw error;
      }

      const items = data || [];
      setMenuItems(prev => ({ ...prev, [cafeteriaId]: items }));
      console.log('Loaded menu items:', items.length);
      return items;
    } catch (error) {
      console.error('Error loading menu items:', error);
      return [];
    }
  };

  const searchMenuItems = async (query) => {
    try {
      const { data, error } = await supabase
        .from('menu_items')
        .select('*, cafeterias(name)')
        .ilike('name', `%${query}%`)
        .eq('is_available', true)
        .limit(20);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching menu items:', error);
      return [];
    }
  };

  useEffect(() => {
    loadCafeterias();
    // Set demo user
    setCurrentUser({
      id: 'demo-user',
      fullName: 'Demo User',
      email: '<EMAIL>'
    });
  }, []);

  const value = {
    cafeterias,
    menuItems,
    loading,
    currentUser,
    loadCafeterias,
    loadMenuItems,
    searchMenuItems,
    supabase,
  };

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  );
};

// Auth Provider
const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Simple auth for demo (you can enhance this)
  const login = (userData) => {
    setCurrentUser(userData);
    setIsAuthenticated(true);
  };

  const logout = () => {
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const value = {
    currentUser,
    isAuthenticated,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Cart Provider with persistence
const CartProvider = ({ children }) => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load cart from storage
  useEffect(() => {
    loadCart();
  }, []);

  // Save cart to storage whenever items change
  useEffect(() => {
    if (!loading) {
      saveCart();
    }
  }, [items, loading]);

  const loadCart = async () => {
    try {
      const savedCart = await AsyncStorage.getItem('cart');
      if (savedCart) {
        setItems(JSON.parse(savedCart));
      }
    } catch (error) {
      console.error('Error loading cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveCart = async () => {
    try {
      await AsyncStorage.setItem('cart', JSON.stringify(items));
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  };

  const addToCart = (item, quantity = 1) => {
    setItems(prev => {
      const existingItem = prev.find(i => i.id === item.id);
      if (existingItem) {
        return prev.map(i =>
          i.id === item.id
            ? { ...i, quantity: i.quantity + quantity }
            : i
        );
      }
      return [...prev, { ...item, quantity }];
    });
  };

  const removeFromCart = (itemId) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    setItems(prev =>
      prev.map(item =>
        item.id === itemId
          ? { ...item, quantity }
          : item
      )
    );
  };

  const clearCart = () => {
    setItems([]);
  };

  const getTotalPrice = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0);
  };

  const value = {
    items,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getTotalPrice,
    getTotalItems,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

// Favorites Provider with persistence
const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load favorites from storage
  useEffect(() => {
    loadFavorites();
  }, []);

  // Save favorites to storage whenever they change
  useEffect(() => {
    if (!loading) {
      saveFavorites();
    }
  }, [favorites, loading]);

  const loadFavorites = async () => {
    try {
      const savedFavorites = await AsyncStorage.getItem('favorites');
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites));
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveFavorites = async () => {
    try {
      await AsyncStorage.setItem('favorites', JSON.stringify(favorites));
    } catch (error) {
      console.error('Error saving favorites:', error);
    }
  };

  const addToFavorites = (item) => {
    setFavorites(prev => {
      if (prev.find(fav => fav.id === item.id)) {
        return prev;
      }
      return [...prev, item];
    });
  };

  const removeFromFavorites = (itemId) => {
    setFavorites(prev => prev.filter(item => item.id !== itemId));
  };

  const isFavorite = (itemId) => {
    return favorites.some(item => item.id === itemId);
  };

  const toggleFavorite = (item) => {
    if (isFavorite(item.id)) {
      removeFromFavorites(item.id);
      return false;
    } else {
      addToFavorites(item);
      return true;
    }
  };

  const value = {
    favorites,
    loading,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};

// Custom hooks
const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (!context) {
    throw new Error('useSupabase must be used within SupabaseProvider');
  }
  return context;
};

const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within CartProvider');
  }
  return context;
};

const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within FavoritesProvider');
  }
  return context;
};

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Sample data
const sampleCafeterias = [
  { id: 1, name: 'Campus Café', image: '🏪', rating: 4.5, deliveryTime: '15-20 min' },
  { id: 2, name: 'Student Bistro', image: '🍽️', rating: 4.3, deliveryTime: '20-25 min' },
  { id: 3, name: 'Quick Bites', image: '🍔', rating: 4.7, deliveryTime: '10-15 min' },
  { id: 4, name: 'Healthy Corner', image: '🥗', rating: 4.4, deliveryTime: '15-20 min' },
];

const sampleCategories = [
  { id: 1, name: 'Pizza', icon: '🍕' },
  { id: 2, name: 'Burgers', icon: '🍔' },
  { id: 3, name: 'Salads', icon: '🥗' },
  { id: 4, name: 'Drinks', icon: '🥤' },
];

// Dashboard Screen - Exact replica of your Flutter app
function DashboardScreen({ navigation }) {
  const { cafeterias, loading, loadCafeterias, currentUser } = useSupabase();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCafeterias();
    setRefreshing(false);
  };

  const userName = currentUser?.fullName?.split(' ')[0] || 'there';

  const renderCafeteria = ({ item, index }) => (
    <TouchableOpacity
      style={[styles.modernCafeteriaCard, { marginRight: index === cafeterias.length - 1 ? 20 : 15 }]}
      onPress={() => navigation.navigate('Cafeteria', {
        cafeteriaId: item.id,
        cafeteriaName: item.name,
        cafeteria: item
      })}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={['#FF6B35', '#FF8A65']}
        style={styles.cafeteriaGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.cafeteriaImageContainer}>
          <Text style={styles.cafeteriaEmoji}>🏪</Text>
        </View>
      </LinearGradient>

      <View style={styles.cafeteriaCardContent}>
        <Text style={styles.modernCafeteriaName} numberOfLines={1}>{item.name}</Text>
        <Text style={styles.modernCafeteriaDescription} numberOfLines={2}>
          {item.description || 'Delicious food awaits you'}
        </Text>

        <View style={styles.modernCafeteriaFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={ModernTheme.colors.warning} />
            <Text style={styles.ratingText}>4.5</Text>
          </View>
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, {
              backgroundColor: item.is_active ? ModernTheme.colors.success : ModernTheme.colors.error
            }]} />
            <Text style={styles.statusText}>{item.is_active ? 'Open' : 'Closed'}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />

      {/* Modern Header with Gradient */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Text style={styles.greeting}>Good Morning!</Text>
            <Text style={styles.welcomeText}>What would you like to eat, {userName}?</Text>
          </View>
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => navigation.navigate('Profile')}
            activeOpacity={0.8}
          >
            <View style={styles.profileAvatar}>
              <Ionicons name="person" size={24} color={ModernTheme.colors.primary} />
            </View>
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ModernTheme.colors.primary]}
            tintColor={ModernTheme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Modern Search Bar */}
        <TouchableOpacity
          style={styles.modernSearchBar}
          onPress={() => navigation.navigate('Search')}
          activeOpacity={0.8}
        >
          <Ionicons name="search" size={20} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.searchPlaceholder}>Search for food, cafeterias...</Text>
          <Ionicons name="options" size={20} color={ModernTheme.colors.textSecondary} />
        </TouchableOpacity>

        {/* Cafeterias Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Cafeterias</Text>
            <TouchableOpacity onPress={() => navigation.navigate('CafeteriaList')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={ModernTheme.colors.primary} />
              <Text style={styles.loadingText}>Loading cafeterias...</Text>
            </View>
          ) : cafeterias.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <Ionicons name="restaurant-outline" size={60} color={ModernTheme.colors.textSecondary} />
              <Text style={styles.emptyStateTitle}>No cafeterias available</Text>
              <Text style={styles.emptyStateText}>Check back later for available cafeterias</Text>
            </View>
          ) : (
            <FlatList
              data={cafeterias}
              renderItem={renderCafeteria}
              keyExtractor={(item) => item.id.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.cafeteriasHorizontalList}
            />
          )}
        </View>

        {/* Reorder Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Reorder</Text>
            <TouchableOpacity onPress={() => navigation.navigate('OrderHistory')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.emptyStateContainer}>
            <Ionicons name="time-outline" size={60} color={ModernTheme.colors.textSecondary} />
            <Text style={styles.emptyStateTitle}>No previous orders</Text>
            <Text style={styles.emptyStateText}>Order something to see your favorites here!</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

// Cafeteria Screen - Exact replica of your Flutter app
function CafeteriaScreen({ route, navigation }) {
  const { cafeteriaId, cafeteriaName, cafeteria } = route.params;
  const { loadMenuItems } = useSupabase();
  const { addToCart } = useCart();
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMenu();
  }, [cafeteriaId]);

  const loadMenu = async () => {
    try {
      setLoading(true);
      const items = await loadMenuItems(cafeteriaId);
      setMenuItems(items);
    } catch (error) {
      console.error('Error loading menu:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMenu();
    setRefreshing(false);
  };

  const handleAddToCart = (item) => {
    if (!item.is_available) return;
    addToCart(item);
    // Show success feedback
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`, [
      { text: 'OK', style: 'default' }
    ]);
  };

  const renderMenuItem = ({ item }) => (
    <TouchableOpacity
      style={styles.modernMenuItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
      activeOpacity={0.8}
    >
      <View style={styles.menuItemCard}>
        <View style={styles.menuItemImageContainer}>
          <LinearGradient
            colors={['#FF6B35', '#FF8A65']}
            style={styles.menuItemImageGradient}
          >
            <Text style={styles.menuItemEmoji}>🍽️</Text>
          </LinearGradient>
        </View>

        <View style={styles.menuItemContent}>
          <View style={styles.menuItemInfo}>
            <Text style={styles.modernMenuItemName} numberOfLines={1}>{item.name}</Text>
            <Text style={styles.modernMenuItemDescription} numberOfLines={2}>
              {item.description || 'Delicious food item'}
            </Text>

            <View style={styles.menuItemFooter}>
              <Text style={styles.modernMenuItemPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
              <View style={styles.availabilityBadge}>
                <View style={[styles.statusDot, {
                  backgroundColor: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
                }]} />
                <Text style={[styles.availabilityText, {
                  color: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
                }]}>
                  {item.is_available ? 'Available' : 'Unavailable'}
                </Text>
              </View>
            </View>
          </View>

          <TouchableOpacity
            style={[styles.modernAddButton, {
              opacity: item.is_available ? 1 : 0.5,
              backgroundColor: item.is_available ? ModernTheme.colors.primary : ModernTheme.colors.textSecondary
            }]}
            onPress={() => handleAddToCart(item)}
            disabled={!item.is_available}
            activeOpacity={0.8}
          >
            <Ionicons name="add" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{cafeteriaName}</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('Search')}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="search" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Cafeteria Info Card */}
      <View style={styles.cafeteriaInfoCard}>
        <Text style={styles.cafeteriaInfoName}>{cafeteriaName}</Text>
        <Text style={styles.cafeteriaInfoDescription}>
          {cafeteria?.description || 'Delicious food awaits you'}
        </Text>
        <View style={styles.cafeteriaInfoDetails}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={ModernTheme.colors.warning} />
            <Text style={styles.ratingText}>4.5</Text>
          </View>
          <View style={styles.deliveryTimeContainer}>
            <Ionicons name="time" size={16} color={ModernTheme.colors.textSecondary} />
            <Text style={styles.deliveryTime}>15-20 min</Text>
          </View>
          <View style={styles.statusContainer}>
            <View style={[styles.statusDot, {
              backgroundColor: cafeteria?.is_active ? ModernTheme.colors.success : ModernTheme.colors.error
            }]} />
            <Text style={[styles.statusText, {
              color: cafeteria?.is_active ? ModernTheme.colors.success : ModernTheme.colors.error
            }]}>
              {cafeteria?.is_active ? 'Open' : 'Closed'}
            </Text>
          </View>
        </View>
      </View>

      {/* Menu Items */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.colors.primary} />
          <Text style={styles.loadingText}>Loading menu...</Text>
        </View>
      ) : menuItems.length === 0 ? (
        <View style={styles.emptyStateContainer}>
          <Ionicons name="restaurant-outline" size={60} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyStateTitle}>No menu items available</Text>
          <Text style={styles.emptyStateText}>This cafeteria doesn't have any items right now</Text>
        </View>
      ) : (
        <FlatList
          data={menuItems}
          renderItem={renderMenuItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.menuList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[ModernTheme.colors.primary]}
              tintColor={ModernTheme.colors.primary}
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

// Item Details Screen - Exact replica of your Flutter app
function ItemDetailsScreen({ route, navigation }) {
  const { item } = route.params;
  const { addToCart } = useCart();
  const { isFavorite, toggleFavorite } = useFavorites();
  const [quantity, setQuantity] = useState(1);

  const handleAddToCart = () => {
    if (!item.is_available) return;

    addToCart(item, quantity);
    Alert.alert(
      'Added to Cart',
      `${quantity} x ${item.name} added to cart`,
      [
        { text: 'Continue Shopping', style: 'default' },
        { text: 'View Cart', onPress: () => navigation.navigate('Cart') }
      ]
    );
  };

  const handleFavoriteToggle = () => {
    const added = toggleFavorite(item);
    Alert.alert(
      added ? 'Added to Favorites' : 'Removed from Favorites',
      added ? `${item.name} has been added to your favorites` : `${item.name} has been removed from your favorites`
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Item Details</Text>
          <TouchableOpacity
            onPress={handleFavoriteToggle}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons
              name={isFavorite(item.id) ? "heart" : "heart-outline"}
              size={24}
              color="white"
            />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.itemDetailsContainer}>
          {/* Item Image */}
          <View style={styles.itemImageLargeContainer}>
            <LinearGradient
              colors={['#FF6B35', '#FF8A65']}
              style={styles.itemImageLarge}
            >
              <Text style={styles.itemEmojiLarge}>🍽️</Text>
            </LinearGradient>
          </View>

          {/* Item Info */}
          <Text style={styles.itemNameLarge}>{item.name}</Text>
          <Text style={styles.itemDescriptionLarge}>
            {item.description || 'Delicious food item with amazing taste and quality ingredients.'}
          </Text>

          {/* Price and Availability */}
          <View style={styles.itemPriceContainer}>
            <Text style={styles.itemPriceLarge}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
            <View style={styles.availabilityBadge}>
              <View style={[styles.statusDot, {
                backgroundColor: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
              }]} />
              <Text style={[styles.availabilityText, {
                color: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
              }]}>
                {item.is_available ? 'Available' : 'Unavailable'}
              </Text>
            </View>
          </View>

          {/* Quantity Selector */}
          {item.is_available && (
            <View style={styles.quantityContainer}>
              <Text style={styles.quantityLabel}>Quantity</Text>
              <View style={styles.modernQuantitySelector}>
                <TouchableOpacity
                  style={styles.modernQuantityButton}
                  onPress={() => setQuantity(Math.max(1, quantity - 1))}
                  activeOpacity={0.8}
                >
                  <Ionicons name="remove" size={20} color={ModernTheme.colors.primary} />
                </TouchableOpacity>
                <View style={styles.quantityDisplay}>
                  <Text style={styles.quantityText}>{quantity}</Text>
                </View>
                <TouchableOpacity
                  style={styles.modernQuantityButton}
                  onPress={() => setQuantity(quantity + 1)}
                  activeOpacity={0.8}
                >
                  <Ionicons name="add" size={20} color={ModernTheme.colors.primary} />
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* Additional Info */}
          <View style={styles.additionalInfo}>
            <View style={styles.infoRow}>
              <Ionicons name="time-outline" size={20} color={ModernTheme.colors.textSecondary} />
              <Text style={styles.infoText}>Preparation time: 10-15 minutes</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="restaurant-outline" size={20} color={ModernTheme.colors.textSecondary} />
              <Text style={styles.infoText}>Fresh ingredients daily</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View style={styles.bottomContainer}>
        <View style={styles.totalPriceContainer}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalPrice}>{((item.price || 0) * quantity).toFixed(2)} EGP</Text>
        </View>
        <TouchableOpacity
          style={[styles.modernAddToCartButton, {
            opacity: item.is_available ? 1 : 0.5,
            backgroundColor: item.is_available ? ModernTheme.colors.primary : ModernTheme.colors.textSecondary
          }]}
          onPress={handleAddToCart}
          disabled={!item.is_available}
          activeOpacity={0.8}
        >
          <Ionicons name="bag-add" size={20} color="white" style={{ marginRight: 8 }} />
          <Text style={styles.addToCartText}>
            {item.is_available ? 'Add to Cart' : 'Unavailable'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

// Cart Screen - Exact replica of your Flutter app
function CartScreen({ navigation }) {
  const { items, removeFromCart, updateQuantity, clearCart, getTotalPrice, getTotalItems } = useCart();

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', style: 'destructive', onPress: clearCart }
      ]
    );
  };

  const handleRemoveItem = (item) => {
    Alert.alert(
      'Remove Item',
      `Remove ${item.name} from cart?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => removeFromCart(item.id) }
      ]
    );
  };

  const handleCheckout = () => {
    Alert.alert(
      'Checkout',
      'Proceed to checkout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Proceed', onPress: () => {
          // Navigate to checkout screen
          Alert.alert('Success', 'Order placed successfully!');
          clearCart();
        }}
      ]
    );
  };

  const renderCartItem = ({ item }) => (
    <View style={styles.modernCartItem}>
      <View style={styles.cartItemImageContainer}>
        <LinearGradient
          colors={['#FF6B35', '#FF8A65']}
          style={styles.cartItemImage}
        >
          <Text style={styles.cartItemEmoji}>🍽️</Text>
        </LinearGradient>
      </View>

      <View style={styles.cartItemContent}>
        <View style={styles.cartItemInfo}>
          <Text style={styles.cartItemName} numberOfLines={1}>{item.name}</Text>
          <Text style={styles.cartItemPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
          <Text style={styles.cartItemSubtotal}>
            Subtotal: {((item.price || 0) * item.quantity).toFixed(2)} EGP
          </Text>
        </View>

        <View style={styles.cartItemActions}>
          <View style={styles.cartQuantityControls}>
            <TouchableOpacity
              style={styles.cartQuantityButton}
              onPress={() => updateQuantity(item.id, item.quantity - 1)}
              activeOpacity={0.8}
            >
              <Ionicons name="remove" size={16} color={ModernTheme.colors.primary} />
            </TouchableOpacity>
            <View style={styles.cartQuantityDisplay}>
              <Text style={styles.cartQuantityText}>{item.quantity}</Text>
            </View>
            <TouchableOpacity
              style={styles.cartQuantityButton}
              onPress={() => updateQuantity(item.id, item.quantity + 1)}
              activeOpacity={0.8}
            >
              <Ionicons name="add" size={16} color={ModernTheme.colors.primary} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveItem(item)}
            activeOpacity={0.8}
          >
            <Ionicons name="trash-outline" size={20} color={ModernTheme.colors.error} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Cart ({getTotalItems()})</Text>
          {items.length > 0 && (
            <TouchableOpacity
              onPress={handleClearCart}
              style={styles.headerButton}
              activeOpacity={0.8}
            >
              <Text style={styles.clearCartText}>Clear All</Text>
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>

      {items.length === 0 ? (
        <View style={styles.emptyStateContainer}>
          <Ionicons name="bag-outline" size={80} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyStateTitle}>Your cart is empty</Text>
          <Text style={styles.emptyStateText}>Add some delicious items to get started</Text>
          <TouchableOpacity
            style={styles.modernBrowseButton}
            onPress={() => navigation.navigate('Home')}
            activeOpacity={0.8}
          >
            <Ionicons name="restaurant" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={styles.browseButtonText}>Browse Cafeterias</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <FlatList
            data={items}
            renderItem={renderCartItem}
            keyExtractor={(item) => `${item.id}-${Math.random()}`}
            contentContainerStyle={styles.cartList}
            showsVerticalScrollIndicator={false}
          />

          {/* Cart Summary and Checkout */}
          <View style={styles.cartFooter}>
            <View style={styles.cartSummary}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Items ({getTotalItems()})</Text>
                <Text style={styles.summaryValue}>{getTotalPrice().toFixed(2)} EGP</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Delivery Fee</Text>
                <Text style={styles.summaryValue}>5.00 EGP</Text>
              </View>
              <View style={[styles.summaryRow, styles.totalRow]}>
                <Text style={styles.totalLabel}>Total</Text>
                <Text style={styles.totalPrice}>{(getTotalPrice() + 5).toFixed(2)} EGP</Text>
              </View>
            </View>

            <TouchableOpacity
              style={styles.modernCheckoutButton}
              onPress={handleCheckout}
              activeOpacity={0.8}
            >
              <Ionicons name="card" size={20} color="white" style={{ marginRight: 8 }} />
              <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </SafeAreaView>
  );
}

// Favorites Screen - Exact replica of your Flutter app
function FavoritesScreen({ navigation }) {
  const { favorites, removeFromFavorites } = useFavorites();
  const { addToCart } = useCart();

  const handleAddToCart = (item) => {
    if (!item.is_available) {
      Alert.alert('Unavailable', 'This item is currently unavailable');
      return;
    }
    addToCart(item);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`);
  };

  const handleRemoveFromFavorites = (item) => {
    Alert.alert(
      'Remove from Favorites',
      `Remove ${item.name} from favorites?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => removeFromFavorites(item.id) }
      ]
    );
  };

  const renderFavoriteItem = ({ item }) => (
    <TouchableOpacity
      style={styles.modernFavoriteItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
      activeOpacity={0.8}
    >
      <View style={styles.favoriteItemCard}>
        <View style={styles.favoriteItemImageContainer}>
          <LinearGradient
            colors={['#FF6B35', '#FF8A65']}
            style={styles.favoriteItemImage}
          >
            <Text style={styles.favoriteItemEmoji}>🍽️</Text>
          </LinearGradient>
        </View>

        <View style={styles.favoriteItemContent}>
          <View style={styles.favoriteItemInfo}>
            <Text style={styles.favoriteItemName} numberOfLines={1}>{item.name}</Text>
            <Text style={styles.favoriteItemDescription} numberOfLines={2}>
              {item.description || 'Delicious food item'}
            </Text>
            <View style={styles.favoriteItemFooter}>
              <Text style={styles.favoriteItemPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
              <View style={styles.availabilityBadge}>
                <View style={[styles.statusDot, {
                  backgroundColor: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
                }]} />
                <Text style={[styles.availabilityText, {
                  color: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
                }]}>
                  {item.is_available ? 'Available' : 'Unavailable'}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.favoriteItemActions}>
            <TouchableOpacity
              style={[styles.favoriteAddButton, {
                opacity: item.is_available ? 1 : 0.5,
                backgroundColor: item.is_available ? ModernTheme.colors.primary : ModernTheme.colors.textSecondary
              }]}
              onPress={() => handleAddToCart(item)}
              disabled={!item.is_available}
              activeOpacity={0.8}
            >
              <Ionicons name="bag-add" size={18} color="white" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.favoriteRemoveButton}
              onPress={() => handleRemoveFromFavorites(item)}
              activeOpacity={0.8}
            >
              <Ionicons name="heart" size={18} color={ModernTheme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Favorites ({favorites.length})</Text>
        </View>
      </LinearGradient>

      {favorites.length === 0 ? (
        <View style={styles.emptyStateContainer}>
          <Ionicons name="heart-outline" size={80} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyStateTitle}>No favorites yet</Text>
          <Text style={styles.emptyStateText}>Add items to your favorites to see them here</Text>
          <TouchableOpacity
            style={styles.modernBrowseButton}
            onPress={() => navigation.navigate('Home')}
            activeOpacity={0.8}
          >
            <Ionicons name="restaurant" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={styles.browseButtonText}>Browse Cafeterias</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={favorites}
          renderItem={renderFavoriteItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.favoritesList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

// Search Screen - Exact replica of your Flutter app
function SearchScreen({ navigation }) {
  const { searchMenuItems } = useSupabase();
  const { addToCart } = useCart();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleSearch = async (query) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const results = await searchMenuItems(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (item) => {
    if (!item.is_available) {
      Alert.alert('Unavailable', 'This item is currently unavailable');
      return;
    }
    addToCart(item);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`);
  };

  const renderSearchResult = ({ item }) => (
    <TouchableOpacity
      style={styles.searchResultItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
      activeOpacity={0.8}
    >
      <View style={styles.searchResultCard}>
        <View style={styles.searchResultImageContainer}>
          <LinearGradient
            colors={['#FF6B35', '#FF8A65']}
            style={styles.searchResultImage}
          >
            <Text style={styles.searchResultEmoji}>🍽️</Text>
          </LinearGradient>
        </View>

        <View style={styles.searchResultContent}>
          <View style={styles.searchResultInfo}>
            <Text style={styles.searchResultName} numberOfLines={1}>{item.name}</Text>
            <Text style={styles.searchResultCafeteria} numberOfLines={1}>
              {item.cafeterias?.name || 'Unknown Cafeteria'}
            </Text>
            <Text style={styles.searchResultDescription} numberOfLines={2}>
              {item.description || 'Delicious food item'}
            </Text>
            <View style={styles.searchResultFooter}>
              <Text style={styles.searchResultPrice}>{item.price?.toFixed(2) || '0.00'} EGP</Text>
              <View style={styles.availabilityBadge}>
                <View style={[styles.statusDot, {
                  backgroundColor: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
                }]} />
                <Text style={[styles.availabilityText, {
                  color: item.is_available ? ModernTheme.colors.success : ModernTheme.colors.error
                }]}>
                  {item.is_available ? 'Available' : 'Unavailable'}
                </Text>
              </View>
            </View>
          </View>

          <TouchableOpacity
            style={[styles.searchResultAddButton, {
              opacity: item.is_available ? 1 : 0.5,
              backgroundColor: item.is_available ? ModernTheme.colors.primary : ModernTheme.colors.textSecondary
            }]}
            onPress={() => handleAddToCart(item)}
            disabled={!item.is_available}
            activeOpacity={0.8}
          >
            <Ionicons name="add" size={18} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.headerButton}
            activeOpacity={0.8}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Search</Text>
          <View style={styles.headerButton} />
        </View>
      </LinearGradient>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={ModernTheme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for food, dishes..."
            placeholderTextColor={ModernTheme.colors.textSecondary}
            value={searchQuery}
            onChangeText={(text) => {
              setSearchQuery(text);
              handleSearch(text);
            }}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery('');
                setSearchResults([]);
              }}
              activeOpacity={0.8}
            >
              <Ionicons name="close-circle" size={20} color={ModernTheme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Results */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={ModernTheme.colors.primary} />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      ) : searchQuery.length === 0 ? (
        <View style={styles.emptyStateContainer}>
          <Ionicons name="search-outline" size={80} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyStateTitle}>Search for food</Text>
          <Text style={styles.emptyStateText}>Find your favorite dishes and restaurants</Text>
        </View>
      ) : searchResults.length === 0 ? (
        <View style={styles.emptyStateContainer}>
          <Ionicons name="sad-outline" size={80} color={ModernTheme.colors.textSecondary} />
          <Text style={styles.emptyStateTitle}>No results found</Text>
          <Text style={styles.emptyStateText}>Try searching for something else</Text>
        </View>
      ) : (
        <FlatList
          data={searchResults}
          renderItem={renderSearchResult}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.searchResultsList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
}

// Profile Screen - Exact replica of your Flutter app
function ProfileScreen({ navigation }) {
  const { currentUser } = useSupabase();
  const { clearCart } = useCart();
  const { favorites } = useFavorites();

  const profileOptions = [
    {
      id: 'account',
      title: 'Account Settings',
      icon: 'person-outline',
      onPress: () => Alert.alert('Account Settings', 'Feature coming soon!')
    },
    {
      id: 'orders',
      title: 'Order History',
      icon: 'time-outline',
      onPress: () => Alert.alert('Order History', 'Feature coming soon!')
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: 'notifications-outline',
      onPress: () => Alert.alert('Notifications', 'Feature coming soon!')
    },
    {
      id: 'payment',
      title: 'Payment Methods',
      icon: 'card-outline',
      onPress: () => Alert.alert('Payment Methods', 'Feature coming soon!')
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => Alert.alert('Help & Support', 'Contact <NAME_EMAIL>')
    },
    {
      id: 'about',
      title: 'About Us',
      icon: 'information-circle-outline',
      onPress: () => Alert.alert('About UniEats', 'UniEats - Your modern food delivery experience')
    }
  ];

  const renderProfileOption = ({ item }) => (
    <TouchableOpacity
      style={styles.modernProfileOption}
      onPress={item.onPress}
      activeOpacity={0.8}
    >
      <View style={styles.profileOptionIcon}>
        <Ionicons name={item.icon} size={24} color={ModernTheme.colors.primary} />
      </View>
      <Text style={styles.profileOptionText}>{item.title}</Text>
      <Ionicons name="chevron-forward" size={20} color={ModernTheme.colors.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />

      {/* Header */}
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Profile</Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.modernProfileHeader}>
          <LinearGradient
            colors={['#FF6B35', '#FF8A65']}
            style={styles.profileAvatarContainer}
          >
            <Ionicons name="person" size={40} color="white" />
          </LinearGradient>
          <Text style={styles.profileName}>{currentUser?.fullName || 'Demo User'}</Text>
          <Text style={styles.profileEmail}>{currentUser?.email || '<EMAIL>'}</Text>

          {/* Stats */}
          <View style={styles.profileStats}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>Orders</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{favorites.length}</Text>
              <Text style={styles.statLabel}>Favorites</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>4.8</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('Favorites')}
              activeOpacity={0.8}
            >
              <Ionicons name="heart" size={24} color={ModernTheme.colors.primary} />
              <Text style={styles.quickActionText}>Favorites</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('Cart')}
              activeOpacity={0.8}
            >
              <Ionicons name="bag" size={24} color={ModernTheme.colors.primary} />
              <Text style={styles.quickActionText}>Cart</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => Alert.alert('Order History', 'Feature coming soon!')}
              activeOpacity={0.8}
            >
              <Ionicons name="time" size={24} color={ModernTheme.colors.primary} />
              <Text style={styles.quickActionText}>Orders</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Profile Options */}
        <View style={styles.profileOptionsContainer}>
          <Text style={styles.sectionTitle}>Settings</Text>
          <FlatList
            data={profileOptions}
            renderItem={renderProfileOption}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        </View>

        {/* App Info */}
        <View style={styles.appInfoContainer}>
          <Text style={styles.appVersion}>UniEats v1.0.0</Text>
          <Text style={styles.appDescription}>Modern food delivery experience</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

// Main Tab Navigator - Exact replica of your Flutter app
function MainTabNavigator() {
  const { getTotalItems } = useCart();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'Cart') {
            iconName = focused ? 'bag' : 'bag-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          // Cart badge
          if (route.name === 'Cart' && getTotalItems() > 0) {
            return (
              <View style={styles.tabIconContainer}>
                <Ionicons name={iconName} size={size} color={color} />
                <View style={styles.cartBadge}>
                  <Text style={styles.cartBadgeText}>
                    {getTotalItems() > 99 ? '99+' : getTotalItems()}
                  </Text>
                </View>
              </View>
            );
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: ModernTheme.colors.primary,
        tabBarInactiveTintColor: ModernTheme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: ModernTheme.colors.surface,
          borderTopWidth: 0,
          elevation: ModernTheme.elevation.high,
          shadowColor: ModernTheme.colors.cardShadow,
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.15,
          shadowRadius: 8,
          height: 65,
          paddingBottom: 10,
          paddingTop: 8,
          borderTopLeftRadius: ModernTheme.borderRadius.xl,
          borderTopRightRadius: ModernTheme.borderRadius.xl,
        },
        headerShown: false,
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarItemStyle: {
          paddingVertical: 5,
        },
      })}
    >
      <Tab.Screen name="Home" component={DashboardScreen} options={{ title: 'Home' }} />
      <Tab.Screen name="Favorites" component={FavoritesScreen} options={{ title: 'Favorites' }} />
      <Tab.Screen name="Cart" component={CartScreen} options={{ title: 'Cart' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
}

// Main Stack Navigator
function AppNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: ModernTheme.colors.background },
        gestureEnabled: true,
        gestureDirection: 'horizontal',
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      <Stack.Screen name="Cafeteria" component={CafeteriaScreen} />
      <Stack.Screen name="ItemDetails" component={ItemDetailsScreen} />
      <Stack.Screen name="Search" component={SearchScreen} />
    </Stack.Navigator>
  );
}

// Main App Component
export default function App() {
  useEffect(() => {
    console.log('🍽️ UniEats App Starting...');
    console.log('📡 Supabase URL:', SUPABASE_URL);
    console.log('🎨 Modern Theme Loaded');
  }, []);

  return (
    <SafeAreaProvider>
      <SupabaseProvider>
        <AuthProvider>
          <CartProvider>
            <FavoritesProvider>
              <NavigationContainer>
                <AppNavigator />
                <StatusBar style="light" backgroundColor={ModernTheme.colors.primary} />
              </NavigationContainer>
            </FavoritesProvider>
          </CartProvider>
        </AuthProvider>
      </SupabaseProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  // Base styles
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },

  // Header styles
  modernHeader: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  greeting: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
    marginBottom: 4,
    fontWeight: '500',
  },
  welcomeText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    lineHeight: 24,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  profileButton: {
    padding: 4,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearCartText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Content styles
  content: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },

  // Modern Search bar
  modernSearchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.borderRadius.xl,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginHorizontal: 20,
    marginTop: 20,
    elevation: ModernTheme.elevation.low,
    shadowColor: ModernTheme.colors.cardShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchPlaceholder: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    fontWeight: '500',
  },

  // Search Screen styles
  searchContainer: {
    padding: 20,
    backgroundColor: ModernTheme.colors.surface,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.background,
    borderRadius: ModernTheme.borderRadius.l,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: ModernTheme.elevation.low,
    shadowColor: ModernTheme.colors.cardShadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: ModernTheme.colors.text,
    marginLeft: 12,
    fontWeight: '500',
  },

  // Section styles
  section: {
    marginVertical: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    letterSpacing: 0.5,
  },
  seeAllText: {
    fontSize: 16,
    color: ModernTheme.colors.primary,
    fontWeight: '600',
  },

  // Modern Cafeteria styles
  cafeteriasHorizontalList: {
    paddingLeft: 20,
  },
  modernCafeteriaCard: {
    width: 280,
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: ModernTheme.borderRadius.l,
    elevation: ModernTheme.elevation.medium,
    shadowColor: ModernTheme.colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    overflow: 'hidden',
  },
  cafeteriaGradient: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cafeteriaImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cafeteriaEmoji: {
    fontSize: 32,
  },
  cafeteriaCardContent: {
    padding: 16,
  },
  modernCafeteriaName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 6,
  },
  modernCafeteriaDescription: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  modernCafeteriaFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginLeft: 4,
    fontWeight: '600',
  },
  deliveryTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryTime: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginLeft: 4,
    fontWeight: '500',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },

  // Cafeteria info header
  cafeteriaInfoHeader: {
    backgroundColor: ModernTheme.colors.surface,
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  cafeteriaInfoName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 8,
  },
  cafeteriaInfoDescription: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 12,
  },
  cafeteriaInfoDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  // Menu item styles
  menuList: {
    padding: 20,
  },
  menuItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuItemImageContainer: {
    marginRight: 15,
  },
  menuItemImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuItemEmoji: {
    fontSize: 30,
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  menuItemDescription: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 8,
  },
  menuItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },
  availabilityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  availabilityText: {
    fontSize: 12,
    color: ModernTheme.colors.textSecondary,
    marginLeft: 4,
  },
  addButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Item details styles
  itemDetailsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  itemImageLarge: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  itemEmojiLarge: {
    fontSize: 80,
  },
  itemNameLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 10,
    textAlign: 'center',
  },
  itemDescriptionLarge: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  itemPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 30,
  },
  itemPriceLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },

  // Quantity selector
  quantityContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  quantityLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 15,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginHorizontal: 20,
  },

  // Bottom container
  bottomContainer: {
    padding: 20,
    backgroundColor: ModernTheme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.colors.border,
  },
  addToCartButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  addToCartText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Tab navigation
  tabIconContainer: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: ModernTheme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },

  // Loading and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginTop: 20,
    marginBottom: 10,
  },
  emptyText: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  browseButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 25,
    paddingHorizontal: 30,
    paddingVertical: 15,
  },
  browseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },

  // Cart styles
  cartList: {
    padding: 20,
  },
  cartItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cartItemImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  cartItemEmoji: {
    fontSize: 24,
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  cartItemPrice: {
    fontSize: 14,
    color: ModernTheme.colors.primary,
    fontWeight: '600',
  },
  cartItemControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  cartQuantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartQuantityText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginHorizontal: 15,
  },
  removeButton: {
    padding: 5,
  },
  cartFooter: {
    backgroundColor: ModernTheme.colors.surface,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.colors.border,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
  },
  totalPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },
  checkoutButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  checkoutButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Favorites styles
  favoritesList: {
    padding: 20,
  },
  favoriteItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  favoriteItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  favoriteItemImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  favoriteItemEmoji: {
    fontSize: 24,
  },
  favoriteItemInfo: {
    flex: 1,
  },
  favoriteItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  favoriteItemDescription: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 4,
  },
  favoriteItemPrice: {
    fontSize: 14,
    color: ModernTheme.colors.primary,
    fontWeight: '600',
  },
  favoriteItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  favoriteAddButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  favoriteRemoveButton: {
    padding: 5,
  },

  // Profile styles
  profileContainer: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  profileHeader: {
    backgroundColor: ModernTheme.colors.surface,
    padding: 30,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: ModernTheme.colors.border,
  },
  profileAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 5,
  },
  profileEmail: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
  },
  profileOptions: {
    padding: 20,
  },
  profileOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  profileOptionText: {
    flex: 1,
    fontSize: 16,
    color: ModernTheme.colors.text,
    marginLeft: 15,
    fontWeight: '500',
  },
});
