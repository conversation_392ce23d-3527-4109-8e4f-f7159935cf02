import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image, FlatList } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

// Modern Theme
const ModernTheme = {
  colors: {
    primary: '#FF6B35',
    primaryDark: '#E55A2B',
    primaryLight: '#FFE5DC',
    secondary: '#4ECDC4',
    background: '#F8F9FA',
    surface: '#FFFFFF',
    text: '#2C3E50',
    textSecondary: '#7F8C8D',
    border: '#E9ECEF',
    warning: '#F39C12',
    success: '#27AE60',
    error: '#E74C3C',
  },
};

// Simple providers (we'll create these files later)
const AuthProvider = ({ children }) => children;
const SupabaseProvider = ({ children }) => children;
const CartProvider = ({ children }) => children;
const FavoritesProvider = ({ children }) => children;

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Sample data
const sampleCafeterias = [
  { id: 1, name: 'Campus Café', image: '🏪', rating: 4.5, deliveryTime: '15-20 min' },
  { id: 2, name: 'Student Bistro', image: '🍽️', rating: 4.3, deliveryTime: '20-25 min' },
  { id: 3, name: 'Quick Bites', image: '🍔', rating: 4.7, deliveryTime: '10-15 min' },
  { id: 4, name: 'Healthy Corner', image: '🥗', rating: 4.4, deliveryTime: '15-20 min' },
];

const sampleCategories = [
  { id: 1, name: 'Pizza', icon: '🍕' },
  { id: 2, name: 'Burgers', icon: '🍔' },
  { id: 3, name: 'Salads', icon: '🥗' },
  { id: 4, name: 'Drinks', icon: '🥤' },
];

// Modern Home Screen
function HomeScreen({ navigation }) {
  const renderCafeteria = ({ item }) => (
    <TouchableOpacity
      style={styles.cafeteriaCard}
      onPress={() => navigation.navigate('Cafeteria', { cafeteriaId: item.id, cafeteriaName: item.name })}
    >
      <View style={styles.cafeteriaImage}>
        <Text style={styles.cafeteriaEmoji}>{item.image}</Text>
      </View>
      <View style={styles.cafeteriaInfo}>
        <Text style={styles.cafeteriaName}>{item.name}</Text>
        <View style={styles.cafeteriaDetails}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={ModernTheme.colors.warning} />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>
          <Text style={styles.deliveryTime}>{item.deliveryTime}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCategory = ({ item }) => (
    <TouchableOpacity style={styles.categoryCard}>
      <Text style={styles.categoryIcon}>{item.icon}</Text>
      <Text style={styles.categoryName}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaProvider>
      <View style={styles.container}>
        <LinearGradient
          colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.greeting}>Good Morning!</Text>
              <Text style={styles.headerTitle}>What would you like to eat?</Text>
            </View>
            <TouchableOpacity style={styles.profileButton}>
              <Ionicons name="person-circle" size={40} color="white" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <View style={styles.content}>
          {/* Categories */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Categories</Text>
            <FlatList
              data={sampleCategories}
              renderItem={renderCategory}
              keyExtractor={(item) => item.id.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesList}
            />
          </View>

          {/* Popular Cafeterias */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Popular Cafeterias</Text>
            <FlatList
              data={sampleCafeterias}
              renderItem={renderCafeteria}
              keyExtractor={(item) => item.id.toString()}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 0 }}
            />
          </View>
        </View>
      </View>
    </SafeAreaProvider>
  );
}

// Cafeteria Screen
function CafeteriaScreen({ route, navigation }) {
  const { cafeteriaId, cafeteriaName } = route.params;

  const sampleMenuItems = [
    { id: 1, name: 'Margherita Pizza', price: 45, image: '🍕', rating: 4.5, description: 'Fresh tomatoes, mozzarella, basil' },
    { id: 2, name: 'Chicken Burger', price: 35, image: '🍔', rating: 4.3, description: 'Grilled chicken, lettuce, tomato' },
    { id: 3, name: 'Caesar Salad', price: 25, image: '🥗', rating: 4.7, description: 'Fresh lettuce, parmesan, croutons' },
    { id: 4, name: 'Pasta Carbonara', price: 40, image: '🍝', rating: 4.4, description: 'Creamy pasta with bacon' },
  ];

  const renderMenuItem = ({ item }) => (
    <TouchableOpacity
      style={styles.menuItem}
      onPress={() => navigation.navigate('ItemDetails', { item })}
    >
      <View style={styles.menuItemImage}>
        <Text style={styles.menuItemEmoji}>{item.image}</Text>
      </View>
      <View style={styles.menuItemInfo}>
        <Text style={styles.menuItemName}>{item.name}</Text>
        <Text style={styles.menuItemDescription}>{item.description}</Text>
        <View style={styles.menuItemFooter}>
          <Text style={styles.menuItemPrice}>{item.price} EGP</Text>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={ModernTheme.colors.warning} />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>
        </View>
      </View>
      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add" size={20} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{cafeteriaName}</Text>
          <TouchableOpacity>
            <Ionicons name="search" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <FlatList
        data={sampleMenuItems}
        renderItem={renderMenuItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.menuList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

// Item Details Screen
function ItemDetailsScreen({ route, navigation }) {
  const { item } = route.params;

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[ModernTheme.colors.primary, ModernTheme.colors.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Item Details</Text>
          <TouchableOpacity>
            <Ionicons name="heart-outline" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content}>
        <View style={styles.itemDetailsContainer}>
          <View style={styles.itemImageLarge}>
            <Text style={styles.itemEmojiLarge}>{item.image}</Text>
          </View>
          <Text style={styles.itemNameLarge}>{item.name}</Text>
          <Text style={styles.itemDescriptionLarge}>{item.description}</Text>
          <View style={styles.itemPriceContainer}>
            <Text style={styles.itemPriceLarge}>{item.price} EGP</Text>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={18} color={ModernTheme.colors.warning} />
              <Text style={styles.ratingText}>{item.rating}</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.bottomContainer}>
        <TouchableOpacity style={styles.addToCartButton}>
          <Text style={styles.addToCartText}>Add to Cart</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

// Search Screen
function SearchScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>🔍 Search</Text>
      <Text style={styles.description}>
        Find your favorite dishes and restaurants
      </Text>
    </View>
  );
}

// Cart Screen
function CartScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>🛒 Cart</Text>
      <Text style={styles.description}>
        Your cart is empty
      </Text>
    </View>
  );
}

// Favorites Screen
function FavoritesScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>❤️ Favorites</Text>
      <Text style={styles.description}>
        Your favorite dishes will appear here
      </Text>
    </View>
  );
}

// Profile Screen
function ProfileScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>👤 Profile</Text>
      <Text style={styles.description}>
        Manage your account and preferences
      </Text>
    </View>
  );
}

// Main Tab Navigator
function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Search') {
            iconName = focused ? 'search' : 'search-outline';
          } else if (route.name === 'Cart') {
            iconName = focused ? 'bag' : 'bag-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: ModernTheme.colors.primary,
        tabBarInactiveTintColor: ModernTheme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: ModernTheme.colors.surface,
          borderTopWidth: 0,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Search" component={SearchScreen} />
      <Tab.Screen name="Cart" component={CartScreen} />
      <Tab.Screen name="Favorites" component={FavoritesScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

// Main Stack Navigator
function AppNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: ModernTheme.colors.background },
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabNavigator} />
      <Stack.Screen name="Cafeteria" component={CafeteriaScreen} />
      <Stack.Screen name="ItemDetails" component={ItemDetailsScreen} />
    </Stack.Navigator>
  );
}

export default function App() {
  return (
    <SafeAreaProvider>
      <SupabaseProvider>
        <AuthProvider>
          <CartProvider>
            <FavoritesProvider>
              <NavigationContainer>
                <AppNavigator />
                <StatusBar style="light" />
              </NavigationContainer>
            </FavoritesProvider>
          </CartProvider>
        </AuthProvider>
      </SupabaseProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  headerTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 4,
  },
  profileButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
  },
  section: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginHorizontal: 20,
    marginBottom: 15,
  },
  categoriesList: {
    paddingHorizontal: 20,
  },
  categoryCard: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    padding: 20,
    marginRight: 15,
    alignItems: 'center',
    minWidth: 80,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  categoryIcon: {
    fontSize: 30,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
    color: ModernTheme.colors.text,
  },
  cafeteriaCard: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginHorizontal: 20,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cafeteriaImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  cafeteriaEmoji: {
    fontSize: 30,
  },
  cafeteriaInfo: {
    flex: 1,
  },
  cafeteriaName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 5,
  },
  cafeteriaDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  ratingText: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginLeft: 4,
  },
  deliveryTime: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
  },
  menuList: {
    padding: 20,
  },
  menuItem: {
    backgroundColor: ModernTheme.colors.surface,
    borderRadius: 15,
    marginBottom: 15,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuItemImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  menuItemEmoji: {
    fontSize: 30,
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 4,
  },
  menuItemDescription: {
    fontSize: 14,
    color: ModernTheme.colors.textSecondary,
    marginBottom: 8,
  },
  menuItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },
  addButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemDetailsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  itemImageLarge: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: ModernTheme.colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  itemEmojiLarge: {
    fontSize: 80,
  },
  itemNameLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.text,
    marginBottom: 10,
    textAlign: 'center',
  },
  itemDescriptionLarge: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  itemPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  itemPriceLarge: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
  },
  bottomContainer: {
    padding: 20,
    backgroundColor: ModernTheme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: ModernTheme.colors.border,
  },
  addToCartButton: {
    backgroundColor: ModernTheme.colors.primary,
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  addToCartText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  screenContainer: {
    flex: 1,
    backgroundColor: ModernTheme.colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: ModernTheme.colors.primary,
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: ModernTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
