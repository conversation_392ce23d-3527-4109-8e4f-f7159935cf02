import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

const Tab = createBottomTabNavigator();

// Simple Home Screen
function HomeScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>🍽️ UniEats</Text>
      <Text style={styles.subtitle}>Home</Text>
      <Text style={styles.description}>
        Discover delicious food from your favorite restaurants
      </Text>
      <TouchableOpacity style={styles.button}>
        <Text style={styles.buttonText}>Browse Restaurants</Text>
      </TouchableOpacity>
    </View>
  );
}

// Simple Search Screen
function SearchScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>🔍 Search</Text>
      <Text style={styles.description}>
        Find your favorite dishes and restaurants
      </Text>
    </View>
  );
}

// Simple Cart Screen
function CartScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>🛒 Cart</Text>
      <Text style={styles.description}>
        Your cart is empty
      </Text>
    </View>
  );
}

// Simple Favorites Screen
function FavoritesScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>❤️ Favorites</Text>
      <Text style={styles.description}>
        Your favorite dishes will appear here
      </Text>
    </View>
  );
}

// Simple Profile Screen
function ProfileScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text style={styles.title}>👤 Profile</Text>
      <Text style={styles.description}>
        Manage your account and preferences
      </Text>
    </View>
  );
}

export default function App() {
  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            if (route.name === 'Home') {
              iconName = focused ? 'home' : 'home-outline';
            } else if (route.name === 'Search') {
              iconName = focused ? 'search' : 'search-outline';
            } else if (route.name === 'Cart') {
              iconName = focused ? 'bag' : 'bag-outline';
            } else if (route.name === 'Favorites') {
              iconName = focused ? 'heart' : 'heart-outline';
            } else if (route.name === 'Profile') {
              iconName = focused ? 'person' : 'person-outline';
            }

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#FF6B35',
          tabBarInactiveTintColor: '#666',
          tabBarStyle: {
            backgroundColor: 'white',
            borderTopWidth: 1,
            borderTopColor: '#eee',
            paddingBottom: 5,
            paddingTop: 5,
            height: 60,
          },
          headerShown: false,
        })}
      >
        <Tab.Screen name="Home" component={HomeScreen} />
        <Tab.Screen name="Search" component={SearchScreen} />
        <Tab.Screen name="Cart" component={CartScreen} />
        <Tab.Screen name="Favorites" component={FavoritesScreen} />
        <Tab.Screen name="Profile" component={ProfileScreen} />
      </Tab.Navigator>
      <StatusBar style="auto" />
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF6B35',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 20,
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
