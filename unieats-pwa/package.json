{"name": "unieats-pwa", "version": "1.0.0", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@supabase/supabase-js": "^2.39.0", "styled-components": "^5.3.6", "framer-motion": "^10.16.0", "react-query": "^3.39.0", "workbox-webpack-plugin": "^6.5.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "serve -s build -l 3000"}, "devDependencies": {"react-scripts": "5.0.1", "serve": "^14.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}