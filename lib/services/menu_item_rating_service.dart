import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:unieatsappv0/models/supabase_models.dart';

class MenuItemRatingService {
  final SupabaseService _supabaseService = SupabaseService();

  /// Submit a rating for a menu item
  Future<void> submitRating({
    required String menuItemId,
    required String orderId,
    required int rating,
    String? comment,
  }) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        throw Exception('User not logged in');
      }

      // Check if user already rated this item for this order
      final existingRating = await _supabaseService.client
          .from('menu_item_ratings')
          .select('id')
          .eq('user_id', user.id)
          .eq('menu_item_id', menuItemId)
          .eq('order_id', orderId)
          .maybeSingle();

      if (existingRating != null) {
        // Update existing rating
        await _supabaseService.client
            .from('menu_item_ratings')
            .update({
              'rating': rating,
              'review_comment': comment,
            })
            .eq('id', existingRating['id']);
      } else {
        // Insert new rating
        await _supabaseService.client
            .from('menu_item_ratings')
            .insert({
              'user_id': user.id,
              'menu_item_id': menuItemId,
              'order_id': orderId,
              'rating': rating,
              'review_comment': comment,
            });
      }
    } catch (e) {
      debugPrint('Error submitting menu item rating: $e');
      rethrow;
    }
  }

  /// Get all ratings for a menu item
  Future<List<SupabaseMenuItemRating>> getMenuItemRatings(String menuItemId) async {
    try {
      final response = await _supabaseService.client
          .from('menu_item_ratings')
          .select('*')
          .eq('menu_item_id', menuItemId)
          .order('created_at', ascending: false);

      return response
          .map((item) => SupabaseMenuItemRating.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('Error getting menu item ratings: $e');
      return [];
    }
  }

  /// Get average rating for a menu item
  Future<Map<String, dynamic>> getMenuItemAverageRating(String menuItemId) async {
    try {
      final ratings = await getMenuItemRatings(menuItemId);

      if (ratings.isEmpty) {
        return {
          'average_rating': 0.0,
          'total_ratings': 0,
        };
      }

      final sum = ratings.fold(0, (sum, rating) => sum + rating.rating);
      final average = sum / ratings.length;

      return {
        'average_rating': average,
        'total_ratings': ratings.length,
      };
    } catch (e) {
      debugPrint('Error getting menu item average rating: $e');
      return {
        'average_rating': 0.0,
        'total_ratings': 0,
      };
    }
  }

  /// Get ratings for multiple menu items (for displaying in lists)
  Future<Map<String, Map<String, dynamic>>> getMultipleMenuItemRatings(List<String> menuItemIds) async {
    try {
      final response = await _supabaseService.client
          .from('menu_item_ratings')
          .select('menu_item_id, rating')
          .inFilter('menu_item_id', menuItemIds);

      final Map<String, List<int>> ratingsMap = {};

      // Group ratings by menu item
      for (final item in response) {
        final menuItemId = item['menu_item_id'] as String;
        final rating = item['rating'] as int;

        if (!ratingsMap.containsKey(menuItemId)) {
          ratingsMap[menuItemId] = [];
        }
        ratingsMap[menuItemId]!.add(rating);
      }

      // Calculate averages
      final Map<String, Map<String, dynamic>> result = {};
      for (final entry in ratingsMap.entries) {
        final ratings = entry.value;
        final sum = ratings.fold(0, (sum, rating) => sum + rating);
        final average = sum / ratings.length;

        result[entry.key] = {
          'average_rating': average,
          'total_ratings': ratings.length,
        };
      }

      // Add entries for menu items with no ratings
      for (final menuItemId in menuItemIds) {
        if (!result.containsKey(menuItemId)) {
          result[menuItemId] = {
            'average_rating': 0.0,
            'total_ratings': 0,
          };
        }
      }

      return result;
    } catch (e) {
      debugPrint('Error getting multiple menu item ratings: $e');
      return {};
    }
  }

  /// Check if user has rated a specific menu item for an order
  Future<SupabaseMenuItemRating?> getUserRatingForOrder({
    required String menuItemId,
    required String orderId,
  }) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) return null;

      final response = await _supabaseService.client
          .from('menu_item_ratings')
          .select('*')
          .eq('user_id', user.id)
          .eq('menu_item_id', menuItemId)
          .eq('order_id', orderId)
          .maybeSingle();

      if (response != null) {
        return SupabaseMenuItemRating.fromJson(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user rating for order: $e');
      return null;
    }
  }

  /// Get all ratings by a specific user
  Future<List<SupabaseMenuItemRating>> getUserRatings() async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) return [];

      final response = await _supabaseService.client
          .from('menu_item_ratings')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return response
          .map((item) => SupabaseMenuItemRating.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('Error getting user ratings: $e');
      return [];
    }
  }

  /// Delete a rating (if user wants to remove their rating)
  Future<void> deleteRating(String ratingId) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        throw Exception('User not logged in');
      }

      await _supabaseService.client
          .from('menu_item_rating')
          .delete()
          .eq('id', ratingId)
          .eq('user_id', user.id); // Ensure user can only delete their own ratings
    } catch (e) {
      debugPrint('Error deleting rating: $e');
      rethrow;
    }
  }
}
