import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// A service for managing real-time subscriptions to Supabase tables
class RealtimeService {
  static final RealtimeService _instance = RealtimeService._internal();
  factory RealtimeService() => _instance;
  RealtimeService._internal();

  final Map<String, RealtimeChannel> _channels = {};
  bool _initialized = false;

  // Get the Supabase client
  SupabaseClient get _client => Supabase.instance.client;

  // Initialize the service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      debugPrint('Initializing RealtimeService...');

      // Subscribe to all relevant tables
      await subscribeToTable('cafeterias');
      await subscribeToTable('menu_items');
      await subscribeToTable('orders');
      await subscribeToTable('order_items');
      await subscribeToTable('ratings');
      await subscribeToTable('favorites');
      await subscribeToTable('users');
      await subscribeToTable('cafeteria_feedback');
      await subscribeToTable('user_feedback');
      await subscribeToTable('chat_conversations');
      await subscribeToTable('chat_messages');
      await subscribeToTable('support_tickets');

      _initialized = true;
      debugPrint('RealtimeService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing RealtimeService: $e');
      rethrow;
    }
  }

  // Subscribe to a table with a callback
  Future<RealtimeChannel> subscribeToTable(
    String table, {
    Function(Map<String, dynamic>)? onInsert,
    Function(Map<String, dynamic>)? onUpdate,
    Function(Map<String, dynamic>)? onDelete,
  }) async {
    try {
      debugPrint('📡 Subscribing to table: $table');

      // Create a channel for this table
      final channel = _client.channel('realtime:$table');
      debugPrint('📺 Created channel for table: $table');

      // Set up the subscription using the same format as the web app
      channel
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: table,
            callback: (payload) {
              debugPrint('Real-time event for $table: ${payload.eventType}');

              switch (payload.eventType) {
                case PostgresChangeEvent.insert:
                  debugPrint(
                      '🆕 INSERT event for $table: ${payload.newRecord.toString()}');
                  if (onInsert != null) onInsert(payload.newRecord);
                  break;
                case PostgresChangeEvent.update:
                  debugPrint(
                      '🔄 UPDATE event for $table: ${payload.newRecord.toString()}');
                  if (onUpdate != null) onUpdate(payload.newRecord);
                  break;
                case PostgresChangeEvent.delete:
                  debugPrint(
                      '🗑️ DELETE event for $table: ${payload.oldRecord.toString()}');
                  if (onDelete != null) onDelete(payload.oldRecord);
                  break;
                case PostgresChangeEvent.all:
                  // This is just a subscription type, not an actual event
                  debugPrint(
                      '⚠️ Received ALL event type (unusual): ${payload.eventType}');
                  break;
              }
            },
          )
          .subscribe();

      // Store the channel
      _channels[table] = channel;

      debugPrint('✅ Successfully subscribed to table: $table');
      debugPrint('📊 Total active subscriptions: ${_channels.length}');
      return channel;
    } catch (e) {
      debugPrint('Error subscribing to table $table: $e');
      rethrow;
    }
  }

  // Unsubscribe from a table
  Future<void> unsubscribeFromTable(String table) async {
    try {
      final channel = _channels[table];
      if (channel != null) {
        await channel.unsubscribe();
        _channels.remove(table);
        debugPrint('Unsubscribed from table: $table');
      }
    } catch (e) {
      debugPrint('Error unsubscribing from table $table: $e');
    }
  }

  // Unsubscribe from all tables
  Future<void> unsubscribeAll() async {
    try {
      for (final channel in _channels.values) {
        await channel.unsubscribe();
      }
      _channels.clear();
      _initialized = false;
      debugPrint('Unsubscribed from all tables');
    } catch (e) {
      debugPrint('Error unsubscribing from all tables: $e');
    }
  }
}
