import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/menu_item.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class MenuService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'menu_items';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  // Create a new menu item
  Future<void> createMenuItem(MenuItem menuItem) async {
    try {
      await init();
      await _supabaseService.insert(_table, menuItem.toJson());
    } catch (e) {
      debugPrint('Error creating menu item: $e');
      rethrow;
    }
  }

  // Read a menu item by ID
  Future<MenuItem?> getMenuItemById(String menuItemId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('id', menuItemId)
          .maybeSingle();

      if (response != null) {
        return MenuItem.fromSupabase(response);
      } else {
        return null;
      }
    } catch (e) {
      debugPrint('Error getting menu item: $e');
      return null;
    }
  }

  // Update a menu item
  Future<void> updateMenuItem(String menuItemId, MenuItem menuItem) async {
    try {
      await init();
      await _supabaseService
          .update(_table, menuItem.toJson(), {'id': menuItemId});
    } catch (e) {
      debugPrint('Error updating menu item: $e');
      rethrow;
    }
  }

  // Delete a menu item
  Future<void> deleteMenuItem(String menuItemId) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': menuItemId});
    } catch (e) {
      debugPrint('Error deleting menu item: $e');
      rethrow;
    }
  }

  // Get menu items by cafeteria
  Future<List<MenuItem>> getMenuItemsByCafeteria(String cafeteriaId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('cafeteriaId', cafeteriaId);

      return response.map((data) => MenuItem.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting menu items by cafeteria: $e');
      return [];
    }
  }

  // Get menu items by category
  Future<List<MenuItem>> getMenuItemsByCategory(
      String cafeteriaId, String category) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('cafeteriaId', cafeteriaId)
          .eq('category', category);

      return response.map((data) => MenuItem.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting menu items by category: $e');
      return [];
    }
  }
}
