import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/admin.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class AdminService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'admins';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  // Create a new admin
  Future<void> createAdmin(Admin admin) async {
    try {
      await init();
      await _supabaseService.insert(_table, admin.toJson());
    } catch (e) {
      debugPrint('Error creating admin: $e');
      rethrow;
    }
  }

  // Get an admin by ID
  Future<Admin?> getAdminById(String adminId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('id', adminId)
          .maybeSingle();

      if (response != null) {
        return Admin.from<PERSON>son(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting admin by ID: $e');
      return null;
    }
  }

  // Update an existing admin
  Future<void> updateAdmin(String adminId, Admin admin) async {
    try {
      await init();
      await _supabaseService.update(_table, admin.toJson(), {'id': adminId});
    } catch (e) {
      debugPrint('Error updating admin: $e');
      rethrow;
    }
  }

  // Delete an admin
  Future<void> deleteAdmin(String adminId) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': adminId});
    } catch (e) {
      debugPrint('Error deleting admin: $e');
      rethrow;
    }
  }

  // Get all admins
  Future<List<Admin>> getAdmins() async {
    try {
      await init();
      final response = await _supabaseService.select(_table);
      return response.map((data) => Admin.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting admins: $e');
      return [];
    }
  }
}
