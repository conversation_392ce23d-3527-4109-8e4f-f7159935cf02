import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  late SharedPreferences _prefs;
  bool _initialized = false;

  Future<void> init() async {
    if (!_initialized) {
      _prefs = await SharedPreferences.getInstance();
      _initialized = true;
    }
  }

  // Login info methods
  Future<Map<String, String>?> getLoginInfo() async {
    await init();
    final email = _prefs.getString('login_email');
    final password = _prefs.getString('login_password');

    if (email != null && password != null) {
      return {
        'email': email,
        'password': password,
      };
    }
    return null;
  }

  Future<bool> saveLoginInfo(String email, String password) async {
    await init();
    await _prefs.setString('login_email', email);
    return await _prefs.setString('login_password', password);
  }

  Future<bool> clearLoginInfo() async {
    await init();
    await _prefs.remove('login_email');
    return await _prefs.remove('login_password');
  }

  // Profile picture methods
  Future<String?> getProfilePicturePath() async {
    await init();
    return _prefs.getString('profile_picture_path');
  }

  Future<String> saveImageToLocalStorage(File imageFile) async {
    final directory = await getApplicationDocumentsDirectory();
    final uuid = const Uuid().v4();
    final fileName = 'profile_$uuid${path.extension(imageFile.path)}';
    final savedImage = await imageFile.copy('${directory.path}/$fileName');

    // Save the path to SharedPreferences
    await _prefs.setString('profile_picture_path', savedImage.path);

    return savedImage.path;
  }

  // SharedPreferences methods
  Future<bool> saveString(String key, String value) async {
    await init();
    return await _prefs.setString(key, value);
  }

  String? getString(String key) {
    if (!_initialized) {
      debugPrint('LocalStorageService not initialized');
      return null;
    }
    return _prefs.getString(key);
  }

  Future<bool> saveBool(String key, bool value) async {
    await init();
    return await _prefs.setBool(key, value);
  }

  bool? getBool(String key) {
    if (!_initialized) {
      debugPrint('LocalStorageService not initialized');
      return null;
    }
    return _prefs.getBool(key);
  }

  Future<bool> saveInt(String key, int value) async {
    await init();
    return await _prefs.setInt(key, value);
  }

  int? getInt(String key) {
    if (!_initialized) {
      debugPrint('LocalStorageService not initialized');
      return null;
    }
    return _prefs.getInt(key);
  }

  Future<bool> saveDouble(String key, double value) async {
    await init();
    return await _prefs.setDouble(key, value);
  }

  double? getDouble(String key) {
    if (!_initialized) {
      debugPrint('LocalStorageService not initialized');
      return null;
    }
    return _prefs.getDouble(key);
  }

  Future<bool> saveStringList(String key, List<String> value) async {
    await init();
    return await _prefs.setStringList(key, value);
  }

  List<String>? getStringList(String key) {
    if (!_initialized) {
      debugPrint('LocalStorageService not initialized');
      return null;
    }
    return _prefs.getStringList(key);
  }

  Future<bool> saveMap(String key, Map<String, dynamic> value) async {
    await init();
    return await _prefs.setString(key, jsonEncode(value));
  }

  Map<String, dynamic>? getMap(String key) {
    if (!_initialized) {
      debugPrint('LocalStorageService not initialized');
      return null;
    }
    final String? jsonString = _prefs.getString(key);
    if (jsonString == null) return null;
    return jsonDecode(jsonString) as Map<String, dynamic>;
  }

  Future<bool> remove(String key) async {
    await init();
    return await _prefs.remove(key);
  }

  Future<bool> clear() async {
    await init();
    return await _prefs.clear();
  }

  // File storage methods
  Future<String> get _localPath async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  Future<File> _localFile(String fileName) async {
    final path = await _localPath;
    return File('$path/$fileName');
  }

  Future<File> writeFile(String fileName, String data) async {
    final file = await _localFile(fileName);
    return file.writeAsString(data);
  }

  Future<String> readFile(String fileName) async {
    try {
      final file = await _localFile(fileName);
      return await file.readAsString();
    } catch (e) {
      return '';
    }
  }

  Future<bool> deleteFile(String fileName) async {
    try {
      final file = await _localFile(fileName);
      await file.delete();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> fileExists(String fileName) async {
    try {
      final file = await _localFile(fileName);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }
}
