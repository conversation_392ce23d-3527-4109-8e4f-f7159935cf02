import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/models/supabase_models.dart';

/// Supabase-based cart service for real-time cart sync across devices
class SupabaseCartService {
  static final SupabaseCartService _instance = SupabaseCartService._internal();
  factory SupabaseCartService() => _instance;
  SupabaseCartService._internal();

  SupabaseClient get client => Supabase.instance.client;
  User? get currentUser => client.auth.currentUser;
  bool get isAuthenticated => currentUser != null;

  // Real-time subscription
  RealtimeChannel? _cartSubscription;

  /// Initialize cart service with real-time subscriptions
  Future<void> initialize() async {
    if (!isAuthenticated) return;

    try {
      // Subscribe to cart changes for current user
      _cartSubscription = client
          .channel('user_cart_${currentUser!.id}')
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'user_carts',
            filter: PostgresChangeFilter(
              type: PostgresChangeFilterType.eq,
              column: 'user_id',
              value: currentUser!.id,
            ),
          )
          .subscribe();

      debugPrint('SupabaseCartService: Initialized with real-time sync');
    } catch (e) {
      debugPrint('Error initializing SupabaseCartService: $e');
    }
  }

  /// Get user's cart items from Supabase
  Future<List<CartItem>> getCartItems() async {
    if (!isAuthenticated) return [];

    try {
      final response = await client
          .from('user_carts')
          .select('''
            *,
            menu_items(
              id,
              name,
              price,
              image_url,
              cafeteria_id,
              cafeterias(name)
            )
          ''')
          .eq('user_id', currentUser!.id)
          .order('created_at', ascending: false);

      return response.map<CartItem>((item) {
        final menuItem = item['menu_items'];
        final cafeteria = menuItem['cafeterias'];
        
        return CartItem(
          id: '${item['menu_item_id']}_${item['id']}',
          menuItem: SupabaseMenuItem(
            id: menuItem['id'],
            name: menuItem['name'],
            price: (menuItem['price'] ?? 0.0).toDouble(),
            imageUrl: menuItem['image_url'],
            cafeteriaId: menuItem['cafeteria_id'],
            description: '',
            category: '',
            isAvailable: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          quantity: item['quantity'] ?? 1,
          customizations: List<String>.from(item['customizations'] ?? []),
          notes: item['notes'],
          name: menuItem['name'],
          price: (menuItem['price'] ?? 0.0).toDouble(),
          image: menuItem['image_url'],
          cafeteriaName: cafeteria['name'] ?? 'Unknown',
          buildingName: 'Campus',
        );
      }).toList();
    } catch (e) {
      debugPrint('Error getting cart items: $e');
      return [];
    }
  }

  /// Add item to cart in Supabase
  Future<bool> addToCart(CartItem cartItem) async {
    if (!isAuthenticated) return false;

    try {
      // Check if item already exists in cart
      final existingItems = await client
          .from('user_carts')
          .select('id, quantity')
          .eq('user_id', currentUser!.id)
          .eq('menu_item_id', cartItem.menuItem.id);

      if (existingItems.isNotEmpty) {
        // Update existing item quantity
        final existingItem = existingItems.first;
        final newQuantity = (existingItem['quantity'] ?? 0) + cartItem.quantity;
        
        await client
            .from('user_carts')
            .update({
              'quantity': newQuantity,
              'customizations': cartItem.customizations,
              'notes': cartItem.notes,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', existingItem['id']);
      } else {
        // Add new item
        await client.from('user_carts').insert({
          'user_id': currentUser!.id,
          'menu_item_id': cartItem.menuItem.id,
          'quantity': cartItem.quantity,
          'customizations': cartItem.customizations,
          'notes': cartItem.notes,
          'created_at': DateTime.now().toIso8601String(),
        });
      }

      return true;
    } catch (e) {
      debugPrint('Error adding to cart: $e');
      return false;
    }
  }

  /// Update cart item quantity
  Future<bool> updateCartItemQuantity(String menuItemId, int quantity) async {
    if (!isAuthenticated) return false;

    try {
      if (quantity <= 0) {
        return await removeFromCart(menuItemId);
      }

      await client
          .from('user_carts')
          .update({
            'quantity': quantity,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', currentUser!.id)
          .eq('menu_item_id', menuItemId);

      return true;
    } catch (e) {
      debugPrint('Error updating cart item quantity: $e');
      return false;
    }
  }

  /// Remove item from cart
  Future<bool> removeFromCart(String menuItemId) async {
    if (!isAuthenticated) return false;

    try {
      await client
          .from('user_carts')
          .delete()
          .eq('user_id', currentUser!.id)
          .eq('menu_item_id', menuItemId);

      return true;
    } catch (e) {
      debugPrint('Error removing from cart: $e');
      return false;
    }
  }

  /// Clear entire cart
  Future<bool> clearCart() async {
    if (!isAuthenticated) return false;

    try {
      await client
          .from('user_carts')
          .delete()
          .eq('user_id', currentUser!.id);

      return true;
    } catch (e) {
      debugPrint('Error clearing cart: $e');
      return false;
    }
  }

  /// Get cart summary (totals)
  Future<Map<String, double>> getCartSummary() async {
    final cartItems = await getCartItems();
    
    double subtotal = 0.0;
    for (final item in cartItems) {
      subtotal += item.calculateItemTotal();
    }
    
    final serviceFee = subtotal * 0.04 > 20.0 ? 20.0 : subtotal * 0.04;
    final total = subtotal + serviceFee;

    return {
      'subtotal': subtotal,
      'serviceFee': serviceFee,
      'total': total,
    };
  }

  /// Convert cart to order items for checkout
  Future<List<Map<String, dynamic>>> getOrderItems() async {
    final cartItems = await getCartItems();
    
    return cartItems.map((item) => {
      'menu_item_id': item.menuItem.id,
      'quantity': item.quantity,
      'price': item.price,
      'notes': item.notes,
      'customizations': item.customizations,
    }).toList();
  }

  /// Subscribe to cart changes
  void subscribeToCartChanges(Function(List<CartItem>) onCartChanged) {
    if (!isAuthenticated) return;

    _cartSubscription?.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'user_carts',
      filter: PostgresChangeFilter(
        type: PostgresChangeFilterType.eq,
        column: 'user_id',
        value: currentUser!.id,
      ),
    ).listen((payload) async {
      // Refresh cart items and notify listeners
      final updatedItems = await getCartItems();
      onCartChanged(updatedItems);
    });
  }

  /// Cleanup subscriptions
  void dispose() {
    if (_cartSubscription != null) {
      client.removeChannel(_cartSubscription!);
      _cartSubscription = null;
    }
  }
}
