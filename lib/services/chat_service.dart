import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/services/simple_auth_service.dart';
import 'package:unieatsappv0/models/chat_models.dart';
import 'package:uuid/uuid.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ChatService {
  static final ChatService _instance = ChatService._internal();
  factory ChatService() => _instance;
  ChatService._internal();

  final SimpleAuthService _authService = SimpleAuthService();

  // Get Supabase client directly
  SupabaseClient get _client => Supabase.instance.client;

  // Table names
  static const String _conversationsTable = 'chat_conversations';
  static const String _messagesTable = 'chat_messages';

  /// Generate a unique ticket number
  String _generateTicketNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'TKT-$timestamp-$random';
  }

  /// Test authentication and database connection
  Future<bool> testConnection() async {
    try {
      debugPrint('Testing ChatService connection...');

      // Test authentication with detailed logging
      debugPrint('SimpleAuthService authenticated: ${_authService.isAuthenticated}');
      debugPrint('SimpleAuthService currentUser: ${_authService.currentUser?.email}');
      debugPrint('Supabase auth currentUser: ${_client.auth.currentUser?.email}');

      final user = _authService.currentUser ?? _client.auth.currentUser;
      if (user == null) {
        debugPrint('No authenticated user found in either service');
        return false;
      }

      debugPrint('User authenticated: ${user.email} (${user.id})');

      // Test database connection
      final testQuery = await _client.from('profiles').select('id').limit(1);
      debugPrint('Database connection test successful: ${testQuery.length} profiles found');

      // Test if user has a profile
      final userProfile = await _client
          .from('profiles')
          .select('id, full_name, role')
          .eq('id', user.id)
          .maybeSingle();

      if (userProfile != null) {
        debugPrint('User profile found: ${userProfile['full_name']} (${userProfile['role']})');
      } else {
        debugPrint('No profile found for user ${user.id}');
      }

      return true;
    } catch (e) {
      debugPrint('Connection test failed: $e');
      return false;
    }
  }

  /// Create a new support conversation
  Future<ChatConversation?> createConversation({
    required String subject,
    required String category,
    String priority = 'medium',
    String? orderId,
  }) async {
    try {
      // Check authentication with multiple methods
      var user = _authService.currentUser;

      // If SimpleAuthService doesn't have a user, try direct Supabase auth
      if (user == null) {
        debugPrint('SimpleAuthService currentUser is null, checking Supabase auth directly');
        user = _client.auth.currentUser;
      }

      if (user == null) {
        debugPrint('Cannot create conversation: User not logged in in any service');
        debugPrint('SimpleAuthService authenticated: ${_authService.isAuthenticated}');
        debugPrint('Supabase auth user: ${_client.auth.currentUser?.email}');
        return null;
      }

      debugPrint('Creating conversation for user: ${user.id} (${user.email})');
      debugPrint('Subject: $subject, Category: $category, Priority: $priority');

      // Check if user has a profile
      final profileCheck = await _client
          .from('profiles')
          .select('id, role')
          .eq('id', user.id)
          .maybeSingle();

      if (profileCheck == null) {
        debugPrint('User does not have a profile. Creating one...');
        // Create a basic profile for the user
        await _client
            .from('profiles')
            .insert({
              'id': user.id,
              'full_name': user.email?.split('@')[0] ?? 'Mobile User',
              'role': 'student',
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            });
        debugPrint('Created profile for user');
      } else {
        debugPrint('User has profile with role: ${profileCheck['role']}');
      }

      // Create support ticket first
      final ticketResult = await _client
          .from('support_tickets')
          .insert({
            'ticket_number': _generateTicketNumber(),
            'user_id': user.id,
            'title': subject,
            'description': 'Support conversation: $subject',
            'category': category,
            'priority': priority,
            'user_type': 'student',
            'order_id': orderId,
          })
          .select()
          .single();

      debugPrint('Created support ticket: ${ticketResult['ticket_number']}');

      // Create conversation linked to the ticket
      final conversationResult = await _client
          .from(_conversationsTable)
          .insert({
            'user_id': user.id,
            'subject': subject,
            'priority': priority,
            'category': category,
            'order_id': orderId,
            'ticket_id': ticketResult['id'],
            'user_type': 'student',
          })
          .select()
          .single();

      debugPrint('Created conversation successfully: ${conversationResult['id']}');
      return ChatConversation.fromJson(conversationResult);
    } catch (e) {
      debugPrint('Detailed error creating conversation: $e');
      debugPrint('Error type: ${e.runtimeType}');

      // Print the full error details for debugging
      if (e is Exception) {
        debugPrint('Exception details: ${e.toString()}');
      }

      return null;
    }
  }

  /// Get all conversations for the current user
  Future<List<ChatConversation>> getUserConversations() async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        debugPrint('Cannot get conversations: User not logged in');
        return [];
      }

      final response = await _client
          .from(_conversationsTable)
          .select()
          .eq('user_id', user.id)
          .order('updated_at', ascending: false);

      return response.map((json) => ChatConversation.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting user conversations: $e');
      return [];
    }
  }

  /// Send a message in a conversation
  Future<ChatMessage?> sendMessage({
    required String conversationId,
    required String content,
    String messageType = 'text',
    String? fileUrl,
    String? fileName,
    int? fileSize,
  }) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        debugPrint('Cannot send message: User not logged in');
        return null;
      }

      final messageId = const Uuid().v4();
      final now = DateTime.now();

      final messageData = {
        'id': messageId,
        'conversation_id': conversationId,
        'sender_id': user.id,
        'message_type': messageType,
        'content': content,
        'file_url': fileUrl,
        'file_name': fileName,
        'file_size': fileSize,
        'is_read': false,
        'created_at': now.toIso8601String(),
      };

      final response = await _client
          .from(_messagesTable)
          .insert(messageData)
          .select()
          .single();

      // Update conversation's updated_at timestamp
      await _client
          .from(_conversationsTable)
          .update({'updated_at': now.toIso8601String()})
          .eq('id', conversationId);

      // Also update the related support ticket status if it exists
      try {
        await _client
            .from('support_tickets')
            .update({
              'status': 'in_progress',
              'updated_at': now.toIso8601String(),
            })
            .eq('user_id', user.id)
            .eq('status', 'open'); // Only update if still open
      } catch (e) {
        debugPrint('Note: Could not update related support ticket: $e');
      }

      return ChatMessage.fromJson(response);
    } catch (e) {
      debugPrint('Error sending message: $e');
      return null;
    }
  }

  /// Get messages for a conversation
  Future<List<ChatMessage>> getConversationMessages(String conversationId) async {
    try {
      final response = await _client
          .from(_messagesTable)
          .select()
          .eq('conversation_id', conversationId)
          .order('created_at', ascending: true);

      return response.map((json) => ChatMessage.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting conversation messages: $e');
      return [];
    }
  }

  /// Mark messages as read
  Future<void> markMessagesAsRead(String conversationId) async {
    try {
      final user = _authService.currentUser;
      if (user == null) return;

      await _client
          .from(_messagesTable)
          .update({'is_read': true})
          .eq('conversation_id', conversationId)
          .neq('sender_id', user.id); // Don't mark own messages as read
    } catch (e) {
      debugPrint('Error marking messages as read: $e');
    }
  }

  /// Close a conversation
  Future<bool> closeConversation(String conversationId) async {
    try {
      await _client
          .from(_conversationsTable)
          .update({
            'status': 'closed',
            'closed_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', conversationId);

      return true;
    } catch (e) {
      debugPrint('Error closing conversation: $e');
      return false;
    }
  }

  /// Rate a conversation
  Future<bool> rateConversation(String conversationId, int rating, String? feedback) async {
    try {
      await _client
          .from(_conversationsTable)
          .update({
            'rating': rating,
            'feedback': feedback,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', conversationId);

      return true;
    } catch (e) {
      debugPrint('Error rating conversation: $e');
      return false;
    }
  }

  /// Subscribe to real-time updates for a conversation
  void subscribeToConversation(String conversationId, Function(ChatMessage) onNewMessage) {
    debugPrint('Setting up real-time subscription for conversation $conversationId');

    // Real-time subscription functionality can be implemented here
    // For now, the chat works with manual refresh when new messages are sent
  }

  /// Unsubscribe from conversation updates
  void unsubscribeFromConversation(String conversationId) {
    try {
      _client.removeChannel(
        _client.channel('conversation_$conversationId'),
      );
    } catch (e) {
      debugPrint('Error unsubscribing from conversation: $e');
    }
  }
}
