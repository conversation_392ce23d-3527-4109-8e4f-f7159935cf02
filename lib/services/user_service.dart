import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/models/app_user.dart';
import 'package:unieatsappv0/services/local_storage_service.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class UserService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'profiles';
  final LocalStorageService _localStorageService = LocalStorageService();
  bool _initialized = false;

  // Initialize the service
  Future<void> init() async {
    if (!_initialized) {
      await _localStorageService.init();
      await _supabaseService.init();
      _initialized = true;
    }
  }

  // Create a new user
  Future<void> createUser(AppUser user) async {
    try {
      await _supabaseService.insert(_table, user.toJson());
    } catch (e) {
      debugPrint('Error creating user: $e');
      rethrow;
    }
  }

  // Read a user by ID
  Future<AppUser?> getUser(String userId) async {
    try {
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('id', userId)
          .maybeSingle();

      if (response != null) {
        return AppUser.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      debugPrint('Error getting user: $e');
      return null;
    }
  }

  // Update a user
  Future<void> updateUser(String userId, AppUser user) async {
    try {
      await _supabaseService.update(_table, user.toJson(), {'id': userId});
    } catch (e) {
      debugPrint('Error updating user: $e');
      rethrow;
    }
  }

  // Delete a user
  Future<void> deleteUser(String userId) async {
    try {
      await _supabaseService.delete(_table, {'id': userId});
    } catch (e) {
      debugPrint('Error deleting user: $e');
      rethrow;
    }
  }

  // Get all users
  Future<List<AppUser>> getUsers() async {
    try {
      final response = await _supabaseService.select(_table);
      return response.map((data) => AppUser.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting users: $e');
      return [];
    }
  }

  // Get user profile from local storage
  Future<Map<String, dynamic>> getUserProfile() async {
    await init();
    final profile = _localStorageService.getMap('user_profile');
    return profile ?? {};
  }

  // Save user profile to local storage
  Future<void> saveUserProfile({
    required String name,
    required String email,
    String? phone,
    String? profilePicture,
  }) async {
    await init();
    final profile = {
      'name': name,
      'email': email,
      'phone': phone,
      'profilePicture': profilePicture,
    };
    await _localStorageService.saveMap('user_profile', profile);
  }

  // Get user settings from local storage
  Future<Map<String, dynamic>> getSettings() async {
    await init();
    final settings = _localStorageService.getMap('user_settings');
    return settings ??
        {
          'notifications': true,
          'dark_mode': false,
          'language': 'en',
        };
  }

  // Save user settings to local storage
  Future<void> saveSettings({
    required bool notifications,
    required bool darkMode,
    String language = 'en',
  }) async {
    await init();
    final settings = {
      'notifications': notifications,
      'dark_mode': darkMode,
      'language': language,
    };
    await _localStorageService.saveMap('user_settings', settings);

    // Also update shared preferences for theme
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', darkMode);
  }
}
