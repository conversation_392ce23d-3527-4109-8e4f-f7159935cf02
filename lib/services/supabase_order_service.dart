import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:unieatsappv0/services/simple_auth_service.dart';
import 'package:unieatsappv0/utils/order_status_utils.dart';
import 'package:uuid/uuid.dart';

class SupabaseOrderService {
  static final SupabaseOrderService _instance =
      SupabaseOrderService._internal();
  factory SupabaseOrderService() => _instance;
  SupabaseOrderService._internal();

  final SupabaseService _supabaseService = SupabaseService();
  final SimpleAuthService _authService = SimpleAuthService();

  // Table names
  static const String _ordersTable = 'orders';
  static const String _orderItemsTable = 'order_items';

  /// Create a new order with items
  Future<SupabaseOrder?> createOrder({
    required String cafeteriaId,
    required double totalAmount,
    required List<SupabaseOrderItem> items,
  }) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        debugPrint('Cannot create order: User not logged in');
        return null;
      }

      final orderId = const Uuid().v4();
      final now = DateTime.now();

      // Calculate revenue breakdown
      final subtotal = totalAmount;
      final serviceFeePercentage = 4.0;
      final userServiceFee = subtotal * (serviceFeePercentage / 100);
      final cafeteriaCommission = subtotal * 0.96; // 96% to cafeteria
      final adminRevenue = userServiceFee; // Service fee goes to admin
      final finalTotal = subtotal + userServiceFee;

      // Create the order
      final orderData = {
        'id': orderId,
        'student_id': user.id, // Use student_id for student reference
        'user_id': user.id, // Also set user_id for web app compatibility
        'cafeteria_id': cafeteriaId,
        'status': 'pending', // Use pending status to match database
        'subtotal': subtotal,
        'service_fee_percentage': serviceFeePercentage,
        'user_service_fee': userServiceFee,
        'cafeteria_commission': cafeteriaCommission,
        'admin_revenue': adminRevenue,
        'total_amount': finalTotal, // Include service fee in total
        'platform': 'mobile', // Indicate this came from mobile app
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      debugPrint('Inserting order data: $orderData');
      final orderResult = await _supabaseService.client.from(_ordersTable).insert(orderData);
      debugPrint('Order insert result: $orderResult');

      // Create order items
      final orderItemsData = items
          .map((item) => {
                'id': const Uuid().v4(),
                'order_id': orderId,
                'item_id': item.menuItemId, // Use item_id to match actual database schema
                'quantity': item.quantity,
                'price': item.price, // Add price field
                'selected_variant': item.notes, // Use selected_variant to match schema
              })
          .toList();

      debugPrint('Inserting order items data: $orderItemsData');
      final itemsResult = await _supabaseService.client
          .from(_orderItemsTable)
          .insert(orderItemsData);
      debugPrint('Order items insert result: $itemsResult');

      // Update the order to trigger fee calculation now that items are inserted
      debugPrint('Updating order to trigger fee calculation...');
      final updateResult = await _supabaseService.client
          .from(_ordersTable)
          .update({'updated_at': DateTime.now().toIso8601String()})
          .eq('id', orderId);
      debugPrint('Order update result: $updateResult');

      // Return the created order with items
      return SupabaseOrder(
        id: orderId,
        userId: user.id,
        cafeteriaId: cafeteriaId,
        totalAmount: totalAmount,
        status: 'pending', // Use pending status to match database
        createdAt: now,
        updatedAt: now,
        items: items
            .map((item) => SupabaseOrderItem(
                  id: const Uuid()
                      .v4(), // This will be different from the actual IDs in the database
                  orderId: orderId,
                  menuItemId: item.menuItemId,
                  quantity: item.quantity,
                  price: item.price,
                  notes: item.notes,
                ))
            .toList(),
      );
    } catch (e, stackTrace) {
      debugPrint('Error creating order: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// Create order with specific user ID (for when user is authenticated via SimpleAuthProvider)
  Future<SupabaseOrder?> createOrderWithUserId({
    required String userId,
    required String cafeteriaId,
    required double totalAmount,
    required List<SupabaseOrderItem> items,
    String? orderNumber,
    String? pickupTime,
    String? paymentMethod,
  }) async {
    try {
      final orderId = const Uuid().v4();
      final now = DateTime.now();

      // Convert pickup time string to timestamp if provided
      DateTime? pickupDateTime;
      if (pickupTime != null && pickupTime.isNotEmpty) {
        try {
          // Parse time like "12:30 PM" and set it for today
          final timeFormat = RegExp(r'(\d{1,2}):(\d{2})\s*(AM|PM)', caseSensitive: false);
          final match = timeFormat.firstMatch(pickupTime);
          if (match != null) {
            int hour = int.parse(match.group(1)!);
            int minute = int.parse(match.group(2)!);
            String period = match.group(3)!.toUpperCase();

            // Convert to 24-hour format
            if (period == 'PM' && hour != 12) hour += 12;
            if (period == 'AM' && hour == 12) hour = 0;

            // Set pickup time for today
            final today = DateTime.now();
            pickupDateTime = DateTime(today.year, today.month, today.day, hour, minute);
          }
        } catch (e) {
          debugPrint('Error parsing pickup time: $e');
        }
      }

      // Calculate revenue breakdown
      final subtotal = totalAmount;
      final serviceFeePercentage = 4.0;
      final userServiceFee = subtotal * (serviceFeePercentage / 100);
      final cafeteriaCommission = subtotal * 0.96; // 96% to cafeteria
      final adminRevenue = userServiceFee; // Service fee goes to admin
      final finalTotal = subtotal + userServiceFee;

      // Create the order with the provided user ID and revenue fields
      final orderData = {
        'id': orderId,
        'student_id': userId, // Use provided user ID for student reference
        'user_id': userId, // Also set user_id for web app compatibility
        'cafeteria_id': cafeteriaId,
        'status': 'pending', // Use pending status to match database
        'subtotal': subtotal,
        'service_fee_percentage': serviceFeePercentage,
        'user_service_fee': userServiceFee,
        'cafeteria_commission': cafeteriaCommission,
        'admin_revenue': adminRevenue,
        'total_amount': finalTotal, // Include service fee in total
        'platform': 'mobile', // Indicate this came from mobile app
        'order_number': orderNumber ?? orderId.substring(0, 8).toUpperCase(), // Use provided order number or generate from ID
        'pickup_time': pickupDateTime?.toIso8601String(), // Add pickup time as timestamp
        'payment_method': paymentMethod ?? 'card', // Add payment method
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      debugPrint('Inserting order data with user ID $userId: $orderData');
      final orderResult = await _supabaseService.client.from(_ordersTable).insert(orderData);
      debugPrint('Order insert result: $orderResult');

      // Create order items
      final orderItemsData = items
          .map((item) => {
                'id': const Uuid().v4(),
                'order_id': orderId,
                'item_id': item.menuItemId, // Use item_id to match actual database schema
                'quantity': item.quantity,
                'price': item.price, // Add price field
                'selected_variant': item.notes, // Use selected_variant to match schema
              })
          .toList();



      debugPrint('Inserting order items data: $orderItemsData');
      final itemsResult = await _supabaseService.client
          .from(_orderItemsTable)
          .insert(orderItemsData);
      debugPrint('Order items insert result: $itemsResult');

      // Update the order to trigger fee calculation now that items are inserted
      debugPrint('Updating order to trigger fee calculation...');
      final updateResult = await _supabaseService.client
          .from(_ordersTable)
          .update({'updated_at': DateTime.now().toIso8601String()})
          .eq('id', orderId);
      debugPrint('Order update result: $updateResult');

      // Create notification for cafeteria owner about new order
      try {
        await _supabaseService.client.rpc('create_order_notification', params: {
          'cafeteria_id_param': cafeteriaId,
          'order_number_param': orderNumber ?? orderId.substring(0, 8).toUpperCase(),
          'customer_name_param': null, // Can be enhanced to include customer name
        });
      } catch (e) {
        debugPrint('Error creating order notification: $e');
        // Don't fail the order creation if notification fails
      }

      // Return the created order with items
      return SupabaseOrder(
        id: orderId,
        userId: userId,
        cafeteriaId: cafeteriaId,
        totalAmount: totalAmount,
        status: 'pending', // Use pending status to match database
        createdAt: now,
        updatedAt: now,
        orderNumber: orderNumber ?? orderId.substring(0, 8).toUpperCase(),
        pickupTime: pickupTime,
        paymentMethod: paymentMethod ?? 'card',
        items: items
            .map((item) => SupabaseOrderItem(
                  id: const Uuid()
                      .v4(), // This will be different from the actual IDs in the database
                  orderId: orderId,
                  menuItemId: item.menuItemId,
                  quantity: item.quantity,
                  price: item.price,
                  notes: item.notes,
                ))
            .toList(),
      );
    } catch (e, stackTrace) {
      debugPrint('Error creating order with user ID: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// Get all orders for the current user
  Future<List<SupabaseOrder>> getUserOrders() async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        debugPrint('❌ Cannot get orders: User not logged in');
        return [];
      }

      debugPrint('🔍 Getting orders for user: ${user.id} (${user.email})');
      debugPrint('🔍 User auth status: ${_authService.isAuthenticated}');

      debugPrint('🔍 Querying orders with filter: user_id.eq.${user.id} OR student_id.eq.${user.id}');

      final response = await _supabaseService.client
          .from(_ordersTable)
          .select()
          .or('user_id.eq.${user.id},student_id.eq.${user.id}')
          .order('created_at', ascending: false);

      debugPrint('🔍 Raw response from database: ${response.length} orders found');

      // Debug: Show first few orders with their user IDs
      for (final orderData in response.take(3)) {
        debugPrint('   📦 Order ${orderData['id']}: user_id=${orderData['user_id']}, student_id=${orderData['student_id']}');
      }

      final orders =
          response.map((json) => SupabaseOrder.fromJson(json)).toList();

      // Fetch order items for each order
      final List<SupabaseOrder> ordersWithItems = [];
      for (var order in orders) {
        debugPrint('🔍 Fetching items for order: ${order.id}');

        final itemsResponse = await _supabaseService.client
            .from(_orderItemsTable)
            .select('''
              *,
              menu_items!order_items_item_id_fkey(
                id,
                name,
                price,
                image_url,
                cafeteria_id,
                cafeterias!menu_items_cafeteria_id_fkey(name, location)
              )
            ''')
            .eq('order_id', order.id);

        final items = itemsResponse
            .map((json) => SupabaseOrderItem.fromJson(json))
            .toList();

        debugPrint('📦 Found ${items.length} items for order ${order.id}');
        for (final item in items) {
          debugPrint('   - Item: ${item.menuItemId} (qty: ${item.quantity}, price: ${item.price})');
        }

        // Create a new order with items
        final orderWithItems = SupabaseOrder(
          id: order.id,
          userId: order.userId,
          cafeteriaId: order.cafeteriaId,
          totalAmount: order.totalAmount,
          status: order.status,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          orderNumber: order.orderNumber,
          pickupTime: order.pickupTime,
          paymentMethod: order.paymentMethod,
          rating: order.rating,
          reviewComment: order.reviewComment,
          items: items,
        );

        ordersWithItems.add(orderWithItems);
      }

      debugPrint('✅ Loaded ${ordersWithItems.length} orders with items');
      return ordersWithItems;
    } catch (e) {
      debugPrint('Error getting user orders: $e');
      return [];
    }
  }

  /// Get an order by ID with its items
  Future<SupabaseOrder?> getOrderById(String orderId) async {
    try {
      final orderResponse = await _supabaseService.client
          .from(_ordersTable)
          .select()
          .eq('id', orderId)
          .single();

      final order = SupabaseOrder.fromJson(orderResponse);

      // Fetch order items with menu item and cafeteria details
      final itemsResponse = await _supabaseService.client
          .from(_orderItemsTable)
          .select('''
            *,
            menu_items!order_items_item_id_fkey(
              id,
              name,
              price,
              image_url,
              cafeteria_id,
              cafeterias!menu_items_cafeteria_id_fkey(name, location)
            )
          ''')
          .eq('order_id', orderId);

      final items = itemsResponse
          .map((json) => SupabaseOrderItem.fromJson(json))
          .toList();

      // Return the order with items
      return SupabaseOrder(
        id: order.id,
        userId: order.userId,
        cafeteriaId: order.cafeteriaId,
        totalAmount: order.totalAmount,
        status: order.status,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
        orderNumber: order.orderNumber,
        pickupTime: order.pickupTime,
        paymentMethod: order.paymentMethod,
        items: items,
      );
    } catch (e) {
      debugPrint('Error getting order by ID: $e');
      return null;
    }
  }

  /// Update order status
  Future<SupabaseOrder?> updateOrderStatus(
      String orderId, String status) async {
    try {
      // Get current order to check if status transition is valid
      final currentOrder = await getOrderById(orderId);
      if (currentOrder == null) {
        debugPrint('Cannot update status: Order not found');
        return null;
      }

      // Convert display status to database status if needed
      final dbStatus = OrderStatusUtils.getDatabaseStatus(status);

      // Check if status transition is valid
      if (!OrderStatusUtils.canUpdateStatus(currentOrder.status, dbStatus)) {
        debugPrint('Invalid status transition: ${currentOrder.status} -> $dbStatus');
        return null;
      }

      // Update the order status
      await _supabaseService.client.from(_ordersTable).update({
        'status': dbStatus,
        'updated_at': DateTime.now().toIso8601String(),
        // If status is completed, set is_picked_up to true
        if (dbStatus.toLowerCase() == 'completed') 'is_picked_up': true,
      }).eq('id', orderId);

      return await getOrderById(orderId);
    } catch (e) {
      debugPrint('Error updating order status: $e');
      return null;
    }
  }

  /// Get orders for a cafeteria (admin function)
  Future<List<SupabaseOrder>> getCafeteriaOrders(String cafeteriaId) async {
    try {
      final response = await _supabaseService.client
          .from(_ordersTable)
          .select()
          .eq('cafeteria_id', cafeteriaId)
          .order('created_at', ascending: false);

      return response.map((json) => SupabaseOrder.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting cafeteria orders: $e');
      return [];
    }
  }
}
