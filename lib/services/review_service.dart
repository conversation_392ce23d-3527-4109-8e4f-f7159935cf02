import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/review.dart';
import 'auth_service.dart';

class ReviewService {
  static const String baseUrl = String.fromEnvironment('API_URL', defaultValue: 'http://localhost:3000');
  final AuthService _authService = AuthService();

  /// Submit a review for an order item
  Future<bool> submitReview({
    required String orderId,
    required String cafeteriaId,
    required String itemId,
    required int rating,
    String? comment,
    bool isAnonymous = false,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('No auth token available');
        return false;
      }

      final response = await http.post(
        Uri.parse('$baseUrl/api/reviews'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'order_id': orderId,
          'cafeteria_id': cafeteriaId,
          'item_id': itemId,
          'rating': rating,
          'comment': comment,
          'is_anonymous': isAnonymous,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        debugPrint('Submit review failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Submit review error: $e');
      return false;
    }
  }

  /// Get reviews for a cafeteria
  Future<List<Review>> getCafeteriaReviews(String cafeteriaId, {int page = 1, int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reviews?cafeteriaId=$cafeteriaId&page=$page&limit=$limit'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['reviews'] != null) {
          return (data['reviews'] as List)
              .map((reviewJson) => Review.fromJson(reviewJson))
              .toList();
        }
      } else {
        debugPrint('Get cafeteria reviews failed: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Get cafeteria reviews error: $e');
    }
    return [];
  }

  /// Get reviews for a menu item
  Future<List<Review>> getItemReviews(String itemId, {int page = 1, int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reviews?itemId=$itemId&page=$page&limit=$limit'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['reviews'] != null) {
          return (data['reviews'] as List)
              .map((reviewJson) => Review.fromJson(reviewJson))
              .toList();
        }
      } else {
        debugPrint('Get item reviews failed: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Get item reviews error: $e');
    }
    return [];
  }

  /// Get user's own reviews
  Future<List<Review>> getUserReviews({int page = 1, int limit = 20}) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('No auth token available');
        return [];
      }

      final response = await http.get(
        Uri.parse('$baseUrl/api/reviews/user?page=$page&limit=$limit'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['reviews'] != null) {
          return (data['reviews'] as List)
              .map((reviewJson) => Review.fromJson(reviewJson))
              .toList();
        }
      } else {
        debugPrint('Get user reviews failed: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Get user reviews error: $e');
    }
    return [];
  }

  /// Get review statistics for a cafeteria
  Future<ReviewStats?> getCafeteriaReviewStats(String cafeteriaId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reviews/stats/cafeteria/$cafeteriaId'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ReviewStats.fromJson(data);
      } else {
        debugPrint('Get cafeteria review stats failed: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Get cafeteria review stats error: $e');
    }
    return null;
  }

  /// Get review statistics for a menu item
  Future<ReviewStats?> getItemReviewStats(String itemId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/reviews/stats/item/$itemId'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ReviewStats.fromJson(data);
      } else {
        debugPrint('Get item review stats failed: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Get item review stats error: $e');
    }
    return null;
  }

  /// Check if user can review an order
  Future<bool> canReviewOrder(String orderId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('No auth token available');
        return false;
      }

      final response = await http.get(
        Uri.parse('$baseUrl/api/reviews/can-review/$orderId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['can_review'] == true;
      } else {
        debugPrint('Check can review failed: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Check can review error: $e');
    }
    return false;
  }

  /// Update an existing review
  Future<bool> updateReview({
    required String reviewId,
    required int rating,
    String? comment,
    bool? isAnonymous,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('No auth token available');
        return false;
      }

      final response = await http.put(
        Uri.parse('$baseUrl/api/reviews/$reviewId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'rating': rating,
          'comment': comment,
          'is_anonymous': isAnonymous,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        debugPrint('Update review failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Update review error: $e');
      return false;
    }
  }

  /// Delete a review
  Future<bool> deleteReview(String reviewId) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        debugPrint('No auth token available');
        return false;
      }

      final response = await http.delete(
        Uri.parse('$baseUrl/api/reviews/$reviewId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        debugPrint('Delete review failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Delete review error: $e');
      return false;
    }
  }
}

/// Review statistics model
class ReviewStats {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution;

  ReviewStats({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
  });

  factory ReviewStats.fromJson(Map<String, dynamic> json) {
    return ReviewStats(
      averageRating: (json['average_rating'] ?? 0.0).toDouble(),
      totalReviews: json['total_reviews'] ?? 0,
      ratingDistribution: Map<int, int>.from(json['rating_distribution'] ?? {}),
    );
  }
}
