import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';

/// Service class for Supabase operations
class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  // Flag to track initialization
  bool _initialized = false;

  // Supabase client - not using late to avoid initialization errors
  SupabaseClient? _clientInstance;

  // Get the client, initializing if necessary
  SupabaseClient get client {
    if (_clientInstance == null) {
      throw Exception('Supabase client not initialized. Call init() first.');
    }
    return _clientInstance!;
  }

  /// Initialize Supabase client
  Future<void> init() async {
    // If already initialized, just return
    if (_initialized) {
      debugPrint('SupabaseService: Already initialized');
      return;
    }

    try {
      debugPrint('SupabaseService: Starting initialization');

      // Check if Supabase is already initialized globally
      bool supabaseInitialized = false;
      try {
        Supabase.instance.client;
        supabaseInitialized = true;
        debugPrint('SupabaseService: Supabase already initialized globally');
      } catch (e) {
        debugPrint('SupabaseService: Supabase not initialized globally: $e');
      }

      // If Supabase is already initialized globally, use the existing client
      if (supabaseInitialized) {
        _clientInstance = Supabase.instance.client;
        debugPrint('SupabaseService: Using existing Supabase client');
      } else {
        // Initialize Supabase
        debugPrint('SupabaseService: Initializing Supabase globally');
        await Supabase.initialize(
          url: 'https://lqtnaxvqkoynaziiinqh.supabase.co',
          anonKey:
              'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxxdG5heHZxa295bmF6aWlpbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1ODUzMjcsImV4cCI6MjA2MzE2MTMyN30.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA',
          debug: true,
        );
        _clientInstance = Supabase.instance.client;
        debugPrint('SupabaseService: Supabase initialized globally');
      }

      // Test the connection
      try {
        final testResponse =
            await _clientInstance!.from('cafeterias').select().limit(1);
        debugPrint(
            'SupabaseService: Test query successful: ${testResponse.length} cafeterias found');
      } catch (e) {
        debugPrint('SupabaseService: Test query failed: $e');
      }

      _initialized = true;
      debugPrint('SupabaseService: Initialization completed successfully');
    } catch (e) {
      debugPrint('SupabaseService: Error initializing: $e');
      rethrow;
    }
  }

  /// Get Supabase auth instance
  GoTrueClient get auth => client.auth;

  /// Get Supabase storage instance
  SupabaseStorageClient get storage => client.storage;

  /// Sign up a new user
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    return await auth.signUp(
      email: email,
      password: password,
      data: data,
    );
  }

  /// Sign in a user with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    return await auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  /// Sign out the current user
  Future<void> signOut() async {
    await auth.signOut();
  }

  /// Get the current user
  User? get currentUser => auth.currentUser;

  /// Check if a user is signed in
  bool get isAuthenticated => auth.currentUser != null;

  /// Insert data into a table
  Future<List<Map<String, dynamic>>> insert(
      String table, Map<String, dynamic> data) async {
    final response = await client.from(table).insert(data).select();
    return response;
  }

  /// Update data in a table
  Future<List<Map<String, dynamic>>> update(String table,
      Map<String, dynamic> data, Map<String, dynamic> conditions) async {
    var query = client.from(table).update(data);

    conditions.forEach((key, value) {
      query = query.eq(key, value);
    });

    final response = await query.select();
    return response;
  }

  /// Delete data from a table
  Future<List<Map<String, dynamic>>> delete(
      String table, Map<String, dynamic> conditions) async {
    var query = client.from(table).delete();

    conditions.forEach((key, value) {
      query = query.eq(key, value);
    });

    final response = await query.select();
    return response;
  }

  /// Select data from a table
  Future<List<Map<String, dynamic>>> select(
    String table, {
    List<String>? columns,
    Map<String, dynamic>? conditions,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    try {
      // Build the query string
      String queryStr = columns?.join(',') ?? '*';

      // Execute the query
      var query = client.from(table).select(queryStr);

      // Apply conditions if any
      if (conditions != null) {
        for (var entry in conditions.entries) {
          query = query.eq(entry.key, entry.value);
        }
      }

      // Get the data
      var data = await query;
      return data;
    } catch (e) {
      debugPrint('Error in select: $e');
      rethrow;
    }
  }
}
