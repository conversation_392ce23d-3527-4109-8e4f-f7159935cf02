import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/services/supabase_service.dart';
import 'package:uuid/uuid.dart';

/// Service to help migrate data from Firebase to Supabase
class MigrationService {
  static final MigrationService _instance = MigrationService._internal();
  factory MigrationService() => _instance;
  MigrationService._internal();

  final SupabaseService _supabaseService = SupabaseService();
  final _uuid = const Uuid();

  /// Migrate a user from Firebase to Supabase
  Future<bool> migrateUser(Map<String, dynamic> firebaseUser) async {
    try {
      // Check if user already exists in Supabase
      final email = firebaseUser['email'] as String?;
      if (email == null) {
        debugPrint('Cannot migrate user: Email is null');
        return false;
      }

      // Generate a random password for the user
      // They will need to reset it later
      final password = _uuid.v4();

      // Create the user in Supabase Auth
      final authResponse = await _supabaseService.signUp(
        email: email,
        password: password,
        data: {
          'full_name': firebaseUser['displayName'] ?? 'User',
        },
      );

      if (authResponse.user == null) {
        debugPrint('Failed to create user in Supabase Auth');
        return false;
      }

      // Create user profile in Supabase
      final userId = authResponse.user!.id;
      final profileData = {
        'id': userId,
        'email': email,
        'full_name': firebaseUser['displayName'] ?? 'User',
        'avatar_url': firebaseUser['photoURL'],
        'created_at': DateTime.now().toIso8601String(),
      };

      await _supabaseService.insert('profiles', profileData);
      debugPrint('User migrated successfully: $email');
      return true;
    } catch (e) {
      debugPrint('Error migrating user: $e');
      return false;
    }
  }

  /// Migrate a cafeteria from Firebase to Supabase
  Future<String?> migrateCafeteria(
      Map<String, dynamic> firebaseCafeteria) async {
    try {
      final cafeteriaId = _uuid.v4();
      final cafeteriaData = {
        'id': cafeteriaId,
        'name': firebaseCafeteria['name'] ?? 'Unknown Cafeteria',
        'description': firebaseCafeteria['description'],
        'image_url': firebaseCafeteria['imageUrl'],
        'location': firebaseCafeteria['location'],
        'rating': firebaseCafeteria['rating'],
        'created_at': DateTime.now().toIso8601String(),
        'is_active': true,
      };

      await _supabaseService.insert('cafeterias', cafeteriaData);
      debugPrint('Cafeteria migrated successfully: ${cafeteriaData['name']}');
      return cafeteriaId;
    } catch (e) {
      debugPrint('Error migrating cafeteria: $e');
      return null;
    }
  }

  /// Migrate a menu item from Firebase to Supabase
  Future<String?> migrateMenuItem(
      Map<String, dynamic> firebaseMenuItem, String cafeteriaId) async {
    try {
      final menuItemId = _uuid.v4();
      final menuItemData = {
        'id': menuItemId,
        'cafeteria_id': cafeteriaId,
        'name': firebaseMenuItem['name'] ?? 'Unknown Item',
        'description': firebaseMenuItem['description'],
        'price': firebaseMenuItem['price'] ?? 0.0,
        'image_url': firebaseMenuItem['imageUrl'],
        'is_available': firebaseMenuItem['isAvailable'] ?? true,
        'category': firebaseMenuItem['category'],
        'created_at': DateTime.now().toIso8601String(),
      };

      await _supabaseService.insert('menu_items', menuItemData);
      debugPrint('Menu item migrated successfully: ${menuItemData['name']}');
      return menuItemId;
    } catch (e) {
      debugPrint('Error migrating menu item: $e');
      return null;
    }
  }

  /// Migrate an order from Firebase to Supabase
  Future<String?> migrateOrder(Map<String, dynamic> firebaseOrder,
      String userId, String cafeteriaId) async {
    try {
      final orderId = _uuid.v4();
      final orderData = {
        'id': orderId,
        'user_id': userId,
        'cafeteria_id': cafeteriaId,
        'total_amount': firebaseOrder['totalAmount'] ?? 0.0,
        'status': firebaseOrder['status'] ?? 'pending',
        'created_at': firebaseOrder['timestamp'] != null
            ? DateTime.parse(firebaseOrder['timestamp']).toIso8601String()
            : DateTime.now().toIso8601String(),
      };

      await _supabaseService.insert('orders', orderData);

      // Migrate order items if available
      final items = firebaseOrder['items'] as List<dynamic>?;
      if (items != null) {
        for (final item in items) {
          final orderItemData = {
            'id': _uuid.v4(),
            'order_id': orderId,
            'menu_item_id': item['menuItemId'],
            'quantity': item['quantity'] ?? 1,
            'price': item['price'] ?? 0.0,
            'notes': item['notes'],
          };

          await _supabaseService.insert('order_items', orderItemData);
        }
      }

      debugPrint('Order migrated successfully: $orderId');
      return orderId;
    } catch (e) {
      debugPrint('Error migrating order: $e');
      return null;
    }
  }

  /// Migrate a rating from Firebase to Supabase
  Future<String?> migrateRating(Map<String, dynamic> firebaseRating,
      String userId, String cafeteriaId) async {
    try {
      final ratingId = _uuid.v4();
      final ratingData = {
        'id': ratingId,
        'user_id': userId,
        'cafeteria_id': cafeteriaId,
        'rating': firebaseRating['rating'] ?? 5,
        'comment': firebaseRating['comment'],
        'created_at': firebaseRating['timestamp'] != null
            ? DateTime.parse(firebaseRating['timestamp']).toIso8601String()
            : DateTime.now().toIso8601String(),
      };

      await _supabaseService.insert('ratings', ratingData);
      debugPrint('Rating migrated successfully: $ratingId');
      return ratingId;
    } catch (e) {
      debugPrint('Error migrating rating: $e');
      return null;
    }
  }
}
