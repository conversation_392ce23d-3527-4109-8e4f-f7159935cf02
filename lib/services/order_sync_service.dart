import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import '../providers/order_provider.dart';
import '../providers/order_history_provider.dart';
import '../providers/simple_auth_provider.dart';
import '../models/order.dart';
import '../models/cart_item.dart';
import '../services/realtime_service.dart';
import '../services/supabase_service_new.dart';
import '../utils/order_status_utils.dart';

/// Service to sync order updates from Supabase real-time to local providers
class OrderSyncService {
  static final OrderSyncService _instance = OrderSyncService._internal();
  factory OrderSyncService() => _instance;
  OrderSyncService._internal();

  final SupabaseService _supabaseService = SupabaseService();
  bool _initialized = false;
  BuildContext? _context;

  /// Initialize the service with context for provider access
  Future<void> initialize(BuildContext context) async {
    if (_initialized) {
      debugPrint('⚠️ OrderSyncService already initialized, skipping...');
      return;
    }

    _context = context;

    try {
      debugPrint('🚀 Initializing OrderSyncService...');

      // Subscribe to order updates from real-time service
      final realtimeService = RealtimeService();
      debugPrint('📡 Subscribing to orders table for real-time updates...');

      await realtimeService.subscribeToTable(
        'orders',
        onUpdate: _handleOrderUpdate,
        onInsert: _handleOrderInsert,
      );

      _initialized = true;
      debugPrint('✅ OrderSyncService initialized successfully - Real-time order updates active!');
    } catch (e) {
      debugPrint('❌ Error initializing OrderSyncService: $e');
    }
  }

  /// Handle order updates from real-time subscription
  void _handleOrderUpdate(Map<String, dynamic> orderData) async {
    if (_context == null || !_context!.mounted) {
      debugPrint('⚠️ Context is null or not mounted, skipping order update');
      return;
    }

    try {
      debugPrint('🔄 REAL-TIME ORDER UPDATE RECEIVED: $orderData');

      // Get the current user to check if this order belongs to them
      final simpleAuthProvider = Provider.of<SimpleAuthProvider>(_context!, listen: false);
      if (!simpleAuthProvider.isAuthenticated || simpleAuthProvider.currentUser == null) {
        debugPrint('❌ User not authenticated, ignoring order update');
        return;
      }

      final currentUserId = simpleAuthProvider.currentUser!.id;
      final orderUserId = orderData['student_id'] ?? orderData['user_id'];

      debugPrint('👤 Current user ID: $currentUserId');
      debugPrint('📦 Order user ID: $orderUserId');

      // Only process orders for the current user
      if (orderUserId != currentUserId) {
        debugPrint('❌ Order update not for current user, ignoring');
        return;
      }

      debugPrint('✅ Order belongs to current user, processing update...');

      // Convert Supabase order data to local Order model
      final updatedOrder = await convertSupabaseOrderToLocal(orderData);
      if (updatedOrder == null) {
        debugPrint('❌ Failed to convert Supabase order to local order');
        return;
      }

      debugPrint('✅ Order converted successfully: ${updatedOrder.orderNumber} - Status: ${updatedOrder.status}');

      // Update the order in providers - use a safer approach
      try {
        final orderProvider = Provider.of<OrderProvider>(_context!, listen: false);
        final orderHistoryProvider = Provider.of<OrderHistoryProvider>(_context!, listen: false);

        debugPrint('📝 Updating providers with order ${updatedOrder.orderNumber} status: ${updatedOrder.status}');

        // Try to find existing order by order number first
        Order? existingOrder = orderProvider.getOrderByNumber(updatedOrder.orderNumber);
        Order finalUpdatedOrder = updatedOrder;

        // If not found by order number, try to find by ID (for compatibility)
        if (existingOrder == null) {
          debugPrint('🔍 Order not found by number ${updatedOrder.orderNumber}, trying to find by ID...');
          try {
            existingOrder = orderProvider.orders.where((order) => order.id == updatedOrder.id).first;
            debugPrint('✅ Found order by ID: ${existingOrder.id} (Order Number: ${existingOrder.orderNumber})');
            // Update the order number to match what's in the database
            finalUpdatedOrder = Order(
              id: updatedOrder.id,
              orderNumber: existingOrder.orderNumber, // Keep the original order number
              userId: updatedOrder.userId,
              orderDate: updatedOrder.orderDate,
              items: updatedOrder.items,
              totalPrice: updatedOrder.totalPrice,
              pickupTime: updatedOrder.pickupTime,
              status: updatedOrder.status,
              paymentMethod: updatedOrder.paymentMethod,
              rating: updatedOrder.rating,
              comment: updatedOrder.comment,
            );
            debugPrint('🔄 Updated order number to match existing: ${finalUpdatedOrder.orderNumber}');
          } catch (e) {
            debugPrint('❌ Order not found by ID either: ${updatedOrder.id}');
          }
        }

        // Update in current orders
        orderProvider.updateOrder(finalUpdatedOrder, context: _context!);
        debugPrint('✅ OrderProvider updated');

        // Update in order history
        orderHistoryProvider.updateOrder(finalUpdatedOrder);
        debugPrint('✅ OrderHistoryProvider updated');

        // Verify the update worked
        final verifyOrder = orderProvider.getOrderByNumber(finalUpdatedOrder.orderNumber);
        if (verifyOrder != null) {
          debugPrint('✅ Verification: Order ${verifyOrder.orderNumber} now has status: ${verifyOrder.status}');
        } else {
          debugPrint('❌ Verification failed: Order not found in provider');
        }
      } catch (e) {
        debugPrint('⚠️ Error updating providers (context may be disposed): $e');
        // Continue anyway - the update was successful in Supabase
      }

      debugPrint('🎉 Successfully updated order ${updatedOrder.orderNumber} with status ${updatedOrder.status}');
    } catch (e) {
      debugPrint('❌ Error handling order update: $e');
    }
  }

  /// Handle new order inserts (less common for mobile app)
  void _handleOrderInsert(Map<String, dynamic> orderData) async {
    debugPrint('Received new order insert: $orderData');
    // For now, we don't need to handle inserts since orders are created locally first
  }

  /// Convert Supabase order data to local Order model
  Future<Order?> convertSupabaseOrderToLocal(Map<String, dynamic> orderData) async {
    try {
      // Get order items from Supabase
      final orderItems = await _getOrderItems(orderData['id']);
      if (orderItems.isEmpty) {
        debugPrint('No order items found for order ${orderData['id']}');
        return null;
      }

      // Convert to local Order model
      final dbStatus = orderData['status'] ?? 'pending';
      final displayStatus = OrderStatusUtils.getDisplayStatus(dbStatus);

      debugPrint('🔄 Converting order status: DB="$dbStatus" → Display="$displayStatus"');

      // Determine order number - try multiple approaches for compatibility
      String orderNumber;
      if (orderData['order_number'] != null && orderData['order_number'].toString().isNotEmpty) {
        orderNumber = orderData['order_number'].toString();
        debugPrint('📋 Using order_number from DB: $orderNumber');
      } else {
        orderNumber = orderData['id'].substring(0, 8).toUpperCase();
        debugPrint('📋 Generated order number from ID: $orderNumber');
      }

      // Also try the full ID as order number for compatibility
      final fullIdOrderNumber = orderData['id'];
      debugPrint('🆔 Full order ID: $fullIdOrderNumber');
      debugPrint('🔢 Final order number: $orderNumber');

      final order = Order(
        id: orderData['id'],
        orderNumber: orderNumber,
        userId: orderData['student_id'] ?? orderData['user_id'],
        orderDate: DateTime.parse(orderData['created_at']),
        items: orderItems,
        totalPrice: (orderData['total_amount'] ?? 0.0).toDouble(),
        pickupTime: _extractPickupTime(orderData),
        status: displayStatus,
        paymentMethod: orderData['payment_method'] ?? 'card',
        rating: orderData['rating'],
        comment: orderData['review_comment'],
      );

      return order;
    } catch (e) {
      debugPrint('Error converting Supabase order to local: $e');
      return null;
    }
  }

  /// Get order items for an order
  Future<List<CartItem>> _getOrderItems(String orderId) async {
    try {
      final response = await _supabaseService.client
          .from('order_items')
          .select('''
            *,
            menu_items!order_items_item_id_fkey(
              id,
              name,
              price,
              image_url,
              cafeteria_id,
              cafeterias!menu_items_cafeteria_id_fkey(name, location)
            )
          ''')
          .eq('order_id', orderId);

      final List<CartItem> items = [];
      for (final itemData in response) {
        final menuItem = itemData['menu_items'];
        if (menuItem != null) {
          final cafeteria = menuItem['cafeterias'];
          final item = CartItem(
            id: menuItem['id'],
            name: menuItem['name'],
            price: (menuItem['price'] ?? 0.0).toDouble(),
            image: menuItem['image_url'] ?? 'assets/images/placeholder.png',
            quantity: itemData['quantity'] ?? 1,
            cafeteriaName: cafeteria?['name'] ?? 'Unknown Cafeteria',
            buildingName: cafeteria?['location'] ?? 'Unknown Location',
            notes: itemData['selected_variant'] ?? '',
            customizations: {}, // Empty customizations for order items
          );
          items.add(item);
        }
      }

      return items;
    } catch (e) {
      debugPrint('Error getting order items: $e');
      return [];
    }
  }

  /// Extract pickup time from order data
  String _extractPickupTime(Map<String, dynamic> orderData) {
    // Try to get pickup time from the order data
    if (orderData['pickup_time'] != null) {
      try {
        final pickupTime = DateTime.parse(orderData['pickup_time']);
        return '${pickupTime.hour.toString().padLeft(2, '0')}:${pickupTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        debugPrint('Error parsing pickup time: $e');
      }
    }

    // Default pickup time
    return 'ASAP';
  }

  /// Dispose the service
  void dispose() {
    _initialized = false;
    _context = null;
  }
}
