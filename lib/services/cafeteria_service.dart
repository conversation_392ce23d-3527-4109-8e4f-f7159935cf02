import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/cafeteria.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class CafeteriaService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'cafeterias';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  // Create a new cafeteria
  Future<void> createCafeteria(Cafeteria cafeteria) async {
    try {
      await init();
      await _supabaseService.insert(_table, cafeteria.toJson());
    } catch (e) {
      debugPrint('Error creating cafeteria: $e');
      rethrow;
    }
  }

  // Read a cafeteria by ID
  Future<Cafeteria?> getCafeteriaById(String cafeteriaId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('id', cafeteriaId)
          .maybeSingle();

      if (response != null) {
        return Cafeteria.from<PERSON>son(response);
      } else {
        return null; // Cafeteria not found
      }
    } catch (e) {
      debugPrint('Error getting cafeteria by ID: $e');
      return null;
    }
  }

  // Update a cafeteria
  Future<void> updateCafeteria(String cafeteriaId, Cafeteria cafeteria) async {
    try {
      await init();
      await _supabaseService
          .update(_table, cafeteria.toJson(), {'id': cafeteriaId});
    } catch (e) {
      debugPrint('Error updating cafeteria: $e');
      rethrow;
    }
  }

  // Delete a cafeteria
  Future<void> deleteCafeteria(String cafeteriaId) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': cafeteriaId});
    } catch (e) {
      debugPrint('Error deleting cafeteria: $e');
      rethrow;
    }
  }

  // Get all cafeterias
  Future<List<Cafeteria>> getCafeterias() async {
    try {
      await init();
      final response = await _supabaseService.select(_table);
      return response.map((data) => Cafeteria.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting cafeterias: $e');
      return [];
    }
  }

  // Get cafeterias by cuisine type
  Future<List<Cafeteria>> getCafeteriasByCuisineType(String cuisineType) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('cuisineType', cuisineType);

      return response.map((data) => Cafeteria.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting cafeterias by cuisine type: $e');
      return [];
    }
  }
}
