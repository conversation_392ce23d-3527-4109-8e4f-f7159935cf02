import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase-based user preferences service for cross-device sync
class SupabasePreferencesService {
  static final SupabasePreferencesService _instance = SupabasePreferencesService._internal();
  factory SupabasePreferencesService() => _instance;
  SupabasePreferencesService._internal();

  SupabaseClient get client => Supabase.instance.client;
  User? get currentUser => client.auth.currentUser;
  bool get isAuthenticated => currentUser != null;

  // Cache for preferences
  Map<String, dynamic>? _cachedPreferences;

  /// Get user preferences from Supabase
  Future<Map<String, dynamic>> getPreferences() async {
    if (!isAuthenticated) return _getDefaultPreferences();

    try {
      final response = await client
          .from('user_preferences')
          .select()
          .eq('user_id', currentUser!.id)
          .maybeSingle();

      if (response != null) {
        _cachedPreferences = Map<String, dynamic>.from(response['preferences'] ?? {});
        return _cachedPreferences!;
      } else {
        // Create default preferences
        final defaultPrefs = _getDefaultPreferences();
        await _createUserPreferences(defaultPrefs);
        return defaultPrefs;
      }
    } catch (e) {
      debugPrint('Error getting preferences: $e');
      return _cachedPreferences ?? _getDefaultPreferences();
    }
  }

  /// Save user preferences to Supabase
  Future<bool> savePreferences(Map<String, dynamic> preferences) async {
    if (!isAuthenticated) return false;

    try {
      // Update cache
      _cachedPreferences = Map<String, dynamic>.from(preferences);

      // Check if preferences exist
      final existing = await client
          .from('user_preferences')
          .select('id')
          .eq('user_id', currentUser!.id)
          .maybeSingle();

      if (existing != null) {
        // Update existing preferences
        await client
            .from('user_preferences')
            .update({
              'preferences': preferences,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('user_id', currentUser!.id);
      } else {
        // Create new preferences
        await _createUserPreferences(preferences);
      }

      return true;
    } catch (e) {
      debugPrint('Error saving preferences: $e');
      return false;
    }
  }

  /// Get specific preference value
  Future<T?> getPreference<T>(String key, {T? defaultValue}) async {
    final preferences = await getPreferences();
    return preferences[key] as T? ?? defaultValue;
  }

  /// Set specific preference value
  Future<bool> setPreference(String key, dynamic value) async {
    final preferences = await getPreferences();
    preferences[key] = value;
    return await savePreferences(preferences);
  }

  /// Get notification preferences
  Future<Map<String, bool>> getNotificationPreferences() async {
    final prefs = await getPreferences();
    final notifications = prefs['notifications'] as Map<String, dynamic>? ?? {};
    
    return {
      'orderUpdates': notifications['orderUpdates'] ?? true,
      'promotions': notifications['promotions'] ?? true,
      'newMenuItems': notifications['newMenuItems'] ?? false,
      'pushNotifications': notifications['pushNotifications'] ?? true,
      'emailNotifications': notifications['emailNotifications'] ?? false,
    };
  }

  /// Save notification preferences
  Future<bool> saveNotificationPreferences(Map<String, bool> notificationPrefs) async {
    return await setPreference('notifications', notificationPrefs);
  }

  /// Get theme preferences
  Future<Map<String, dynamic>> getThemePreferences() async {
    final prefs = await getPreferences();
    final theme = prefs['theme'] as Map<String, dynamic>? ?? {};
    
    return {
      'darkMode': theme['darkMode'] ?? true,
      'primaryColor': theme['primaryColor'] ?? 'orange',
      'fontSize': theme['fontSize'] ?? 'medium',
      'animations': theme['animations'] ?? true,
    };
  }

  /// Save theme preferences
  Future<bool> saveThemePreferences(Map<String, dynamic> themePrefs) async {
    return await setPreference('theme', themePrefs);
  }

  /// Get language preferences
  Future<String> getLanguage() async {
    return await getPreference<String>('language', defaultValue: 'en') ?? 'en';
  }

  /// Set language preference
  Future<bool> setLanguage(String language) async {
    return await setPreference('language', language);
  }

  /// Get payment preferences
  Future<Map<String, dynamic>> getPaymentPreferences() async {
    final prefs = await getPreferences();
    final payment = prefs['payment'] as Map<String, dynamic>? ?? {};
    
    return {
      'defaultMethod': payment['defaultMethod'] ?? 'cash_on_pickup',
      'saveCards': payment['saveCards'] ?? false,
      'autoTip': payment['autoTip'] ?? false,
      'tipPercentage': payment['tipPercentage'] ?? 10,
    };
  }

  /// Save payment preferences
  Future<bool> savePaymentPreferences(Map<String, dynamic> paymentPrefs) async {
    return await setPreference('payment', paymentPrefs);
  }

  /// Get privacy preferences
  Future<Map<String, bool>> getPrivacyPreferences() async {
    final prefs = await getPreferences();
    final privacy = prefs['privacy'] as Map<String, dynamic>? ?? {};
    
    return {
      'shareLocation': privacy['shareLocation'] ?? false,
      'shareOrderHistory': privacy['shareOrderHistory'] ?? false,
      'allowAnalytics': privacy['allowAnalytics'] ?? true,
      'allowMarketing': privacy['allowMarketing'] ?? false,
    };
  }

  /// Save privacy preferences
  Future<bool> savePrivacyPreferences(Map<String, bool> privacyPrefs) async {
    return await setPreference('privacy', privacyPrefs);
  }

  /// Get app behavior preferences
  Future<Map<String, dynamic>> getAppPreferences() async {
    final prefs = await getPreferences();
    final app = prefs['app'] as Map<String, dynamic>? ?? {};
    
    return {
      'autoRefresh': app['autoRefresh'] ?? true,
      'offlineMode': app['offlineMode'] ?? false,
      'cacheImages': app['cacheImages'] ?? true,
      'hapticFeedback': app['hapticFeedback'] ?? true,
      'soundEffects': app['soundEffects'] ?? false,
    };
  }

  /// Save app behavior preferences
  Future<bool> saveAppPreferences(Map<String, dynamic> appPrefs) async {
    return await setPreference('app', appPrefs);
  }

  /// Reset preferences to default
  Future<bool> resetToDefaults() async {
    final defaultPrefs = _getDefaultPreferences();
    return await savePreferences(defaultPrefs);
  }

  /// Create user preferences record
  Future<void> _createUserPreferences(Map<String, dynamic> preferences) async {
    await client.from('user_preferences').insert({
      'user_id': currentUser!.id,
      'preferences': preferences,
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  /// Get default preferences
  Map<String, dynamic> _getDefaultPreferences() {
    return {
      'notifications': {
        'orderUpdates': true,
        'promotions': true,
        'newMenuItems': false,
        'pushNotifications': true,
        'emailNotifications': false,
      },
      'theme': {
        'darkMode': true,
        'primaryColor': 'orange',
        'fontSize': 'medium',
        'animations': true,
      },
      'language': 'en',
      'payment': {
        'defaultMethod': 'cash_on_pickup',
        'saveCards': false,
        'autoTip': false,
        'tipPercentage': 10,
      },
      'privacy': {
        'shareLocation': false,
        'shareOrderHistory': false,
        'allowAnalytics': true,
        'allowMarketing': false,
      },
      'app': {
        'autoRefresh': true,
        'offlineMode': false,
        'cacheImages': true,
        'hapticFeedback': true,
        'soundEffects': false,
      },
    };
  }

  /// Clear cached preferences
  void clearCache() {
    _cachedPreferences = null;
  }
}
