import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:unieatsappv0/models/chat_models.dart';
import 'package:uuid/uuid.dart';

class SupportTicketService {
  static final SupportTicketService _instance = SupportTicketService._internal();
  factory SupportTicketService() => _instance;
  SupportTicketService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Table name
  static const String _ticketsTable = 'support_tickets';

  /// Generate a unique ticket number
  String _generateTicketNumber() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8);
    return 'UE-$timestamp';
  }

  /// Create a new support ticket
  Future<SupportTicket?> createTicket({
    required String title,
    required String description,
    required String category,
    String priority = 'medium',
    String? orderId,
    List<String>? attachments,
    List<String>? tags,
  }) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        debugPrint('Cannot create ticket: User not logged in');
        return null;
      }

      final ticketId = const Uuid().v4();
      final ticketNumber = _generateTicketNumber();
      final now = DateTime.now();

      final ticketData = {
        'id': ticketId,
        'ticket_number': ticketNumber,
        'user_id': user.id,
        'title': title,
        'description': description,
        'category': category,
        'priority': priority,
        'status': 'open',
        'order_id': orderId,
        'attachments': attachments ?? [],
        'tags': tags ?? [],
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      debugPrint('Creating support ticket with data: $ticketData');

      final response = await _supabaseService.client
          .from(_ticketsTable)
          .insert(ticketData)
          .select()
          .single();

      debugPrint('Support ticket created successfully: ${response['ticket_number']}');
      debugPrint('Ticket will appear in admin portal for processing');

      return SupportTicket.fromJson(response);
    } catch (e) {
      debugPrint('Error creating support ticket: $e');
      return null;
    }
  }

  /// Get all tickets for the current user
  Future<List<SupportTicket>> getUserTickets() async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        debugPrint('Cannot get tickets: User not logged in');
        return [];
      }

      final response = await _supabaseService.client
          .from(_ticketsTable)
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return response.map((json) => SupportTicket.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting user tickets: $e');
      return [];
    }
  }

  /// Get a specific ticket by ID
  Future<SupportTicket?> getTicketById(String ticketId) async {
    try {
      final response = await _supabaseService.client
          .from(_ticketsTable)
          .select()
          .eq('id', ticketId)
          .maybeSingle();

      if (response != null) {
        return SupportTicket.fromJson(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting ticket by ID: $e');
      return null;
    }
  }

  /// Get a ticket by ticket number
  Future<SupportTicket?> getTicketByNumber(String ticketNumber) async {
    try {
      final response = await _supabaseService.client
          .from(_ticketsTable)
          .select()
          .eq('ticket_number', ticketNumber)
          .maybeSingle();

      if (response != null) {
        return SupportTicket.fromJson(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting ticket by number: $e');
      return null;
    }
  }

  /// Update ticket status
  Future<bool> updateTicketStatus(String ticketId, String status) async {
    try {
      final updateData = {
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Add resolved_at or closed_at timestamps
      if (status == 'resolved') {
        updateData['resolved_at'] = DateTime.now().toIso8601String();
      } else if (status == 'closed') {
        updateData['closed_at'] = DateTime.now().toIso8601String();
      }

      await _supabaseService.client
          .from(_ticketsTable)
          .update(updateData)
          .eq('id', ticketId);

      return true;
    } catch (e) {
      debugPrint('Error updating ticket status: $e');
      return false;
    }
  }

  /// Add resolution to ticket
  Future<bool> addResolution(String ticketId, String resolution) async {
    try {
      await _supabaseService.client
          .from(_ticketsTable)
          .update({
            'resolution': resolution,
            'status': 'resolved',
            'resolved_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', ticketId);

      return true;
    } catch (e) {
      debugPrint('Error adding resolution: $e');
      return false;
    }
  }

  /// Rate a ticket
  Future<bool> rateTicket(String ticketId, int rating, String? feedback) async {
    try {
      await _supabaseService.client
          .from(_ticketsTable)
          .update({
            'customer_rating': rating,
            'customer_feedback': feedback,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', ticketId);

      return true;
    } catch (e) {
      debugPrint('Error rating ticket: $e');
      return false;
    }
  }

  /// Add tags to ticket
  Future<bool> addTags(String ticketId, List<String> tags) async {
    try {
      // Get current ticket to merge tags
      final ticket = await getTicketById(ticketId);
      if (ticket == null) return false;

      final updatedTags = {...ticket.tags, ...tags}.toList();

      await _supabaseService.client
          .from(_ticketsTable)
          .update({
            'tags': updatedTags,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', ticketId);

      return true;
    } catch (e) {
      debugPrint('Error adding tags: $e');
      return false;
    }
  }

  /// Get tickets by category
  Future<List<SupportTicket>> getTicketsByCategory(String category) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) return [];

      final response = await _supabaseService.client
          .from(_ticketsTable)
          .select()
          .eq('user_id', user.id)
          .eq('category', category)
          .order('created_at', ascending: false);

      return response.map((json) => SupportTicket.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting tickets by category: $e');
      return [];
    }
  }

  /// Get tickets by status
  Future<List<SupportTicket>> getTicketsByStatus(String status) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) return [];

      final response = await _supabaseService.client
          .from(_ticketsTable)
          .select()
          .eq('user_id', user.id)
          .eq('status', status)
          .order('created_at', ascending: false);

      return response.map((json) => SupportTicket.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting tickets by status: $e');
      return [];
    }
  }

  /// Get available categories
  List<String> getAvailableCategories() {
    return [
      'general_inquiry',
      'order_issue',
      'payment_problem',
      'account_issue',
      'technical_support',
      'feature_request',
      'bug_report',
      'refund_request',
      'other',
    ];
  }

  /// Get available priorities
  List<String> getAvailablePriorities() {
    return ['low', 'medium', 'high', 'urgent'];
  }
}
