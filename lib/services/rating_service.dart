import 'package:flutter/material.dart';
import 'package:unieatsappv0/config/supabase_config.dart';
import 'package:unieatsappv0/models/cafeteria_rating.dart';

/// Service class for handling ratings in the app
class RatingService {
  /// Verify user authentication and get user details
  static Future<Map<String, dynamic>> _verifyAuthentication() async {
    try {
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      debugPrint('🔐 Authentication check:');
      debugPrint('   User ID: ${currentUser.id}');
      debugPrint('   Email: ${currentUser.email}');
      debugPrint('   Last sign in: ${currentUser.lastSignInAt}');
      debugPrint('   Session valid: ${currentUser.aud}');

      // Also check if user exists in profiles table
      final profileResponse = await SupabaseConfig.client
          .from('profiles')
          .select('id, full_name, role')
          .eq('id', currentUser.id)
          .maybeSingle();

      debugPrint('   Profile found: ${profileResponse != null}');
      if (profileResponse != null) {
        debugPrint('   Profile name: ${profileResponse['full_name']}');
        debugPrint('   Profile role: ${profileResponse['role']}');
      }

      return {
        'user': currentUser,
        'profile': profileResponse,
      };
    } catch (e) {
      debugPrint('❌ Authentication verification failed: $e');
      rethrow;
    }
  }
  /// Submit a cafeteria rating
  static Future<void> submitCafeteriaRating({
    required String cafeteriaId,
    required double rating,
    String? comment,
    String? orderId,
    required BuildContext context,
  }) async {
    try {
      debugPrint('🔄 Submitting cafeteria rating...');
      debugPrint('   Cafeteria ID: $cafeteriaId');
      debugPrint('   Rating: ${rating.round()}');
      debugPrint('   Comment: $comment');
      debugPrint('   Order ID: $orderId');

      // Verify authentication first
      final authData = await _verifyAuthentication();
      final currentUser = authData['user'];

      debugPrint('✅ Authentication verified for user: ${currentUser.id}');

      // Prepare the data to insert
      final ratingData = {
        'cafeteria_id': cafeteriaId,
        'user_id': currentUser.id,
        'rating': rating.round(), // Ensure rating is an integer between 1-5
        'comment': comment,
        'order_id': orderId,
      };

      debugPrint('📝 Rating data to insert: $ratingData');

      // Insert rating into cafeteria_user_ratings table
      final response = await SupabaseConfig.client
          .from('cafeteria_user_ratings')
          .insert(ratingData)
          .select();

      debugPrint('✅ Cafeteria rating submitted successfully: $response');

      // The trigger function will automatically update the cafeteria_ratings table
    } catch (e) {
      debugPrint('❌ Error submitting cafeteria rating: $e');
      debugPrint('   Error type: ${e.runtimeType}');
      debugPrint('   Error details: ${e.toString()}');

      // Show error message if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting rating: $e')),
        );
      }
      rethrow;
    }
  }

  /// Submit a menu item rating
  static Future<void> submitMenuItemRating({
    required String menuItemId,
    required double rating,
    String? comment,
    String? orderId,
    required BuildContext context,
  }) async {
    try {
      debugPrint('🔄 Submitting menu item rating...');
      debugPrint('   Menu Item ID: $menuItemId');
      debugPrint('   Rating: ${rating.round()}');
      debugPrint('   Comment: $comment');
      debugPrint('   Order ID: $orderId');

      // Verify authentication first
      final authData = await _verifyAuthentication();
      final currentUser = authData['user'];

      debugPrint('✅ Authentication verified for user: ${currentUser.id}');

      // Prepare the data to insert
      final ratingData = {
        'menu_item_id': menuItemId,
        'user_id': currentUser.id,
        'rating': rating.round(), // Ensure rating is an integer between 1-5
        'review_comment': comment,
        'order_id': orderId,
      };

      debugPrint('📝 Menu item rating data to insert: $ratingData');

      // Insert rating into menu_item_ratings table
      final response = await SupabaseConfig.client
          .from('menu_item_ratings')
          .insert(ratingData)
          .select();

      debugPrint('✅ Menu item rating submitted successfully: $response');

      // The trigger function will automatically update the menu_items table
    } catch (e) {
      debugPrint('❌ Error submitting menu item rating: $e');
      debugPrint('   Error type: ${e.runtimeType}');
      debugPrint('   Error details: ${e.toString()}');

      // Show error message if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting rating: $e')),
        );
      }
      rethrow;
    }
  }

  /// Submit an order rating
  static Future<void> submitOrderRating({
    required String orderId,
    required int rating,
    String? comment,
    required BuildContext context,
  }) async {
    try {
      debugPrint('🔄 Submitting order rating...');
      debugPrint('   Order ID: $orderId');
      debugPrint('   Rating: $rating');
      debugPrint('   Comment: $comment');

      // Verify authentication first
      final authData = await _verifyAuthentication();
      final currentUser = authData['user'];

      debugPrint('✅ Authentication verified for user: ${currentUser.id}');

      // Update the order with the rating
      final response = await SupabaseConfig.client
          .from('orders')
          .update({
            'rating': rating,
            'review_comment': comment,
          })
          .eq('id', orderId)
          .select();

      debugPrint('✅ Order rating submitted successfully: $response');
    } catch (e) {
      debugPrint('❌ Error submitting order rating: $e');
      debugPrint('   Error type: ${e.runtimeType}');
      debugPrint('   Error details: ${e.toString()}');

      // Show error message if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting rating: $e')),
        );
      }
      rethrow;
    }
  }

  /// Get cafeteria ratings
  static Future<List<CafeteriaRating>> getCafeteriaRatings(String cafeteriaId) async {
    try {
      // Get ratings from cafeteria_user_ratings table without automatic join
      final response = await SupabaseConfig.client
          .from('cafeteria_user_ratings')
          .select('*')
          .eq('cafeteria_id', cafeteriaId)
          .order('created_at', ascending: false);

      // Get user names separately if needed
      final List<CafeteriaRating> ratings = [];
      for (final item in response as List) {
        String? username;

        // Try to get username if user_id exists
        if (item['user_id'] != null) {
          try {
            final userResponse = await SupabaseConfig.client
                .from('profiles')
                .select('full_name')
                .eq('id', item['user_id'])
                .maybeSingle();
            username = userResponse?['full_name'];
          } catch (e) {
            debugPrint('Could not fetch username for user ${item['user_id']}: $e');
          }
        }

        ratings.add(CafeteriaRating(
          id: item['id'],
          cafeteriaId: item['cafeteria_id'],
          rating: (item['rating'] ?? 0).toDouble(),
          comment: item['comment'],
          date: DateTime.parse(item['created_at']),
          orderId: item['order_id'],
          userId: item['user_id'],
          username: username ?? 'Anonymous User',
        ));
      }

      return ratings;
    } catch (e) {
      debugPrint('Error getting cafeteria ratings: $e');
      return [];
    }
  }

  /// Get cafeteria average rating
  static Future<Map<String, dynamic>> getCafeteriaAverageRating(String cafeteriaId) async {
    try {
      // Get average rating from cafeteria_ratings table
      final response = await SupabaseConfig.client
          .from('cafeteria_ratings')
          .select('*')
          .eq('cafeteria_id', cafeteriaId)
          .single();

      return {
        'overall_rating': (response['overall_rating'] ?? 0).toDouble(),
        'total_ratings': response['total_ratings'] ?? 0,
        'food_quality': (response['food_quality'] ?? 0).toDouble(),
        'service': (response['service'] ?? 0).toDouble(),
        'cleanliness': (response['cleanliness'] ?? 0).toDouble(),
        'value_for_money': (response['value_for_money'] ?? 0).toDouble(),
      };
    } catch (e) {
      debugPrint('Error getting cafeteria average rating: $e');
      return {
        'overall_rating': 0.0,
        'total_ratings': 0,
        'food_quality': 0.0,
        'service': 0.0,
        'cleanliness': 0.0,
        'value_for_money': 0.0,
      };
    }
  }

  /// Get menu item ratings
  static Future<List<Map<String, dynamic>>> getMenuItemRatings(String menuItemId) async {
    try {
      // Get ratings from menu_item_ratings table without automatic join
      final response = await SupabaseConfig.client
          .from('menu_item_ratings')
          .select('*')
          .eq('menu_item_id', menuItemId)
          .order('created_at', ascending: false);

      // Get user names separately for each rating
      final List<Map<String, dynamic>> ratingsWithUsernames = [];
      for (final rating in response as List) {
        String? username;

        // Try to get username if user_id exists
        if (rating['user_id'] != null) {
          try {
            final userResponse = await SupabaseConfig.client
                .from('profiles')
                .select('full_name')
                .eq('id', rating['user_id'])
                .maybeSingle();
            username = userResponse?['full_name'];
          } catch (e) {
            debugPrint('Could not fetch username for user ${rating['user_id']}: $e');
          }
        }

        // Add the rating with username in the expected format
        ratingsWithUsernames.add({
          'id': rating['id'],
          'menu_item_id': rating['menu_item_id'],
          'rating': rating['rating'],
          'review_comment': rating['review_comment'],
          'created_at': rating['created_at'],
          'user_id': rating['user_id'],
          'profiles': {'full_name': username ?? 'Anonymous'},
        });
      }

      return ratingsWithUsernames;
    } catch (e) {
      debugPrint('Error getting menu item ratings: $e');
      return [];
    }
  }
}
