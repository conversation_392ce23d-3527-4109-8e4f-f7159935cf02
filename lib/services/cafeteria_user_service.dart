import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/cafeteria_user.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class CafeteriaUserService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'cafeteria_users';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  Future<void> createCafeteriaUser(CafeteriaUser cafeteriaUser) async {
    try {
      await init();
      await _supabaseService.insert(_table, cafeteriaUser.toJson());
    } catch (e) {
      debugPrint('Error creating cafeteria user: $e');
      rethrow;
    }
  }

  Future<CafeteriaUser?> getCafeteriaUser(String id) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('id', id)
          .maybeSingle();

      if (response != null) {
        return CafeteriaUser.fromSupabase(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting cafeteria user: $e');
      return null;
    }
  }

  Future<void> updateCafeteriaUser(
      String id, CafeteriaUser cafeteriaUser) async {
    try {
      await init();
      await _supabaseService.update(_table, cafeteriaUser.toJson(), {'id': id});
    } catch (e) {
      debugPrint('Error updating cafeteria user: $e');
      rethrow;
    }
  }

  Future<void> deleteCafeteriaUser(String id) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': id});
    } catch (e) {
      debugPrint('Error deleting cafeteria user: $e');
      rethrow;
    }
  }

  // Get cafeteria users by cafeteria ID
  Future<List<CafeteriaUser>> getCafeteriaUsersByCafeteriaId(
      String cafeteriaId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('cafeteriaId', cafeteriaId);

      return response.map((data) => CafeteriaUser.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting cafeteria users by cafeteria ID: $e');
      return [];
    }
  }

  // Get cafeteria user by email
  Future<CafeteriaUser?> getCafeteriaUserByEmail(String email) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('email', email)
          .limit(1)
          .maybeSingle();

      if (response != null) {
        return CafeteriaUser.fromSupabase(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting cafeteria user by email: $e');
      return null;
    }
  }
}
