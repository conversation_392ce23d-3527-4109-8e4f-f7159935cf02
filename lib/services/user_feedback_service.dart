import 'package:flutter/foundation.dart';
import '../models/user_feedback.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class UserFeedbackService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'user_feedback';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  Future<void> createUserFeedback(UserFeedback feedback) async {
    try {
      await init();
      await _supabaseService.insert(_table, feedback.toJson());
    } catch (e) {
      debugPrint('Error creating user feedback: $e');
      rethrow;
    }
  }

  Future<List<UserFeedback>> getUserFeedback() async {
    try {
      await init();
      final response = await _supabaseService.select(_table);
      return response.map((data) => UserFeedback.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting user feedback: $e');
      return [];
    }
  }

  Future<void> updateUserFeedback(
      String feedbackId, UserFeedback feedback) async {
    try {
      await init();
      await _supabaseService
          .update(_table, feedback.toJson(), {'id': feedbackId});
    } catch (e) {
      debugPrint('Error updating user feedback: $e');
      rethrow;
    }
  }

  Future<void> deleteUserFeedback(String feedbackId) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': feedbackId});
    } catch (e) {
      debugPrint('Error deleting user feedback: $e');
      rethrow;
    }
  }
}
