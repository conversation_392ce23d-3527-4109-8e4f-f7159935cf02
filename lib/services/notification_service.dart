import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // GlobalKey for accessing the navigator and scaffold
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // Initialize the notification service
  Future<void> init() async {
    debugPrint('Notification service initialized');
  }

  // Request notification permissions (simplified version)
  Future<void> requestPermissions() async {
    debugPrint('Notification permissions requested');
  }

  // Show a notification for order status change
  void showOrderStatusNotification({
    required String title,
    required String body,
    required String orderNumber,
    String? status,
    VoidCallback? onTap,
  }) {
    // Show in-app notification if the app is in the foreground
    final context = navigatorKey.currentContext;
    if (context != null) {
      // Show a snackbar using our utility
      SnackBarUtils.showOrderSnackBar(
        context: context,
        message: body,
        status: status ?? 'Updated',
        onViewOrder: onTap,
      );
    }
  }

  // Show a notification for order ready for pickup
  void showOrderReadyNotification(Order order, {VoidCallback? onTap}) {
    final String cafeteriaName = order.items.isNotEmpty
        ? order.items.first.cafeteriaName
        : 'the cafeteria';

    showOrderStatusNotification(
      title: 'Order Ready for Pickup',
      body: 'Your order #${order.orderNumber} is ready for pickup at $cafeteriaName.',
      orderNumber: order.orderNumber,
      status: 'Ready for pickup',
      onTap: onTap,
    );
  }

  // Show a notification for order completed
  void showOrderCompletedNotification(Order order, {VoidCallback? onTap}) {
    showOrderStatusNotification(
      title: 'Order Completed',
      body: 'Your order #${order.orderNumber} has been completed. Thank you for using UniEats!',
      orderNumber: order.orderNumber,
      status: 'Completed',
      onTap: onTap,
    );
  }

  // Show a notification for order cancelled
  void showOrderCancelledNotification(Order order, {VoidCallback? onTap}) {
    showOrderStatusNotification(
      title: 'Order Cancelled',
      body: 'Your order #${order.orderNumber} has been cancelled.',
      orderNumber: order.orderNumber,
      status: 'Cancelled',
      onTap: onTap,
    );
  }
}
