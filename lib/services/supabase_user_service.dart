import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';

class SupabaseUserService {
  static final SupabaseUserService _instance = SupabaseUserService._internal();
  factory SupabaseUserService() => _instance;
  SupabaseUserService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Table name
  static const String _tableName = 'profiles';

  /// Register a new user
  Future<UserProfile?> register({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      debugPrint('Registering user with email: $email, name: $fullName');

      // Register the user with Supabase Auth
      // Include metadata so the trigger can use it
      final authResponse = await _supabaseService.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName}, // Include full_name in metadata
      );

      if (authResponse.user == null) {
        debugPrint('Failed to register user: No user returned');
        return null;
      }

      debugPrint(
          'User registered successfully with ID: ${authResponse.user!.id}');

      // The profile will be created automatically by the database trigger
      final userId = authResponse.user!.id;

      // Wait a moment for the trigger to create the profile
      await Future.delayed(const Duration(milliseconds: 500));

      try {
        // Check if the profile was created
        final profileResponse = await _supabaseService.client
            .from(_tableName)
            .select()
            .eq('id', userId)
            .maybeSingle();

        if (profileResponse == null) {
          debugPrint('Profile not created by trigger, creating manually');

          // First, get the student role ID
          final roleResponse = await _supabaseService.client
              .from('roles')
              .select('id')
              .eq('name', 'student')
              .single();

          debugPrint('Got student role ID: ${roleResponse['id']}');

          // Create a profile with the correct role ID
          final profileData = {
            'id': userId,
            'full_name': fullName,
            'role': roleResponse['id'], // Use the role ID, not the name
          };

          debugPrint('Creating profile with data: $profileData');

          // Insert the profile
          await _supabaseService.client.from(_tableName).insert(profileData);
          debugPrint('Profile created successfully manually');
        } else {
          debugPrint(
              'Profile created by trigger: ${profileResponse.toString()}');
        }

        // Get the role ID for the user profile
        String roleId;
        if (profileResponse != null && profileResponse['role'] != null) {
          // Use the role ID from the profile
          roleId = profileResponse['role'];
          debugPrint('Using role ID from profile: $roleId');
        } else {
          // Get the student role ID
          final roleData = await _supabaseService.client
              .from('roles')
              .select('id')
              .eq('name', 'student')
              .single();
          roleId = roleData['id'];
          debugPrint('Using role ID from query: $roleId');
        }

        // Return the user profile
        return UserProfile(
          id: userId,
          email: email,
          fullName: fullName,
          roleId: roleId,
          theme: 'light',
          notificationEnabled: true,
          createdAt: DateTime.now(),
        );
      } catch (profileError) {
        debugPrint('Error creating profile: $profileError');
        // Even if profile creation fails, return a basic profile
        // so the user can still use the app
        // Use the default student role ID
        const defaultStudentRoleId = '66827359-236a-4cc6-9d29-b96c53ccccfd';
        debugPrint(
            'Using default student role ID for fallback: $defaultStudentRoleId');

        return UserProfile(
          id: userId,
          email: email,
          fullName: fullName,
          roleId: defaultStudentRoleId,
          createdAt: DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('Error registering user: $e');
      rethrow; // Rethrow to see the full error in the UI
    }
  }

  /// Login a user
  Future<UserProfile?> login({
    required String email,
    required String password,
  }) async {
    try {
      final authResponse = await _supabaseService.signIn(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        debugPrint('Failed to login: No user returned');
        return null;
      }

      // Get the user profile
      return await getUserProfile(authResponse.user!.id);
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  /// Logout the current user
  Future<void> logout() async {
    try {
      await _supabaseService.signOut();
    } catch (e) {
      debugPrint('Error logging out: $e');
    }
  }

  /// Get the current user profile
  Future<UserProfile?> getCurrentUserProfile() async {
    final user = _supabaseService.currentUser;
    if (user == null) {
      return null;
    }

    return await getUserProfile(user.id);
  }

  /// Get a user profile by ID
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      final response = await _supabaseService.client
          .from(_tableName)
          .select()
          .eq('id', userId)
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }

  /// Check if a user can log in with the given credentials
  Future<bool> canLogin({
    required String email,
    required String password,
  }) async {
    try {
      final authResponse = await _supabaseService.signIn(
        email: email,
        password: password,
      );

      return authResponse.user != null;
    } catch (e) {
      debugPrint('Error checking login: $e');
      return false;
    }
  }

  /// Update a user profile
  Future<UserProfile?> updateUserProfile({
    required String userId,
    String? fullName,
    String? avatarUrl,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (fullName != null) {
        updateData['full_name'] = fullName;
      }

      if (avatarUrl != null) {
        updateData['avatar_url'] = avatarUrl;
      }

      await _supabaseService.client
          .from(_tableName)
          .update(updateData)
          .eq('id', userId);

      return await getUserProfile(userId);
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      return null;
    }
  }
}
