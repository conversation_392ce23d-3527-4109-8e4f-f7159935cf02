import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get current user
  User? get currentUser => _supabase.auth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Get authentication token
  Future<String?> getToken() async {
    try {
      final session = _supabase.auth.currentSession;
      if (session != null) {
        return session.accessToken;
      }

      // Fallback to stored token
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      debugPrint('Error getting token: $e');
      return null;
    }
  }

  /// Sign in with email and password
  Future<AuthResult> signIn(String email, String password) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        await _saveAuthData(response.session?.accessToken, response.user);
        return AuthResult(success: true, user: response.user);
      } else {
        return AuthResult(success: false, error: 'Invalid credentials');
      }
    } catch (e) {
      debugPrint('Sign in error: $e');
      return AuthResult(success: false, error: e.toString());
    }
  }

  /// Sign up with email and password
  Future<AuthResult> signUp(String email, String password, {
    String? fullName,
    String? studentId,
    String? phone,
  }) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName,
          'student_id': studentId,
          'phone': phone,
        },
      );

      if (response.user != null) {
        await _saveAuthData(response.session?.accessToken, response.user);
        return AuthResult(success: true, user: response.user);
      } else {
        return AuthResult(success: false, error: 'Sign up failed');
      }
    } catch (e) {
      debugPrint('Sign up error: $e');
      return AuthResult(success: false, error: e.toString());
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
      await _clearAuthData();
    } catch (e) {
      debugPrint('Sign out error: $e');
    }
  }

  /// Reset password
  Future<bool> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
      return true;
    } catch (e) {
      debugPrint('Reset password error: $e');
      return false;
    }
  }

  /// Update password
  Future<bool> updatePassword(String newPassword) async {
    try {
      final response = await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return response.user != null;
    } catch (e) {
      debugPrint('Update password error: $e');
      return false;
    }
  }

  /// Get user profile
  Future<UserProfile?> getUserProfile() async {
    try {
      final user = currentUser;
      if (user == null) return null;

      final response = await _supabase
          .from('profiles')
          .select()
          .eq('id', user.id)
          .single();

      return UserProfile.fromJson(response);
    } catch (e) {
      debugPrint('Get user profile error: $e');
      return null;
    }
  }

  /// Update user profile
  Future<bool> updateUserProfile(Map<String, dynamic> updates) async {
    try {
      final user = currentUser;
      if (user == null) return false;

      await _supabase
          .from('profiles')
          .update(updates)
          .eq('id', user.id);

      return true;
    } catch (e) {
      debugPrint('Update user profile error: $e');
      return false;
    }
  }

  /// Save authentication data locally
  Future<void> _saveAuthData(String? token, User? user) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (token != null) {
        await prefs.setString(_tokenKey, token);
      }

      if (user != null) {
        await prefs.setString(_userKey, json.encode(user.toJson()));
      }
    } catch (e) {
      debugPrint('Error saving auth data: $e');
    }
  }

  /// Clear authentication data
  Future<void> _clearAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userKey);
    } catch (e) {
      debugPrint('Error clearing auth data: $e');
    }
  }

  /// Initialize auth state
  Future<void> initialize() async {
    try {
      // Listen to auth state changes
      _supabase.auth.onAuthStateChange.listen((data) {
        final event = data.event;
        final session = data.session;

        if (event == AuthChangeEvent.signedIn && session != null) {
          _saveAuthData(session.accessToken, session.user);
        } else if (event == AuthChangeEvent.signedOut) {
          _clearAuthData();
        }
      });
    } catch (e) {
      debugPrint('Auth initialization error: $e');
    }
  }
}

/// Authentication result model
class AuthResult {
  final bool success;
  final User? user;
  final String? error;

  AuthResult({
    required this.success,
    this.user,
    this.error,
  });
}

/// User profile model
class UserProfile {
  final String id;
  final String email;
  final String? fullName;
  final String? studentId;
  final String? phone;
  final String? avatarUrl;
  final String role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserProfile({
    required this.id,
    required this.email,
    this.fullName,
    this.studentId,
    this.phone,
    this.avatarUrl,
    required this.role,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      email: json['email'],
      fullName: json['full_name'],
      studentId: json['student_id'],
      phone: json['phone'],
      avatarUrl: json['avatar_url'],
      role: json['role'] ?? 'student',
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'student_id': studentId,
      'phone': phone,
      'avatar_url': avatarUrl,
      'role': role,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
