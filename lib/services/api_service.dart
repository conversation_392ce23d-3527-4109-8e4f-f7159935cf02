import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'auth_service.dart';

class ApiService {
  static const String baseUrl = String.fromEnvironment('API_URL', defaultValue: 'http://localhost:3000');
  final AuthService _authService = AuthService();

  /// Make a GET request
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? queryParams,
    bool requiresAuth = false,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final uriWithParams = queryParams != null 
          ? uri.replace(queryParameters: queryParams)
          : uri;

      final headers = <String, String>{
        'Content-Type': 'application/json',
      };

      if (requiresAuth) {
        final token = await _authService.getToken();
        if (token != null) {
          headers['Authorization'] = 'Bearer $token';
        }
      }

      final response = await http.get(uriWithParams, headers: headers);

      return _handleResponse(response);
    } catch (e) {
      debugPrint('GET request error: $e');
      return {'success': false, 'error': 'Network error'};
    }
  }

  /// Make a POST request
  Future<Map<String, dynamic>> post(
    String endpoint,
    Map<String, dynamic> data, {
    bool requiresAuth = false,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      final headers = <String, String>{
        'Content-Type': 'application/json',
      };

      if (requiresAuth) {
        final token = await _authService.getToken();
        if (token != null) {
          headers['Authorization'] = 'Bearer $token';
        }
      }

      final response = await http.post(
        uri,
        headers: headers,
        body: json.encode(data),
      );

      return _handleResponse(response);
    } catch (e) {
      debugPrint('POST request error: $e');
      return {'success': false, 'error': 'Network error'};
    }
  }

  /// Make a PUT request
  Future<Map<String, dynamic>> put(
    String endpoint,
    Map<String, dynamic> data, {
    bool requiresAuth = false,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      final headers = <String, String>{
        'Content-Type': 'application/json',
      };

      if (requiresAuth) {
        final token = await _authService.getToken();
        if (token != null) {
          headers['Authorization'] = 'Bearer $token';
        }
      }

      final response = await http.put(
        uri,
        headers: headers,
        body: json.encode(data),
      );

      return _handleResponse(response);
    } catch (e) {
      debugPrint('PUT request error: $e');
      return {'success': false, 'error': 'Network error'};
    }
  }

  /// Make a DELETE request
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    bool requiresAuth = false,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      final headers = <String, String>{
        'Content-Type': 'application/json',
      };

      if (requiresAuth) {
        final token = await _authService.getToken();
        if (token != null) {
          headers['Authorization'] = 'Bearer $token';
        }
      }

      final response = await http.delete(uri, headers: headers);

      return _handleResponse(response);
    } catch (e) {
      debugPrint('DELETE request error: $e');
      return {'success': false, 'error': 'Network error'};
    }
  }

  /// Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    try {
      final data = json.decode(response.body) as Map<String, dynamic>;

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return data;
      } else {
        return {
          'success': false,
          'error': data['error'] ?? 'Request failed',
          'status_code': response.statusCode,
        };
      }
    } catch (e) {
      debugPrint('Response parsing error: $e');
      return {
        'success': false,
        'error': 'Invalid response format',
        'status_code': response.statusCode,
      };
    }
  }
}
