import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';

class SupabaseMenuService {
  static final SupabaseMenuService _instance = SupabaseMenuService._internal();
  factory SupabaseMenuService() => _instance;
  SupabaseMenuService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Table name
  static const String _tableName = 'menu_items';

  /// Get all menu items for a cafeteria
  Future<List<SupabaseMenuItem>> getMenuItemsByCafeteria(
      String cafeteriaId) async {
    try {
      debugPrint('🔍 Loading menu items for cafeteria: $cafeteriaId');

      final response = await _supabaseService.client
          .from(_tableName)
          .select('''
            *,
            rating:menu_item_ratings(rating),
            total_ratings:menu_item_ratings(rating)
          ''')
          .eq('cafeteria_id', cafeteriaId)
          .eq('is_available', true)
          .order('name');

      debugPrint('📋 Raw response type: ${response.runtimeType}');
      debugPrint('📋 Raw response: $response');

      final menuItems = (response as List)
          .map((json) {
            try {
              if (json is Map<String, dynamic>) {
                // Calculate average rating from the ratings array
                final ratings = json['rating'] as List?;
                double averageRating = 0.0;
                int totalRatings = 0;

                if (ratings != null && ratings.isNotEmpty) {
                  final ratingValues = ratings
                      .map((r) => (r['rating'] as num?)?.toDouble() ?? 0.0)
                      .where((r) => r > 0)
                      .toList();

                  if (ratingValues.isNotEmpty) {
                    averageRating = ratingValues.reduce((a, b) => a + b) / ratingValues.length;
                    totalRatings = ratingValues.length;
                  }
                }

                // Create a new JSON object with calculated ratings
                final updatedJson = Map<String, dynamic>.from(json);
                updatedJson['rating'] = averageRating;
                updatedJson['total_ratings'] = totalRatings;

                return SupabaseMenuItem.fromJson(updatedJson);
              } else {
                debugPrint('❌ Invalid item format: ${json.runtimeType} - $json');
                return null;
              }
            } catch (e) {
              debugPrint('❌ Error parsing menu item: $e');
              debugPrint('   Item data: $json');
              return null;
            }
          })
          .where((item) => item != null)
          .cast<SupabaseMenuItem>()
          .toList();

      debugPrint('✅ Successfully parsed ${menuItems.length} menu items');
      return menuItems;
    } catch (e) {
      debugPrint('❌ Error getting menu items: $e');
      debugPrint('   Error type: ${e.runtimeType}');
      return [];
    }
  }

  /// Get menu items by category
  Future<List<SupabaseMenuItem>> getMenuItemsByCategory(
      String cafeteriaId, String category) async {
    try {
      debugPrint('🔍 Loading menu items for cafeteria: $cafeteriaId, category: $category');

      final response = await _supabaseService.client
          .from(_tableName)
          .select('''
            *,
            rating:menu_item_ratings(rating),
            total_ratings:menu_item_ratings(rating)
          ''')
          .eq('cafeteria_id', cafeteriaId)
          .eq('category', category)
          .eq('is_available', true)
          .order('name');

      debugPrint('📋 Category response type: ${response.runtimeType}');

      final menuItems = (response as List)
          .map((json) {
            try {
              if (json is Map<String, dynamic>) {
                // Calculate average rating from the ratings array
                final ratings = json['rating'] as List?;
                double averageRating = 0.0;
                int totalRatings = 0;

                if (ratings != null && ratings.isNotEmpty) {
                  final ratingValues = ratings
                      .map((r) => (r['rating'] as num?)?.toDouble() ?? 0.0)
                      .where((r) => r > 0)
                      .toList();

                  if (ratingValues.isNotEmpty) {
                    averageRating = ratingValues.reduce((a, b) => a + b) / ratingValues.length;
                    totalRatings = ratingValues.length;
                  }
                }

                // Create a new JSON object with calculated ratings
                final updatedJson = Map<String, dynamic>.from(json);
                updatedJson['rating'] = averageRating;
                updatedJson['total_ratings'] = totalRatings;

                return SupabaseMenuItem.fromJson(updatedJson);
              } else {
                debugPrint('❌ Invalid category item format: ${json.runtimeType} - $json');
                return null;
              }
            } catch (e) {
              debugPrint('❌ Error parsing category menu item: $e');
              debugPrint('   Item data: $json');
              return null;
            }
          })
          .where((item) => item != null)
          .cast<SupabaseMenuItem>()
          .toList();

      debugPrint('✅ Successfully parsed ${menuItems.length} menu items for category: $category');
      return menuItems;
    } catch (e) {
      debugPrint('❌ Error getting menu items by category: $e');
      debugPrint('   Error type: ${e.runtimeType}');
      return [];
    }
  }

  /// Get a menu item by ID
  Future<SupabaseMenuItem?> getMenuItemById(String id) async {
    try {
      final response = await _supabaseService.client
          .from(_tableName)
          .select()
          .eq('id', id)
          .single();

      return SupabaseMenuItem.fromJson(response);
    } catch (e) {
      debugPrint('Error getting menu item by ID: $e');
      return null;
    }
  }

  /// Create a new menu item
  Future<SupabaseMenuItem?> createMenuItem({
    required String cafeteriaId,
    required String name,
    String? description,
    required double price,
    String? imageUrl,
    String? category,
  }) async {
    try {
      final menuItemData = {
        'cafeteria_id': cafeteriaId,
        'name': name,
        'description': description,
        'price': price,
        'image_url': imageUrl,
        'is_available': true,
        'category': category,
        'created_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabaseService.client
          .from(_tableName)
          .insert(menuItemData)
          .select()
          .single();

      return SupabaseMenuItem.fromJson(response);
    } catch (e) {
      debugPrint('Error creating menu item: $e');
      return null;
    }
  }

  /// Update a menu item
  Future<SupabaseMenuItem?> updateMenuItem({
    required String id,
    String? name,
    String? description,
    double? price,
    String? imageUrl,
    bool? isAvailable,
    String? category,
  }) async {
    try {
      final updateData = <String, dynamic>{};

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (price != null) updateData['price'] = price;
      if (imageUrl != null) updateData['image_url'] = imageUrl;
      if (isAvailable != null) updateData['is_available'] = isAvailable;
      if (category != null) updateData['category'] = category;

      final response = await _supabaseService.client
          .from(_tableName)
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

      return SupabaseMenuItem.fromJson(response);
    } catch (e) {
      debugPrint('Error updating menu item: $e');
      return null;
    }
  }

  /// Delete a menu item (soft delete by setting is_available to false)
  Future<bool> deleteMenuItem(String id) async {
    try {
      await _supabaseService.client
          .from(_tableName)
          .update({'is_available': false}).eq('id', id);

      return true;
    } catch (e) {
      debugPrint('Error deleting menu item: $e');
      return false;
    }
  }

  /// Search menu items by name
  Future<List<SupabaseMenuItem>> searchMenuItems(
      String cafeteriaId, String query) async {
    try {
      final response = await _supabaseService.client
          .from(_tableName)
          .select()
          .eq('cafeteria_id', cafeteriaId)
          .eq('is_available', true)
          .ilike('name', '%$query%')
          .order('name');

      return response.map((json) => SupabaseMenuItem.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error searching menu items: $e');
      return [];
    }
  }

  /// Get all categories for a cafeteria
  Future<List<String>> getCategoriesByCafeteria(String cafeteriaId) async {
    try {
      final response = await _supabaseService.client
          .from(_tableName)
          .select('category')
          .eq('cafeteria_id', cafeteriaId)
          .eq('is_available', true)
          .not('category', 'is', null);

      final categories = response
          .map((item) => item['category'] as String)
          .toSet() // Remove duplicates
          .toList();

      return categories;
    } catch (e) {
      debugPrint('Error getting categories: $e');
      return [];
    }
  }
}
