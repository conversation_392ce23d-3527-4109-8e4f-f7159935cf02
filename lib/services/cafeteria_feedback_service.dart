import 'package:flutter/foundation.dart';
import '../models/cafeteria_feedback.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class CafeteriaFeedbackService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'cafeteria_feedback';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  Future<void> createCafeteriaFeedback(CafeteriaFeedback feedback) async {
    try {
      await init();
      await _supabaseService.insert(_table, feedback.toJson());
    } catch (e) {
      debugPrint('Error creating cafeteria feedback: $e');
      rethrow;
    }
  }

  Future<List<CafeteriaFeedback>> getCafeteriaFeedback() async {
    try {
      await init();
      final response = await _supabaseService.select(_table);
      return response
          .map((data) => CafeteriaFeedback.fromSupabase(data))
          .toList();
    } catch (e) {
      debugPrint('Error getting cafeteria feedback: $e');
      return [];
    }
  }

  Future<void> updateCafeteriaFeedback(
      String id, CafeteriaFeedback feedback) async {
    try {
      await init();
      await _supabaseService.update(_table, feedback.toJson(), {'id': id});
    } catch (e) {
      debugPrint('Error updating cafeteria feedback: $e');
      rethrow;
    }
  }

  Future<void> deleteCafeteriaFeedback(String id) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': id});
    } catch (e) {
      debugPrint('Error deleting cafeteria feedback: $e');
      rethrow;
    }
  }
}
