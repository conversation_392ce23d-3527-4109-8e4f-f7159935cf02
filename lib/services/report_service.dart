import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/report.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class ReportService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'reports';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  // Create a new report
  Future<void> createReport(Report report) async {
    try {
      await init();
      await _supabaseService.insert(_table, report.toJson());
    } catch (e) {
      debugPrint('Error creating report: $e');
      rethrow;
    }
  }

  // Get a report by ID
  Future<Report?> getReportById(String reportId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('id', reportId)
          .maybeSingle();

      if (response != null) {
        return Report.fromSupabase(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting report by ID: $e');
      return null;
    }
  }

  // Update a report
  Future<void> updateReport(String reportId, Report report) async {
    try {
      await init();
      await _supabaseService.update(_table, report.toJson(), {'id': reportId});
    } catch (e) {
      debugPrint('Error updating report: $e');
      rethrow;
    }
  }

  // Delete a report
  Future<void> deleteReport(String reportId) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': reportId});
    } catch (e) {
      debugPrint('Error deleting report: $e');
      rethrow;
    }
  }

  // Get reports by user
  Future<List<Report>> getReportsByUser(String userId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('userId', userId)
          .order('date', ascending: false);

      return response.map((data) => Report.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting reports by user: $e');
      return [];
    }
  }
}
