import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';

/// A custom authentication service that bypasses Supabase Auth
/// and directly manages users in the profiles table
class CustomAuthService {
  static final CustomAuthService _instance = CustomAuthService._internal();
  factory CustomAuthService() => _instance;
  CustomAuthService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Table names
  static const String _profilesTable = 'profiles';
  static const String _usersTable = 'custom_users';

  // Current user
  UserProfile? _currentUser;
  UserProfile? get currentUser => _currentUser;

  /// Initialize the service
  Future<void> init() async {
    await _supabaseService.init();

    // Create the custom_users table if it doesn't exist
    try {
      await _supabaseService.client
          .rpc('create_custom_users_table_if_not_exists');
    } catch (e) {
      debugPrint('Error creating custom_users table: $e');
      // Try to create the table directly
      try {
        await _supabaseService.client.from('custom_users').select().limit(1);
      } catch (e) {
        debugPrint('custom_users table does not exist, creating it');
        await _createCustomUsersTable();
      }
    }
  }

  /// Create the custom_users table
  Future<void> _createCustomUsersTable() async {
    try {
      await _supabaseService.client.rpc('create_custom_users_table');
    } catch (e) {
      debugPrint('Error creating custom_users table via RPC: $e');
    }
  }

  /// Hash a password
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Register a new user
  Future<UserProfile?> register({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      debugPrint('Registering user with email: $email, name: $fullName');

      // Check if user already exists
      final existingUser = await _supabaseService.client
          .from(_usersTable)
          .select()
          .eq('email', email)
          .maybeSingle();

      if (existingUser != null) {
        debugPrint('User with email $email already exists');
        return null;
      }

      // Get the student role ID
      final roleResponse = await _supabaseService.client
          .from('roles')
          .select('id')
          .eq('name', 'student')
          .single();

      final roleId = roleResponse['id'];
      debugPrint('Got student role ID: $roleId');

      // Generate a UUID for the user
      final userId = const Uuid().v4();

      // Hash the password
      final hashedPassword = _hashPassword(password);

      // Create the user in custom_users table
      await _supabaseService.client.from(_usersTable).insert({
        'id': userId,
        'email': email,
        'password_hash': hashedPassword,
        'created_at': DateTime.now().toIso8601String(),
      });

      // Create the profile
      await _supabaseService.client.from(_profilesTable).insert({
        'id': userId,
        'full_name': fullName,
        'role': roleId,
        'created_at': DateTime.now().toIso8601String(),
      });

      // Create and return the user profile
      final userProfile = UserProfile(
        id: userId,
        email: email,
        fullName: fullName,
        roleId: roleId,
        createdAt: DateTime.now(),
      );

      _currentUser = userProfile;
      return userProfile;
    } catch (e) {
      debugPrint('Error registering user: $e');
      return null;
    }
  }

  /// Login a user
  Future<UserProfile?> login({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('Logging in user with email: $email');

      // Hash the password
      final hashedPassword = _hashPassword(password);

      // Find the user
      final user = await _supabaseService.client
          .from(_usersTable)
          .select()
          .eq('email', email)
          .eq('password_hash', hashedPassword)
          .maybeSingle();

      if (user == null) {
        debugPrint('Invalid email or password');
        return null;
      }

      // Get the user profile
      final profile = await _supabaseService.client
          .from(_profilesTable)
          .select()
          .eq('id', user['id'])
          .single();

      // Create and return the user profile
      final userProfile = UserProfile.fromJson(profile);
      _currentUser = userProfile;
      return userProfile;
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  /// Logout the current user
  void logout() {
    _currentUser = null;
  }

  /// Check if a user is logged in
  bool get isLoggedIn => _currentUser != null;
}
