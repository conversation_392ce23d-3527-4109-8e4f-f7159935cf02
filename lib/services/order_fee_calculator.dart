import 'dart:math';

/// Utility class for calculating order fees
class OrderFeeCalculator {
  /// Calculate the subtotal for an order based on its items
  static double calculateSubtotal(List<OrderItem> items) {
    return items.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
  }

  /// Calculate the service fee (4% of subtotal, capped at 20 EGP)
  static double calculateServiceFee(double subtotal) {
    final fee = subtotal * 0.04;
    return min(fee, 20.0);
  }

  /// Calculate the cafeteria commission (10% of subtotal)
  static double calculateCafeteriaCommission(double subtotal) {
    return subtotal * 0.1;
  }

  /// Calculate the cafeteria revenue (subtotal minus commission)
  static double calculateCafeteriaRevenue(double subtotal) {
    final commission = calculateCafeteriaCommission(subtotal);
    return subtotal - commission;
  }

  /// Calculate the admin revenue (service fee + cafeteria commission)
  static double calculateAdminRevenue(double subtotal) {
    final serviceFee = calculateServiceFee(subtotal);
    final commission = calculateCafeteriaCommission(subtotal);
    return serviceFee + commission;
  }

  /// Calculate the total amount (subtotal + service fee)
  static double calculateTotalAmount(double subtotal) {
    final serviceFee = calculateServiceFee(subtotal);
    return subtotal + serviceFee;
  }

  /// Calculate all order fees based on order items
  static OrderFees calculateAllOrderFees(List<OrderItem> items) {
    final subtotal = calculateSubtotal(items);

    return OrderFees(
      subtotal: subtotal,
      userServiceFee: calculateServiceFee(subtotal),
      cafeteriaCommission: calculateCafeteriaCommission(subtotal),
      cafeteriaRevenue: calculateCafeteriaRevenue(subtotal),
      adminRevenue: calculateAdminRevenue(subtotal),
      totalAmount: calculateTotalAmount(subtotal),
    );
  }
}

/// Class representing an order item for fee calculation
class OrderItem {
  final double price;
  final int quantity;

  OrderItem({required this.price, required this.quantity});
}

/// Class representing all calculated order fees
class OrderFees {
  final double subtotal;
  final double userServiceFee;
  final double cafeteriaCommission;
  final double cafeteriaRevenue;
  final double adminRevenue;
  final double totalAmount;

  OrderFees({
    required this.subtotal,
    required this.userServiceFee,
    required this.cafeteriaCommission,
    required this.cafeteriaRevenue,
    required this.adminRevenue,
    required this.totalAmount,
  });

  /// Convert to a map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'subtotal': subtotal,
      'user_service_fee': userServiceFee,
      'cafeteria_commission': cafeteriaCommission,
      'cafeteria_revenue': cafeteriaRevenue,
      'admin_revenue': adminRevenue,
      'total_amount': totalAmount,
    };
  }

  /// Format currency for display with EGP
  static String formatCurrency(double value) {
    return '${value.toStringAsFixed(2)} EGP';
  }

  /// Format currency for display with Egyptian Pound symbol
  static String formatCurrencyWithSymbol(double value) {
    return 'ج.م ${value.toStringAsFixed(2)}';
  }
}
