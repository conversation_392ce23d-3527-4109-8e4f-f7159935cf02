import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/penalty_history.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class PenaltyHistoryService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'penalty_history';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  Future<void> createPenaltyHistory(PenaltyHistory penaltyHistory) async {
    try {
      await init();
      await _supabaseService.insert(_table, penaltyHistory.toJson());
    } catch (e) {
      debugPrint('Error creating penalty history: $e');
      rethrow;
    }
  }

  Future<List<PenaltyHistory>> getPenaltyHistory() async {
    try {
      await init();
      final response = await _supabaseService.select(_table);
      return response.map((data) => PenaltyHistory.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting penalty history: $e');
      return [];
    }
  }

  // Get penalty history by user ID
  Future<List<PenaltyHistory>> getPenaltyHistoryByUserId(String userId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('userId', userId)
          .order('penaltyDate', ascending: false);

      return response.map((data) => PenaltyHistory.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting penalty history by user ID: $e');
      return [];
    }
  }

  Future<void> updatePenaltyHistory(
      String id, PenaltyHistory penaltyHistory) async {
    try {
      await init();
      await _supabaseService
          .update(_table, penaltyHistory.toJson(), {'id': id});
    } catch (e) {
      debugPrint('Error updating penalty history: $e');
      rethrow;
    }
  }

  Future<void> deletePenaltyHistory(String id) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': id});
    } catch (e) {
      debugPrint('Error deleting penalty history: $e');
      rethrow;
    }
  }

  // Mark penalty as paid
  Future<void> markPenaltyAsPaid(String id) async {
    try {
      await init();
      await _supabaseService.update(_table, {
        'isPaid': true,
        'paidDate': DateTime.now().toIso8601String(),
        'penaltyStatus': 'Paid',
      }, {
        'id': id
      });
    } catch (e) {
      debugPrint('Error marking penalty as paid: $e');
      rethrow;
    }
  }

  // Get total unpaid penalties for a user
  Future<double> getTotalUnpaidPenalties(String userId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('userId', userId)
          .eq('isPaid', false);

      double total = 0.0;
      for (var data in response) {
        total += (data['amount'] ?? 0.0).toDouble();
      }

      return total;
    } catch (e) {
      debugPrint('Error getting total unpaid penalties: $e');
      return 0.0;
    }
  }
}
