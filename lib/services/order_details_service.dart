import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/order_details.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class OrderDetailsService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'order_details';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  Future<void> createOrderDetails(OrderDetails orderDetails) async {
    try {
      await init();
      await _supabaseService.insert(_table, orderDetails.toJson());
    } catch (e) {
      debugPrint('Error creating order details: $e');
      rethrow;
    }
  }

  Future<List<OrderDetails>> getOrderDetails() async {
    try {
      await init();
      final response = await _supabaseService.select(_table);
      return response.map((data) => OrderDetails.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting order details: $e');
      return [];
    }
  }

  // Get order details by order ID
  Future<List<OrderDetails>> getOrderDetailsByOrderId(String orderId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('orderId', orderId);

      return response.map((data) => OrderDetails.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting order details by order ID: $e');
      return [];
    }
  }

  Future<void> updateOrderDetails(String id, OrderDetails orderDetails) async {
    try {
      await init();
      await _supabaseService.update(_table, orderDetails.toJson(), {'id': id});
    } catch (e) {
      debugPrint('Error updating order details: $e');
      rethrow;
    }
  }

  Future<void> deleteOrderDetails(String id) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': id});
    } catch (e) {
      debugPrint('Error deleting order details: $e');
      rethrow;
    }
  }

  // Get order details by item ID
  Future<List<OrderDetails>> getOrderDetailsByItemId(String itemId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('itemId', itemId);

      return response.map((data) => OrderDetails.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting order details by item ID: $e');
      return [];
    }
  }
}
