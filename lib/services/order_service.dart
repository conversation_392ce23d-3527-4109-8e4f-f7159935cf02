import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class OrderService {
  final SupabaseService _supabaseService = SupabaseService();
  final String _table = 'orders';

  // Initialize Supabase
  Future<void> init() async {
    await _supabaseService.init();
  }

  // Create a new order
  Future<void> createOrder(Order order) async {
    try {
      await init();
      await _supabaseService.insert(_table, order.toJson());
    } catch (e) {
      debugPrint('Error creating order: $e');
      rethrow;
    }
  }

  // Get an order by ID
  Future<Order?> getOrderById(String orderId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('id', orderId)
          .maybeSingle();

      if (response != null) {
        return Order.fromSupabase(response);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting order by ID: $e');
      return null;
    }
  }

  // Update an existing order
  Future<void> updateOrder(String orderId, Order order) async {
    try {
      await init();
      await _supabaseService.update(_table, order.toJson(), {'id': orderId});
    } catch (e) {
      debugPrint('Error updating order: $e');
      rethrow;
    }
  }

  // Delete an order
  Future<void> deleteOrder(String orderId) async {
    try {
      await init();
      await _supabaseService.delete(_table, {'id': orderId});
    } catch (e) {
      debugPrint('Error deleting order: $e');
      rethrow;
    }
  }

  // Get orders for a user
  Future<List<Order>> getOrdersForUser(String userId) async {
    try {
      await init();
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('userId', userId)
          .order('orderDate', ascending: false);

      return response.map((data) => Order.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting orders for user: $e');
      return [];
    }
  }

  // Get orders for a cafeteria
  Future<List<Order>> getOrdersForCafeteria(String cafeteriaId) async {
    try {
      await init();
      // This is a simplification - in Supabase you might need a different approach
      // to query JSON arrays. This assumes you have a cafeteriaId field in the orders table.
      final response = await _supabaseService.client
          .from(_table)
          .select()
          .eq('cafeteriaId', cafeteriaId)
          .order('orderDate', ascending: false);

      return response.map((data) => Order.fromSupabase(data)).toList();
    } catch (e) {
      debugPrint('Error getting orders for cafeteria: $e');
      return [];
    }
  }
}
