import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/config/env_config.dart';

/// Service class for Supabase operations
class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  // Supabase client instance
  late final SupabaseClient _client;

  // Supabase project constants from environment config
  static const String _supabaseUrl = EnvConfig.supabaseUrl;

  bool _initialized = false;

  /// Initialize Supabase client
  Future<void> init() async {
    if (_initialized) {
      debugPrint('Supabase already initialized, skipping initialization');
      _client = Supabase.instance.client;
      return;
    }

    try {
      debugPrint('Initializing Supabase with URL: $_supabaseUrl');

      // Force initialization with the correct API key
      try {
        // Try to get the existing client first
        _client = Supabase.instance.client;
        debugPrint('Supabase already initialized, using existing client');

        // Test the connection
        final testResponse = await _client.from('cafeterias').select().limit(1);
        debugPrint(
            'Test query successful: ${testResponse.length} cafeterias found');
      } catch (e) {
        debugPrint(
            'Error with existing client or Supabase not initialized: $e');

        // Force re-initialization with the correct API key
        await Supabase.initialize(
          url: 'https://lqtnaxvqkoynaziiinqh.supabase.co',
          anonKey:
              'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxxdG5heHZxa295bmF6aWlpbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1ODUzMjcsImV4cCI6MjA2MzE2MTMyN30.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA',
          debug: true,
        );
        _client = Supabase.instance.client;
        debugPrint('Supabase reinitialized with direct config');

        // Test the connection again
        final testResponse = await _client.from('cafeterias').select().limit(1);
        debugPrint(
            'Test query after reinitialization successful: ${testResponse.length} cafeterias found');
      }

      _initialized = true;
      debugPrint('Supabase client initialized successfully');

      // Ensure storage buckets exist
      await _ensureStorageBuckets();
    } catch (e) {
      debugPrint('Error initializing Supabase: $e');
      rethrow;
    }
  }

  /// Ensure required storage buckets exist
  Future<void> _ensureStorageBuckets() async {
    try {
      final buckets = [
        EnvConfig.profileImagesBucket,
        EnvConfig.menuItemImagesBucket,
        EnvConfig.cafeteriaImagesBucket,
      ];

      for (final bucket in buckets) {
        try {
          // Try to get bucket info to check if it exists
          await storage.getBucket(bucket);
          debugPrint('Bucket $bucket exists');
        } catch (e) {
          // Bucket doesn't exist, create it
          debugPrint('Creating bucket $bucket');
          await storage.createBucket(
              bucket,
              const BucketOptions(
                public: true, // Make bucket publicly accessible
              ));
        }
      }
    } catch (e) {
      debugPrint('Error ensuring storage buckets: $e');
      // Don't rethrow, as this is not critical for app initialization
    }
  }

  /// Get Supabase client instance
  SupabaseClient get client {
    if (!_initialized) {
      throw Exception('Supabase not initialized. Call init() first.');
    }
    return _client;
  }

  /// Get Supabase auth instance
  GoTrueClient get auth => client.auth;

  /// Get Supabase storage instance
  SupabaseStorageClient get storage => client.storage;

  /// Sign up a new user
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    return await auth.signUp(
      email: email,
      password: password,
      data: data,
    );
  }

  /// Sign in a user with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    return await auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  /// Sign out the current user
  Future<void> signOut() async {
    await auth.signOut();
  }

  /// Get the current user
  User? get currentUser => auth.currentUser;

  /// Check if a user is signed in
  bool get isAuthenticated => auth.currentUser != null;

  /// Insert data into a table
  Future<List<Map<String, dynamic>>> insert(
      String table, Map<String, dynamic> data) async {
    final response = await client.from(table).insert(data).select();
    return response;
  }

  /// Update data in a table
  Future<List<Map<String, dynamic>>> update(String table,
      Map<String, dynamic> data, Map<String, dynamic> conditions) async {
    var query = client.from(table).update(data);

    conditions.forEach((key, value) {
      query = query.eq(key, value);
    });

    final response = await query.select();
    return response;
  }

  /// Delete data from a table
  Future<List<Map<String, dynamic>>> delete(
      String table, Map<String, dynamic> conditions) async {
    var query = client.from(table).delete();

    conditions.forEach((key, value) {
      query = query.eq(key, value);
    });

    final response = await query.select();
    return response;
  }

  /// Select data from a table
  Future<List<Map<String, dynamic>>> select(
    String table, {
    List<String>? columns,
    Map<String, dynamic>? conditions,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    try {
      // Build the query string
      String queryStr = columns?.join(',') ?? '*';

      // Execute the query
      var query = client.from(table).select(queryStr);

      // Apply conditions if any
      if (conditions != null) {
        for (var entry in conditions.entries) {
          query = query.eq(entry.key, entry.value);
        }
      }

      // Get the data
      var data = await query;

      // Apply ordering if specified
      if (orderBy != null && data.isNotEmpty) {
        data.sort((a, b) {
          var aValue = a[orderBy];
          var bValue = b[orderBy];

          if (aValue == null && bValue == null) return 0;
          if (aValue == null) return ascending ? -1 : 1;
          if (bValue == null) return ascending ? 1 : -1;

          int compareResult;
          if (aValue is String && bValue is String) {
            compareResult = aValue.compareTo(bValue);
          } else if (aValue is num && bValue is num) {
            compareResult = aValue.compareTo(bValue);
          } else if (aValue is DateTime && bValue is DateTime) {
            compareResult = aValue.compareTo(bValue);
          } else if (aValue is bool && bValue is bool) {
            compareResult = aValue == bValue ? 0 : (aValue ? 1 : -1);
          } else {
            // Convert to string for comparison if types don't match
            compareResult = aValue.toString().compareTo(bValue.toString());
          }

          return ascending ? compareResult : -compareResult;
        });
      }

      // Apply limit if specified
      if (limit != null && limit > 0 && data.length > limit) {
        data = data.sublist(0, limit);
      }

      return data;
    } catch (e) {
      debugPrint('Error in select: $e');
      rethrow;
    }
  }

  /// Upload a file to Supabase Storage
  Future<String> uploadFile(
    String bucket,
    String path,
    Uint8List fileBytes,
    String? contentType,
  ) async {
    final response = await storage.from(bucket).uploadBinary(
          path,
          fileBytes,
          fileOptions: FileOptions(contentType: contentType),
        );

    return storage.from(bucket).getPublicUrl(response);
  }

  /// Delete a file from Supabase Storage
  Future<void> deleteFile(String bucket, String path) async {
    await storage.from(bucket).remove([path]);
  }
}
