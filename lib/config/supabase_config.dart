import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/config/env_config.dart';

/// Shared Supabase configuration for both mobile and web apps
class SupabaseConfig {
  static final SupabaseConfig _instance = SupabaseConfig._internal();
  factory SupabaseConfig() => _instance;
  SupabaseConfig._internal();

  // Supabase project constants from environment config
  static const String supabaseUrl = EnvConfig.supabaseUrl;
  static const String supabaseAnonKey = EnvConfig.supabaseAnonKey;

  // Storage buckets
  static const String profileImagesBucket = EnvConfig.profileImagesBucket;
  static const String menuItemImagesBucket = EnvConfig.menuItemImagesBucket;
  static const String cafeteriaImagesBucket = EnvConfig.cafeteriaImagesBucket;

  // Table names - ensure these are the same across mobile and web
  static const String usersTable = 'users';
  static const String cafeteriasTable = 'cafeterias';
  static const String menuItemsTable = 'menu_items';
  static const String ordersTable = 'orders';
  static const String orderDetailsTable = 'order_details';
  static const String cafeteriaFeedbackTable = 'cafeteria_feedback';
  static const String userFeedbackTable = 'user_feedback';
  static const String cafeteriaUsersTable = 'cafeteria_users';

  // Initialize Supabase for both mobile and web
  static Future<void> initialize() async {
    try {
      debugPrint('Starting Supabase initialization...');

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: kDebugMode,
        // Enable real-time subscriptions for both platforms
        realtimeClientOptions: const RealtimeClientOptions(
          eventsPerSecond: 10,
        ),
      );

      debugPrint('Supabase initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Supabase: $e');
      rethrow;
    }
  }

  // Get Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;

  // Get Supabase auth instance
  static GoTrueClient get auth => client.auth;

  // Get Supabase storage instance
  static SupabaseStorageClient get storage => client.storage;

  // Subscribe to real-time changes for a table
  static RealtimeChannel subscribeToTable(
      String table, Function(Map<String, dynamic> payload) callback) {
    try {
      // Create a channel for this table
      final channel = client.channel('public:$table');

      // Set up the subscription
      channel
          .onPostgresChanges(
            schema: 'public',
            table: table,
            event: PostgresChangeEvent.all,
            callback: (payload) {
              debugPrint(
                  'Real-time update for table $table: ${payload.toString()}');
              callback(payload.newRecord);
            },
          )
          .subscribe();

      return channel;
    } catch (e) {
      debugPrint('Error setting up real-time subscription for $table: $e');
      rethrow;
    }
  }

  // Unsubscribe from real-time changes
  static Future<void> unsubscribe(RealtimeChannel channel) async {
    await channel.unsubscribe();
  }
}
