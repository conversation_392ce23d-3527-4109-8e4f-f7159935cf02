import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/config/supabase_config.dart';

/// Utility class for testing the notification system
class NotificationTestUtils {
  /// Test the notification system by creating a test notification in Supabase
  static Future<void> testNotificationSystem(BuildContext context) async {
    // Get the current user
    final currentUser = SupabaseConfig.client.auth.currentUser;
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User not logged in')),
      );
      return;
    }

    try {
      // Create a test notification directly in Supabase
      final response = await SupabaseConfig.client.from('notifications').insert({
        'user_id': currentUser.id,
        'title': 'Test Notification',
        'message': 'This is a test notification from the system.',
        'is_read': false,
        'created_at': DateTime.now().toIso8601String(),
      });

      // Check if the widget is still mounted before using BuildContext
      if (!context.mounted) return;

      if (response.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${response.error!.message}')),
        );
      } else {
        // Refresh notifications
        final notificationProvider =
            Provider.of<NotificationProvider>(context, listen: false);
        await notificationProvider.refresh();

        // Check if the widget is still mounted before using BuildContext
        if (!context.mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Test notification created successfully')),
        );
      }
    } catch (e) {
      // Check if the widget is still mounted before using BuildContext
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  /// Test the order status notification system by simulating an order status change
  static Future<void> testOrderStatusNotification(
      BuildContext context, String orderId, String newStatus) async {
    try {
      // Update the order status in Supabase
      final response = await SupabaseConfig.client
          .from('orders')
          .update({'status': newStatus})
          .eq('id', orderId);

      // Check if the widget is still mounted before using BuildContext
      if (!context.mounted) return;

      if (response.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${response.error!.message}')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Order status updated to $newStatus successfully')),
        );
      }
    } catch (e) {
      // Check if the widget is still mounted before using BuildContext
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }
}
