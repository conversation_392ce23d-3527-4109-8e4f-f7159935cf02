import 'package:flutter/material.dart';

/// Utility class for handling order statuses consistently across the app
class OrderStatusUtils {
  /// Standardized order status values
  static const String statusNew = 'new';
  static const String statusPending = 'pending';
  static const String statusPreparing = 'preparing';
  static const String statusReady = 'ready';
  static const String statusReadyForPickup = 'Ready for pickup';
  static const String statusCompleted = 'completed';
  static const String statusCompletedDisplay = 'Completed';
  static const String statusCancelled = 'cancelled';
  static const String statusCancelledDisplay = 'Cancelled';

  /// Normalize status values to handle 'new' and 'pending' equivalence
  static String normalizeStatus(String status) {
    switch (status.toLowerCase()) {
      case 'new':
        return statusPending; // Normalize 'new' to 'pending'
      default:
        return status.toLowerCase();
    }
  }

  /// Convert database status to display status
  static String getDisplayStatus(String dbStatus) {
    switch (dbStatus.toLowerCase()) {
      case 'new':
      case 'pending':
        return 'Pending'; // Treat both 'new' and 'pending' as 'Pending' for consistency
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return statusReadyForPickup;
      case 'completed':
        return statusCompletedDisplay;
      case 'cancelled':
        return statusCancelledDisplay;
      default:
        return dbStatus;
    }
  }

  /// Convert display status to database status
  static String getDatabaseStatus(String displayStatus) {
    switch (displayStatus) {
      case 'New':
      case 'Pending':
        return statusPending; // Always use 'pending' for database consistency
      case 'Preparing':
        return statusPreparing;
      case 'Ready for pickup':
        return statusReady;
      case 'Completed':
        return statusCompleted;
      case 'Cancelled':
        return statusCancelled;
      default:
        return displayStatus.toLowerCase();
    }
  }

  /// Get color for order status
  static Color getStatusColor(String status, BuildContext context) {
    final theme = Theme.of(context);

    switch (status.toLowerCase()) {
      case 'new':
      case 'pending':
        return Colors.blue;
      case 'preparing':
        return Colors.orange;
      case 'ready':
        return Colors.green;
      case 'completed':
        return theme.primaryColor;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Get icon for order status
  static IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'new':
      case 'pending':
        return Icons.receipt_long;
      case 'preparing':
        return Icons.restaurant;
      case 'ready':
        return Icons.check_circle_outline;
      case 'completed':
        return Icons.done_all;
      case 'cancelled':
        return Icons.cancel_outlined;
      default:
        return Icons.help_outline;
    }
  }

  /// Check if order status can be updated to the new status
  static bool canUpdateStatus(String currentStatus, String newStatus) {
    // Convert to lowercase for comparison
    final current = currentStatus.toLowerCase();
    final next = newStatus.toLowerCase();

    // Define valid transitions
    switch (current) {
      case 'new':
        return ['preparing', 'cancelled'].contains(next);
      case 'pending':
        return ['preparing', 'cancelled'].contains(next);
      case 'preparing':
        return ['ready', 'cancelled'].contains(next);
      case 'ready':
        return ['completed', 'cancelled'].contains(next);
      case 'completed':
        return false; // Cannot change from completed
      case 'cancelled':
        return false; // Cannot change from cancelled
      default:
        return false;
    }
  }

  /// Get next possible statuses for the current status
  static List<String> getNextPossibleStatuses(String currentStatus) {
    // Convert to lowercase for comparison
    final current = currentStatus.toLowerCase();

    // Define valid transitions
    switch (current) {
      case 'new':
        return ['preparing', 'cancelled'];
      case 'pending':
        return ['preparing', 'cancelled'];
      case 'preparing':
        return ['ready', 'cancelled'];
      case 'ready':
        return ['completed', 'cancelled'];
      case 'completed':
        return []; // No next status
      case 'cancelled':
        return []; // No next status
      default:
        return [];
    }
  }

  /// Get progress value (0.0 to 1.0) for order status
  static double getProgressValue(String status) {
    switch (status.toLowerCase()) {
      case 'new':
      case 'pending':
        return 0.0;
      case 'preparing':
        return 0.33;
      case 'ready':
        return 0.66;
      case 'completed':
        return 1.0;
      case 'cancelled':
        return 0.0;
      default:
        return 0.0;
    }
  }
}
