import 'package:flutter/material.dart';

class SnackBarUtils {
  /// Shows a custom styled SnackBar
  static void showCustomSnackBar({
    required BuildContext context,
    required String message,
    String? title,
    IconData? icon,
    Color backgroundColor = Colors.black87,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
    bool floating = true,
    double? width,
    EdgeInsetsGeometry? margin,
    ShapeBorder? shape,
  }) {
    final snackBar = SnackBar(
      content: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null) ...[
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                Text(
                  message,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: backgroundColor,
      duration: duration,
      action: action,
      behavior: floating ? SnackBarBehavior.floating : SnackBarBehavior.fixed,
      width: width,
      margin: margin ?? (floating ? const EdgeInsets.all(16) : null),
      shape: shape ??
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
    );

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  /// Shows a success SnackBar
  static void showSuccessSnackBar({
    required BuildContext context,
    required String message,
    String? title,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    showCustomSnackBar(
      context: context,
      message: message,
      title: title,
      icon: Icons.check_circle,
      backgroundColor: Colors.green.shade800,
      duration: duration,
      action: action,
    );
  }

  /// Shows an error SnackBar
  static void showErrorSnackBar({
    required BuildContext context,
    required String message,
    String? title,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    showCustomSnackBar(
      context: context,
      message: message,
      title: title,
      icon: Icons.error,
      backgroundColor: Colors.red.shade800,
      duration: duration,
      action: action,
    );
  }

  /// Shows an info SnackBar
  static void showInfoSnackBar({
    required BuildContext context,
    required String message,
    String? title,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    showCustomSnackBar(
      context: context,
      message: message,
      title: title,
      icon: Icons.info,
      backgroundColor: Colors.blue.shade800,
      duration: duration,
      action: action,
    );
  }

  /// Shows a warning SnackBar
  static void showWarningSnackBar({
    required BuildContext context,
    required String message,
    String? title,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    showCustomSnackBar(
      context: context,
      message: message,
      title: title,
      icon: Icons.warning,
      backgroundColor: Colors.orange.shade800,
      duration: duration,
      action: action,
    );
  }

  /// Shows a cart notification SnackBar
  static void showCartSnackBar({
    required BuildContext context,
    required String message,
    VoidCallback? onViewCart,
  }) {
    showCustomSnackBar(
      context: context,
      message: message,
      title: 'Cart Updated',
      icon: Icons.shopping_cart,
      backgroundColor: Colors.deepPurple.shade800,
      action: onViewCart != null
          ? SnackBarAction(
              label: 'VIEW CART',
              textColor: Colors.white,
              onPressed: onViewCart,
            )
          : null,
    );
  }

  /// Shows an order notification SnackBar
  static void showOrderSnackBar({
    required BuildContext context,
    required String message,
    required String status,
    VoidCallback? onViewOrder,
  }) {
    IconData icon;
    Color color;

    final lowerStatus = status.toLowerCase();

    if (lowerStatus.contains('ready') || lowerStatus.contains('pickup')) {
      icon = Icons.check_circle;
      color = Colors.green.shade800;
    } else if (lowerStatus.contains('complete') ||
        lowerStatus.contains('done')) {
      icon = Icons.done_all;
      color = Colors.blue.shade800;
    } else if (lowerStatus.contains('cancel')) {
      icon = Icons.cancel;
      color = Colors.red.shade800;
    } else if (lowerStatus.contains('prepar')) {
      icon = Icons.restaurant;
      color = Colors.orange.shade800;
    } else {
      icon = Icons.receipt_long;
      color = Colors.deepPurple.shade800;
    }

    showCustomSnackBar(
      context: context,
      message: message,
      title: 'Order $status',
      icon: icon,
      backgroundColor: color,
      action: onViewOrder != null
          ? SnackBarAction(
              label: 'VIEW ORDER',
              textColor: Colors.white,
              onPressed: onViewOrder,
            )
          : null,
    );
  }
}
