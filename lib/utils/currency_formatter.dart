/// Currency formatting utilities for the UniEats app
class CurrencyFormatter {
  /// Format a double value as Egyptian Pounds
  static String formatEGP(double value) {
    return '${value.toStringAsFixed(2)} EGP';
  }

  /// Format a double value with Egyptian Pound symbol
  static String formatEGPWithSymbol(double value) {
    return 'ج.م ${value.toStringAsFixed(2)}';
  }

  /// Format a double value as currency without symbol (just the number)
  static String formatNumber(double value) {
    return value.toStringAsFixed(2);
  }

  /// Parse a currency string back to double
  static double parseEGP(String currencyString) {
    // Remove 'EGP' and any whitespace, then parse
    final cleanString = currencyString
        .replaceAll('EGP', '')
        .replaceAll('ج.م', '')
        .trim();
    return double.tryParse(cleanString) ?? 0.0;
  }

  /// Format currency for display in lists (shorter format)
  static String formatCompact(double value) {
    if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K EGP';
    }
    return formatEGP(value);
  }
}
