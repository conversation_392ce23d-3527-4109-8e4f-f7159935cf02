import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Modern animation utilities using only built-in Flutter animations
class ModernAnimations {
  
  /// Standard animation durations
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration medium = Duration(milliseconds: 400);
  static const Duration slow = Duration(milliseconds: 600);
  
  /// Standard animation curves
  static const Curve easeInOutCubic = Curves.easeInOutCubic;
  static const Curve easeOutBack = Curves.easeOutBack;
  static const Curve elasticOut = Curves.elasticOut;

  /// Fade in animation
  static Widget fadeIn({
    required Widget child,
    Duration duration = medium,
    Curve curve = easeInOutCubic,
    Duration delay = Duration.zero,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration + delay,
      curve: curve,
      builder: (context, value, child) {
        final adjustedValue = delay == Duration.zero 
            ? value 
            : math.max(0.0, (value - delay.inMilliseconds / (duration + delay).inMilliseconds) * 
                (duration + delay).inMilliseconds / duration.inMilliseconds);
        return Opacity(
          opacity: adjustedValue.clamp(0.0, 1.0),
          child: child,
        );
      },
      child: child,
    );
  }

  /// Slide in from bottom animation
  static Widget slideInFromBottom({
    required Widget child,
    Duration duration = medium,
    Curve curve = easeInOutCubic,
    double offset = 50.0,
    Duration delay = Duration.zero,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration + delay,
      curve: curve,
      builder: (context, value, child) {
        final adjustedValue = delay == Duration.zero 
            ? value 
            : math.max(0.0, (value - delay.inMilliseconds / (duration + delay).inMilliseconds) * 
                (duration + delay).inMilliseconds / duration.inMilliseconds);
        return Transform.translate(
          offset: Offset(0, offset * (1 - adjustedValue.clamp(0.0, 1.0))),
          child: Opacity(
            opacity: adjustedValue.clamp(0.0, 1.0),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// Slide in from right animation
  static Widget slideInFromRight({
    required Widget child,
    Duration duration = medium,
    Curve curve = easeInOutCubic,
    double offset = 50.0,
    Duration delay = Duration.zero,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration + delay,
      curve: curve,
      builder: (context, value, child) {
        final adjustedValue = delay == Duration.zero 
            ? value 
            : math.max(0.0, (value - delay.inMilliseconds / (duration + delay).inMilliseconds) * 
                (duration + delay).inMilliseconds / duration.inMilliseconds);
        return Transform.translate(
          offset: Offset(offset * (1 - adjustedValue.clamp(0.0, 1.0)), 0),
          child: Opacity(
            opacity: adjustedValue.clamp(0.0, 1.0),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// Scale in animation
  static Widget scaleIn({
    required Widget child,
    Duration duration = medium,
    Curve curve = easeOutBack,
    double initialScale = 0.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: initialScale, end: 1.0),
      duration: duration,
      curve: curve,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: child,
        );
      },
      child: child,
    );
  }

  /// Bounce animation
  static Widget bounce({
    required Widget child,
    Duration duration = const Duration(seconds: 2),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        final scale = minScale + (maxScale - minScale) * (0.5 + 0.5 * math.sin(value * 2 * math.pi));
        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
      onEnd: () {
        // Animation will restart automatically due to TweenAnimationBuilder
      },
      child: child,
    );
  }

  /// Rotation animation
  static Widget rotate({
    required Widget child,
    Duration duration = const Duration(seconds: 2),
    double turns = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: turns),
      duration: duration,
      builder: (context, value, child) {
        return Transform.rotate(
          angle: value * 2 * math.pi,
          child: child,
        );
      },
      child: child,
    );
  }

  /// Shimmer effect
  static Widget shimmer({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1500),
    Color baseColor = const Color(0xFFE0E0E0),
    Color highlightColor = const Color(0xFFF5F5F5),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                (value - 0.3).clamp(0.0, 1.0),
                value,
                (value + 0.3).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      onEnd: () {
        // Animation will restart automatically
      },
      child: child,
    );
  }

  /// Staggered animation for lists
  static Widget staggeredAnimation({
    required Widget child,
    required int index,
    Duration delay = const Duration(milliseconds: 100),
    Duration duration = medium,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: duration.inMilliseconds + (index * delay.inMilliseconds)),
      curve: easeInOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// Page transition animation
  static PageRouteBuilder<T> createRoute<T>({
    required Widget page,
    Duration duration = medium,
    RouteTransitionsBuilder? transitionsBuilder,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: transitionsBuilder ?? (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.ease;

        var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// Fade page transition
  static PageRouteBuilder<T> fadeRoute<T>({
    required Widget page,
    Duration duration = medium,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }

  /// Scale page transition
  static PageRouteBuilder<T> scaleRoute<T>({
    required Widget page,
    Duration duration = medium,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.fastOutSlowIn,
          )),
          child: child,
        );
      },
    );
  }
}
