import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/screens/splash_screen.dart';
import 'package:unieatsappv0/widgets/supabase_loading_wrapper.dart';
import 'package:unieatsappv0/widgets/order_sync_wrapper.dart';

/// A wrapper component that ensures Supabase is initialized
/// before showing any screens
class AppWrapper extends StatelessWidget {
  final Widget child;

  const AppWrapper({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use both providers to ensure we have both authentication and data
    return Consumer2<SupabaseProvider, SimpleAuthProvider>(
      builder: (context, supabaseProvider, authProvider, _) {
        // Show loading indicator while Supabase is initializing
        if (supabaseProvider.isLoading) {
          return const SplashScreen();
        }

        // Show error screen if there was an error initializing Supabase
        if (supabaseProvider.error != null) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error initializing app',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    supabaseProvider.error!,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      // Retry initialization
                      supabaseProvider.initialize();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        // Check if user is authenticated with SimpleAuthProvider
        if (!authProvider.isAuthenticated) {
          // Only redirect to login if we're not already on a login-related screen
          final currentRoute = ModalRoute.of(context)?.settings.name;
          debugPrint('Current route: $currentRoute');

          // Don't redirect if we're on login, signup, or in the middle of navigation
          if (currentRoute != '/login' &&
              currentRoute != '/signup' &&
              currentRoute != '/cafeteria') {
            // If not authenticated, redirect to login screen
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (context.mounted) {
                Navigator.of(context).pushReplacementNamed('/login');
              }
            });
            // Show a loading screen while redirecting
            return const SplashScreen();
          }
        }

        // Show the main app once Supabase is initialized and user is authenticated
        // Wrap with loading overlay and order sync service
        return SupabaseLoadingWrapper(
          child: OrderSyncWrapper(child: child),
        );
      },
    );
  }
}
