import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:unieatsappv0/services/favorites_service.dart';
import 'package:unieatsappv0/services/chat_service.dart';
import 'package:unieatsappv0/models/supabase_models.dart';


class DatabaseCompatibilityCheck extends StatefulWidget {
  const DatabaseCompatibilityCheck({super.key});

  @override
  State<DatabaseCompatibilityCheck> createState() => _DatabaseCompatibilityCheckState();
}

class _DatabaseCompatibilityCheckState extends State<DatabaseCompatibilityCheck> {
  final SupabaseService _supabaseService = SupabaseService();
  final FavoritesService _favoritesService = FavoritesService();
  final ChatService _chatService = ChatService();

  Map<String, bool> _testResults = {};
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Compatibility Check'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Database Schema Compatibility Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'This test verifies that all new components are compatible with the Supabase database schema.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),

            ElevatedButton(
              onPressed: _isRunning ? null : _runCompatibilityTests,
              child: _isRunning
                  ? const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Running Tests...'),
                      ],
                    )
                  : const Text('Run Compatibility Tests'),
            ),

            const SizedBox(height: 24),

            Expanded(
              child: ListView(
                children: [
                  _buildTestResult('Cafeterias Table Schema', 'cafeterias'),
                  _buildTestResult('Menu Items Table Schema', 'menu_items'),
                  _buildTestResult('Favorites Table Schema', 'favorites'),
                  _buildTestResult('Chat Conversations Schema', 'chat_conversations'),
                  _buildTestResult('Chat Messages Schema', 'chat_messages'),
                  _buildTestResult('Orders Table Schema', 'orders'),
                  _buildTestResult('Order Items Schema', 'order_items'),
                  _buildTestResult('Menu Item Rating Schema', 'menu_item_rating'),
                  _buildTestResult('Favorites Service Compatibility', 'favorites_service'),
                  _buildTestResult('Chat Service Compatibility', 'chat_service'),
                  _buildTestResult('Search Provider Compatibility', 'search_provider'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResult(String testName, String testKey) {
    final result = _testResults[testKey];

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(testName),
        trailing: result == null
            ? const Icon(Icons.help_outline, color: Colors.grey)
            : result
                ? const Icon(Icons.check_circle, color: Colors.green)
                : const Icon(Icons.error, color: Colors.red),
        subtitle: result == null
            ? const Text('Not tested')
            : result
                ? const Text('Compatible')
                : const Text('Compatibility issues found'),
      ),
    );
  }

  Future<void> _runCompatibilityTests() async {
    setState(() {
      _isRunning = true;
      _testResults.clear();
    });

    try {
      // Test cafeterias table
      await _testCafeteriasSchema();

      // Test menu items table
      await _testMenuItemsSchema();

      // Test favorites table
      await _testFavoritesSchema();

      // Test chat tables
      await _testChatSchema();

      // Test orders tables
      await _testOrdersSchema();

      // Test service compatibility
      await _testServicesCompatibility();

    } catch (e) {
      debugPrint('Error running compatibility tests: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _testCafeteriasSchema() async {
    try {
      final response = await _supabaseService.client
          .from('cafeterias')
          .select('id, name, description, image_url, location, rating, created_at, is_active')
          .limit(1);

      // Test if we can create a SupabaseCafeteria from the response
      if (response.isNotEmpty) {
        SupabaseCafeteria.fromJson(response.first);
      }

      setState(() {
        _testResults['cafeterias'] = true;
      });
    } catch (e) {
      debugPrint('Cafeterias schema test failed: $e');
      setState(() {
        _testResults['cafeterias'] = false;
      });
    }
  }

  Future<void> _testMenuItemsSchema() async {
    try {
      final response = await _supabaseService.client
          .from('menu_items')
          .select('id, cafeteria_id, name, description, price, image_url, is_available, category, created_at')
          .limit(1);

      // Test if we can create a SupabaseMenuItem from the response
      if (response.isNotEmpty) {
        SupabaseMenuItem.fromJson(response.first);
      }

      setState(() {
        _testResults['menu_items'] = true;
      });
    } catch (e) {
      debugPrint('Menu items schema test failed: $e');
      setState(() {
        _testResults['menu_items'] = false;
      });
    }
  }

  Future<void> _testFavoritesSchema() async {
    try {
      final response = await _supabaseService.client
          .from('favorites')
          .select('id, user_id, menu_item_id, created_at')
          .limit(1);

      setState(() {
        _testResults['favorites'] = true;
      });
    } catch (e) {
      debugPrint('Favorites schema test failed: $e');
      setState(() {
        _testResults['favorites'] = false;
      });
    }
  }

  Future<void> _testChatSchema() async {
    try {
      // Test chat_conversations
      await _supabaseService.client
          .from('chat_conversations')
          .select('id, user_id, subject, category, priority, status, created_at')
          .limit(1);

      // Test chat_messages
      await _supabaseService.client
          .from('chat_messages')
          .select('id, conversation_id, sender_id, message_type, content, file_url, created_at')
          .limit(1);

      setState(() {
        _testResults['chat_conversations'] = true;
        _testResults['chat_messages'] = true;
      });
    } catch (e) {
      debugPrint('Chat schema test failed: $e');
      setState(() {
        _testResults['chat_conversations'] = false;
        _testResults['chat_messages'] = false;
      });
    }
  }

  Future<void> _testOrdersSchema() async {
    try {
      // Test orders
      await _supabaseService.client
          .from('orders')
          .select('id, student_id, cafeteria_id, total_amount, status, created_at, updated_at')
          .limit(1);

      // Test order_items
      await _supabaseService.client
          .from('order_items')
          .select('id, order_id, item_id, quantity, selected_variant, price')
          .limit(1);

      // Test menu_item_rating
      await _supabaseService.client
          .from('menu_item_rating')
          .select('id, user_id, menu_item_id, order_id, rating, review_comment, created_at')
          .limit(1);

      setState(() {
        _testResults['orders'] = true;
        _testResults['order_items'] = true;
        _testResults['menu_item_rating'] = true;
      });
    } catch (e) {
      debugPrint('Orders schema test failed: $e');
      setState(() {
        _testResults['orders'] = false;
        _testResults['order_items'] = false;
      });
    }
  }

  Future<void> _testServicesCompatibility() async {
    try {
      // Test favorites service
      final user = _supabaseService.currentUser;
      if (user != null) {
        await _favoritesService.getUserFavoriteIds();
      }

      setState(() {
        _testResults['favorites_service'] = true;
      });
    } catch (e) {
      debugPrint('Favorites service test failed: $e');
      setState(() {
        _testResults['favorites_service'] = false;
      });
    }

    try {
      // Test chat service
      await _chatService.getUserConversations();

      setState(() {
        _testResults['chat_service'] = true;
      });
    } catch (e) {
      debugPrint('Chat service test failed: $e');
      setState(() {
        _testResults['chat_service'] = false;
      });
    }

    // Search provider test (always passes since it's read-only)
    setState(() {
      _testResults['search_provider'] = true;
    });
  }
}
