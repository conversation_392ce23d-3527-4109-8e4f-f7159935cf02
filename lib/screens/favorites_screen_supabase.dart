import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/widgets/cafeteria_card.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/services/favorites_service.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final FavoritesService _favoritesService = FavoritesService();
  List<Map<String, dynamic>> _favoriteItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    setState(() => _isLoading = true);
    try {
      final favorites = await _favoritesService.getUserFavoritesWithDetails();
      setState(() {
        _favoriteItems = favorites;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      debugPrint('Error loading favorites: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Favorites'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadFavorites,
            ),
          ],
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Cafeterias'),
              Tab(text: 'Menu Items'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // Cafeterias tab
            _buildFavoriteCafeterias(),
            // Menu Items tab
            _buildFavoriteMenuItems(),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteCafeterias() {
    return Consumer2<FavoritesProvider, SupabaseProvider>(
      builder: (context, favorites, supabaseProvider, _) {
        // Get cafeterias from Supabase
        final cafeterias = supabaseProvider.cafeterias;

        // Filter to favorites
        final favCafeterias = cafeterias
            .where((caf) => favorites.isCafeteriaFavorite(caf.id))
            .toList();

        if (favCafeterias.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite_border, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                const Text('No favorite cafeterias yet.',
                    style: TextStyle(fontSize: 18)),
                const SizedBox(height: 8),
                const Text('Add cafeterias to your favorites to see them here.',
                    style: TextStyle(fontSize: 14, color: Colors.grey)),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: favCafeterias.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: CafeteriaCard(cafeteria: favCafeterias[index]),
            );
          },
        );
      },
    );
  }

  Widget _buildFavoriteMenuItems() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_favoriteItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite_border, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            const Text('No favorite menu items yet.',
                style: TextStyle(fontSize: 18)),
            const SizedBox(height: 8),
            const Text('Add menu items to your favorites to see them here.',
                style: TextStyle(fontSize: 14, color: Colors.grey)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pushNamed(context, '/dashboard');
              },
              child: const Text('Browse Menu Items'),
            ),
          ],
        ),
      );
    }

    return Consumer<CartProvider>(
      builder: (context, cartProvider, _) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _favoriteItems.length,
          itemBuilder: (context, index) {
            final favoriteItem = _favoriteItems[index];
            final menuItem = favoriteItem['menu_items'];
            final cafeteria = menuItem['cafeterias'];

            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  menuItem['name'] ?? 'Unknown Item',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  cafeteria['name'] ?? 'Unknown Cafeteria',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Consumer<FavoritesProvider>(
                            builder: (context, favProvider, _) {
                              return IconButton(
                                icon: Icon(
                                  favProvider.isMenuItemFavorite(menuItem['id'])
                                      ? Icons.favorite
                                      : Icons.favorite_border,
                                  color: favProvider.isMenuItemFavorite(menuItem['id'])
                                      ? Colors.red
                                      : null,
                                ),
                                onPressed: () async {
                                  await favProvider.toggleMenuItemFavorite(menuItem['id']);
                                  _loadFavorites(); // Refresh the list
                                },
                              );
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (menuItem['description'] != null)
                        Text(
                          menuItem['description'],
                          style: TextStyle(color: Colors.grey[700]),
                        ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${(menuItem['price'] ?? 0).toStringAsFixed(2)} EGP',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Color(0xFFFF6B35), // Orange color
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: menuItem['is_available'] == true
                                ? () {
                                    final cartItem = CartItem(
                                      id: menuItem['id'],
                                      name: menuItem['name'] ?? 'Unknown Item',
                                      price: (menuItem['price'] ?? 0).toDouble(),
                                      image: menuItem['image_url'] ?? 'assets/images/food-placeholder.png',
                                      quantity: 1,
                                      cafeteriaName: cafeteria['name'] ?? 'Unknown Cafeteria',
                                      buildingName: cafeteria['location'] ?? 'Unknown Location',
                                      customizations: {},
                                    );
                                    cartProvider.addItem(cartItem);
                                    SnackBarUtils.showSuccessSnackBar(
                                      context: context,
                                      message: '${menuItem['name']} added to cart!',
                                    );
                                  }
                                : null,
                            icon: const Icon(Icons.add_shopping_cart, size: 18),
                            label: const Text('Add to Cart'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: menuItem['is_available'] == true
                                  ? null
                                  : Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      if (menuItem['is_available'] != true)
                        const Padding(
                          padding: EdgeInsets.only(top: 8),
                          child: Text(
                            'Currently unavailable',
                            style: TextStyle(
                              color: Colors.red,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
