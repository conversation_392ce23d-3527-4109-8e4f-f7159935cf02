import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/auth_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AccountSettingsScreen extends StatefulWidget {
  final VoidCallback onThemeChanged;

  const AccountSettingsScreen({
    super.key,
    required this.onThemeChanged,
  });

  @override
  State<AccountSettingsScreen> createState() => _AccountSettingsScreenState();
}

class _AccountSettingsScreenState extends State<AccountSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late bool _isDarkMode;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    // Get user from SimpleAuthProvider first, fall back to old AuthProvider
    final simpleAuthProvider =
        Provider.of<SimpleAuthProvider>(context, listen: false);
    final oldAuthProvider = Provider.of<AuthProvider>(context, listen: false);

    final simpleUser = simpleAuthProvider.currentUser;
    final oldUser = oldAuthProvider.currentUser;

    // Use SimpleAuthProvider data if available, otherwise fall back to old provider
    _nameController = TextEditingController(
      text: simpleUser?.fullName ?? oldUser?.name ?? '',
    );
    _emailController = TextEditingController(
      text: simpleUser?.email ?? oldUser?.email ?? '',
    );
    _phoneController = TextEditingController(
      text: simpleUser?.phone ?? oldUser?.phone ?? '',
    );

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _isDarkMode = Theme.of(context).brightness == Brightness.dark;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _toggleTheme() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', _isDarkMode);
    widget.onThemeChanged();
  }

  @override
  Widget build(BuildContext context) {
    final oldAuthProvider = Provider.of<AuthProvider>(context);
    final simpleAuthProvider = Provider.of<SimpleAuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Settings'),
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: ListView(
          padding: const EdgeInsets.all(24),
          children: [
            const Text(
              'Update your profile information, change your password, and manage your account settings here.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                prefixIcon: Icon(Icons.email),
                border: OutlineInputBorder(),
              ),
              enabled: false,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                prefixIcon: Icon(Icons.phone),
                border: OutlineInputBorder(),
                hintText: 'Egyptian number (e.g., 01XXXXXXXXX)',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return null; // Phone is optional when updating
                }
                // Egyptian phone numbers are typically 11 digits starting with 01
                final egyptianPhoneRegex = RegExp(r'^01[0-2,5]{1}[0-9]{8}$');
                if (!egyptianPhoneRegex.hasMatch(value)) {
                  return 'Please enter a valid Egyptian phone number';
                }
                return null;
              },
              autovalidateMode: AutovalidateMode.onUserInteraction,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                // Capture the context before the async gap
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                // Update profile in both providers for compatibility
                try {
                  // Update in both providers
                  await Future.wait([
                    // Update in old provider
                    oldAuthProvider.updateProfile(
                      name: _nameController.text.trim(),
                      email: _emailController.text.trim(),
                      phone: _phoneController.text.trim(),
                    ),

                    // Update in SimpleAuthProvider
                    simpleAuthProvider.updateProfile(
                      fullName: _nameController.text.trim(),
                      email: _emailController.text.trim(),
                      phone: _phoneController.text.trim(),
                    ),
                  ]);

                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(content: Text('Profile updated!')),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(content: Text('Error updating profile: $e')),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Save Changes'),
            ),
            const SizedBox(height: 24),
            Card(
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('Dark Mode'),
                    subtitle: const Text('Toggle between light and dark theme'),
                    value: _isDarkMode,
                    onChanged: (value) => _toggleTheme(),
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.lock),
                    title: const Text('Change Password'),
                    subtitle: const Text('Update your account password.'),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('Change Password'),
                          content: const Text(
                              'This feature will be available in a future update.'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('OK'),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text('Delete Account',
                        style: TextStyle(color: Colors.red)),
                    subtitle: const Text('Permanently remove your account.'),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('Delete Account'),
                          content: const Text(
                            'Are you sure you want to delete your account? This action cannot be undone.',
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('CANCEL'),
                            ),
                            TextButton(
                              onPressed: () {
                                // Close the dialog
                                Navigator.of(context).pop();

                                // Show confirmation
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                        'This feature will be available in a future update.'),
                                  ),
                                );
                              },
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.red,
                              ),
                              child: const Text('DELETE'),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
