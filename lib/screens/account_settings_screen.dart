import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/auth_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';

class AccountSettingsScreen extends StatefulWidget {
  final VoidCallback onThemeChanged;

  const AccountSettingsScreen({
    super.key,
    required this.onThemeChanged,
  });

  @override
  State<AccountSettingsScreen> createState() => _AccountSettingsScreenState();
}

class _AccountSettingsScreenState extends State<AccountSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late bool _isDarkMode;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    // Get user from SimpleAuthProvider first, fall back to old AuthProvider
    final simpleAuthProvider =
        Provider.of<SimpleAuthProvider>(context, listen: false);
    final oldAuthProvider = Provider.of<AuthProvider>(context, listen: false);

    final simpleUser = simpleAuthProvider.currentUser;
    final oldUser = oldAuthProvider.currentUser;

    // Use SimpleAuthProvider data if available, otherwise fall back to old provider
    _nameController = TextEditingController(
      text: simpleUser?.fullName ?? oldUser?.name ?? '',
    );
    _emailController = TextEditingController(
      text: simpleUser?.email ?? oldUser?.email ?? '',
    );
    _phoneController = TextEditingController(
      text: simpleUser?.phone ?? oldUser?.phone ?? '',
    );

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _isDarkMode = Theme.of(context).brightness == Brightness.dark;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _toggleTheme() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', _isDarkMode);
    widget.onThemeChanged();
  }

  @override
  Widget build(BuildContext context) {
    final oldAuthProvider = Provider.of<AuthProvider>(context);
    final simpleAuthProvider = Provider.of<SimpleAuthProvider>(context);

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildModernAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(ModernTheme.spaceM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeaderSection(),
              const SizedBox(height: ModernTheme.spaceXL),
              _buildProfileSection(),
              const SizedBox(height: ModernTheme.spaceXL),
              _buildPreferencesSection(),
              const SizedBox(height: ModernTheme.spaceXL),
              _buildSecuritySection(),
              const SizedBox(height: ModernTheme.spaceXL),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: ModernTheme.primaryGradient,
              borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
            ),
            child: const Icon(
              Icons.settings,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: ModernTheme.spaceM),
          Text(
            'Account Settings',
            style: ModernTheme.headingMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(ModernTheme.spaceL),
      decoration: BoxDecoration(
        gradient: ModernTheme.primaryGradient,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
            ),
            child: const Icon(
              Icons.person,
              color: Colors.white,
              size: 32,
            ),
          ),
          const SizedBox(width: ModernTheme.spaceM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Manage Your Account',
                  style: ModernTheme.headingMedium.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Update your profile information and preferences',
                  style: ModernTheme.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ModernTheme.accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                ),
                child: Icon(
                  Icons.person_outline,
                  color: ModernTheme.accentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: ModernTheme.spaceM),
              Text(
                'Profile Information',
                style: ModernTheme.headingSmall,
              ),
            ],
          ),
          const SizedBox(height: ModernTheme.spaceL),
          _buildModernTextField(
            controller: _nameController,
            label: 'Full Name',
            icon: Icons.person,
          ),
          const SizedBox(height: ModernTheme.spaceM),
          _buildModernTextField(
            controller: _emailController,
            label: 'Email Address',
            icon: Icons.email,
            enabled: false,
          ),
          const SizedBox(height: ModernTheme.spaceM),
          _buildModernTextField(
            controller: _phoneController,
            label: 'Phone Number',
            icon: Icons.phone,
            hint: 'Egyptian number (e.g., 01XXXXXXXXX)',
          ),
          const SizedBox(height: ModernTheme.spaceL),
          SizedBox(
            width: double.infinity,
            child: ModernButton(
              text: 'Save Changes',
              onPressed: _saveProfile,
              icon: Icons.save,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? hint,
    bool enabled = true,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: enabled ? Colors.white : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        enabled: enabled,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(
            icon,
            color: enabled ? ModernTheme.accentColor : Colors.grey,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
          labelStyle: TextStyle(
            color: enabled ? Colors.grey.shade700 : Colors.grey.shade500,
          ),
        ),
        validator: controller == _phoneController ? _validatePhone : null,
        autovalidateMode: AutovalidateMode.onUserInteraction,
      ),
    );
  }

  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional
    }
    final egyptianPhoneRegex = RegExp(r'^01[0-2,5]{1}[0-9]{8}$');
    if (!egyptianPhoneRegex.hasMatch(value)) {
      return 'Please enter a valid Egyptian phone number';
    }
    return null;
  }

  Widget _buildPreferencesSection() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ModernTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                ),
                child: Icon(
                  Icons.palette_outlined,
                  color: ModernTheme.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: ModernTheme.spaceM),
              Text(
                'Preferences',
                style: ModernTheme.headingSmall,
              ),
            ],
          ),
          const SizedBox(height: ModernTheme.spaceL),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
            ),
            child: SwitchListTile(
              title: Text(
                'Dark Mode',
                style: ModernTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                'Toggle between light and dark theme',
                style: ModernTheme.bodyMedium.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              value: _isDarkMode,
              onChanged: (value) => _toggleTheme(),
              activeColor: ModernTheme.accentColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySection() {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                ),
                child: const Icon(
                  Icons.security,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              const SizedBox(width: ModernTheme.spaceM),
              Text(
                'Security & Account',
                style: ModernTheme.headingSmall,
              ),
            ],
          ),
          const SizedBox(height: ModernTheme.spaceL),
          _buildSecurityOption(
            icon: Icons.lock_outline,
            title: 'Change Password',
            subtitle: 'Update your account password',
            onTap: _showChangePasswordDialog,
          ),
          const SizedBox(height: ModernTheme.spaceM),
          _buildSecurityOption(
            icon: Icons.delete_outline,
            title: 'Delete Account',
            subtitle: 'Permanently remove your account',
            onTap: _showDeleteAccountDialog,
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDestructive ? Colors.red.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : Colors.grey.shade700,
        ),
        title: Text(
          title,
          style: ModernTheme.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: isDestructive ? Colors.red : null,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: ModernTheme.bodyMedium.copyWith(
            color: isDestructive ? Colors.red.shade700 : Colors.grey.shade600,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: isDestructive ? Colors.red : Colors.grey.shade400,
        ),
        onTap: onTap,
      ),
    );
  }

  Future<void> _saveProfile() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final oldAuthProvider = Provider.of<AuthProvider>(context, listen: false);
    final simpleAuthProvider = Provider.of<SimpleAuthProvider>(context, listen: false);

    try {
      await Future.wait([
        oldAuthProvider.updateProfile(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          phone: _phoneController.text.trim(),
        ),
        simpleAuthProvider.updateProfile(
          fullName: _nameController.text.trim(),
          email: _emailController.text.trim(),
          phone: _phoneController.text.trim(),
        ),
      ]);

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('Profile updated successfully!'),
              ],
            ),
            backgroundColor: ModernTheme.accentColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('Error updating profile: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ModernTheme.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: Icon(
                Icons.lock,
                color: ModernTheme.accentColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text('Change Password'),
          ],
        ),
        content: const Text(
          'This feature will be available in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.warning,
                color: Colors.red,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text('Delete Account'),
          ],
        ),
        content: const Text(
          'Are you sure you want to delete your account? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'This feature will be available in a future update.',
                  ),
                ),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );
  }

}
