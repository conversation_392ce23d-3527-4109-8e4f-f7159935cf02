import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/supabase_service.dart';
import 'package:unieatsappv0/config/env_config.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  String _status = 'Initializing...';
  bool _isLoading = false;
  final SupabaseService _supabaseService = SupabaseService();

  @override
  void initState() {
    super.initState();
    _checkSupabaseConfig();
  }

  Future<void> _checkSupabaseConfig() async {
    setState(() {
      _status = 'Checking Supabase configuration...';
    });

    try {
      // Check Supabase URL and anon key
      const url = EnvConfig.supabaseUrl;
      const anonKey = EnvConfig.supabaseAnonKey;

      setState(() {
        _status = '''
Supabase Configuration:
URL: $url
Anon Key: ${anonKey.substring(0, 10)}...${anonKey.substring(anonKey.length - 5)}
''';
      });
    } catch (e) {
      setState(() {
        _status = 'Error checking Supabase configuration: $e';
      });
    }
  }

  Future<void> _testSupabaseConnection() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing Supabase connection...';
    });

    try {
      await _supabaseService.init();
      setState(() {
        _status = 'Supabase connection successful!';
      });
    } catch (e) {
      setState(() {
        _status = 'Supabase connection error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Screen'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Supabase Debug Information',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(5),
              ),
              width: double.infinity,
              child: Text(
                _status,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isLoading ? null : _testSupabaseConnection,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('Test Supabase Connection'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacementNamed(context, '/dashboard');
              },
              child: const Text('Go to Dashboard'),
            ),
            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 20),
            const Text(
              'Troubleshooting Steps:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const Text('1. Verify Supabase URL and anon key'),
            const Text('2. Test Supabase connection'),
            const Text('3. Check console for error messages'),
            const Text('4. Try restarting the app'),
            const SizedBox(height: 20),
            const Text(
              'If issues persist, try:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const Text('- Clearing app data and cache'),
            const Text('- Reinstalling the app'),
            const Text('- Checking Supabase dashboard for service status'),
          ],
        ),
      ),
    );
  }
}
