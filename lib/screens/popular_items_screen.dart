import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/services/supabase_menu_service.dart';
import 'package:unieatsappv0/models/supabase_models.dart';

class PopularItemsScreen extends StatefulWidget {
  const PopularItemsScreen({super.key});

  @override
  State<PopularItemsScreen> createState() => _PopularItemsScreenState();
}

class _PopularItemsScreenState extends State<PopularItemsScreen> {
  List<SupabaseMenuItem> _menuItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMenuItems();
  }

  Future<void> _loadMenuItems() async {
    final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
    final menuService = SupabaseMenuService();

    // Load all menu items from all cafeterias
    final allMenuItems = <SupabaseMenuItem>[];
    for (final cafeteria in supabaseProvider.cafeterias) {
      final menuItems = await menuService.getMenuItemsByCafeteria(cafeteria.id);
      allMenuItems.addAll(menuItems);
    }

    // Sort by price (highest first) as a proxy for popularity since we don't have ratings
    allMenuItems.sort((a, b) => b.price.compareTo(a.price));

    if (mounted) {
      setState(() {
        _menuItems = allMenuItems;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Popular Items'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _menuItems.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.restaurant_menu, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No menu items available'),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _menuItems.length,
                  itemBuilder: (context, index) {
                    return _buildPopularItemCard(context, _menuItems[index]);
                  },
                ),
    );
  }

  Widget _buildPopularItemCard(BuildContext context, SupabaseMenuItem item) {
    final theme = Theme.of(context);
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final favoritesProvider = Provider.of<FavoritesProvider>(context);

    return InkWell(
      onTap: () {
        Navigator.of(context).pushNamed(
          '/item_details',
          arguments: item,
        );
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: item.imageUrl != null
                    ? Image.network(
                        item.imageUrl!,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 80,
                            height: 80,
                            color: theme.colorScheme.surface,
                            child: Icon(Icons.fastfood,
                                color: theme.textTheme.bodySmall?.color),
                          );
                        },
                      )
                    : Container(
                        width: 80,
                        height: 80,
                        color: theme.colorScheme.surface,
                        child: Icon(Icons.fastfood,
                            color: theme.textTheme.bodySmall?.color),
                      ),
              ),
              const SizedBox(width: 12),
              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.description ?? 'No description available',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodySmall?.color,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${item.price.toStringAsFixed(2)} EGP',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.primaryColor,
                          ),
                        ),
                        Row(
                          children: [
                            const Icon(
                              Icons.local_fire_department,
                              size: 16,
                              color: Colors.orange,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Popular',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Action buttons
              Column(
                children: [
                  IconButton(
                    icon: Icon(
                      favoritesProvider.isMenuItemFavorite(item.id)
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: favoritesProvider.isMenuItemFavorite(item.id)
                          ? Colors.red
                          : null,
                    ),
                    onPressed: () {
                      favoritesProvider.toggleMenuItemFavorite(item.id);
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.add_shopping_cart),
                    onPressed: () {
                      // Find the cafeteria for this menu item
                      final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
                      final cafeteria = supabaseProvider.cafeterias.firstWhere(
                        (caf) => caf.id == item.cafeteriaId,
                        orElse: () => supabaseProvider.cafeterias.isNotEmpty
                            ? supabaseProvider.cafeterias.first
                            : SupabaseCafeteria(id: 'unknown', name: 'Unknown Cafeteria'),
                      );

                      final cartItem = CartItem(
                        id: item.id,
                        name: item.name,
                        price: item.price,
                        image: item.imageUrl ?? '',
                        cafeteriaName: cafeteria.name,
                        buildingName: cafeteria.location ?? '',
                        quantity: 1,
                        customizations: {},
                        notes: null,
                        menuItem: item.toMenuItem(), // Convert to MenuItem
                      );
                      cartProvider.addItem(cartItem);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('${item.name} added to cart')),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
