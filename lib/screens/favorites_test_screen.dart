import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/services/favorites_service.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';

class FavoritesTestScreen extends StatefulWidget {
  const FavoritesTestScreen({super.key});

  @override
  State<FavoritesTestScreen> createState() => _FavoritesTestScreenState();
}

class _FavoritesTestScreenState extends State<FavoritesTestScreen> {
  final FavoritesService _favoritesService = FavoritesService();
  List<String> _favoriteIds = [];
  bool _isLoading = false;
  String? _testMenuItemId;

  @override
  void initState() {
    super.initState();
    _loadTestData();
  }

  Future<void> _loadTestData() async {
    setState(() => _isLoading = true);
    try {
      // Get a test menu item ID from Supabase
      final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
      if (supabaseProvider.menuItems.isNotEmpty) {
        _testMenuItemId = supabaseProvider.menuItems.first.id;
      }

      // Load current favorites
      await _loadFavorites();
    } catch (e) {
      debugPrint('Error loading test data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadFavorites() async {
    try {
      final favorites = await _favoritesService.getUserFavoriteIds();
      setState(() {
        _favoriteIds = favorites;
      });
    } catch (e) {
      debugPrint('Error loading favorites: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Favorites Test'),
        backgroundColor: Colors.pink,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFavorites,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Favorites Functionality Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'Test the favorites functionality to ensure it works with Supabase.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),

            // Authentication status
            Consumer<SupabaseProvider>(
              builder: (context, supabaseProvider, _) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Authentication Status',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              supabaseProvider.isAuthenticated ? Icons.check_circle : Icons.error,
                              color: supabaseProvider.isAuthenticated ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              supabaseProvider.isAuthenticated
                                  ? 'Authenticated'
                                  : 'Not authenticated',
                            ),
                          ],
                        ),
                        if (supabaseProvider.isAuthenticated)
                          Text(
                            'User: ${supabaseProvider.currentUser?.email ?? 'Unknown'}',
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // Test menu item
            if (_testMenuItemId != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Test Menu Item',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text('ID: $_testMenuItemId'),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _isLoading ? null : () => _testAddToFavorites(),
                              icon: const Icon(Icons.favorite),
                              label: const Text('Add to Favorites'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.pink,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _isLoading ? null : () => _testRemoveFromFavorites(),
                              icon: const Icon(Icons.favorite_border),
                              label: const Text('Remove'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Current favorites
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Current Favorites',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Total: ${_favoriteIds.length}'),
                    const SizedBox(height: 8),
                    if (_favoriteIds.isEmpty)
                      const Text('No favorites yet')
                    else
                      ...(_favoriteIds.take(5).map((id) => Text('• $id'))),
                    if (_favoriteIds.length > 5)
                      Text('... and ${_favoriteIds.length - 5} more'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Provider test
            Consumer<FavoritesProvider>(
              builder: (context, favProvider, _) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'FavoritesProvider Status',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text('Loading: ${favProvider.isLoading}'),
                        Text('Error: ${favProvider.error ?? 'None'}'),
                        Text('Favorites count: ${favProvider.favoriteMenuItemIds.length}'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () async {
                            await favProvider.initialize();
                            _loadFavorites();
                          },
                          child: const Text('Reinitialize Provider'),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testAddToFavorites() async {
    if (_testMenuItemId == null) return;

    setState(() => _isLoading = true);
    try {
      final success = await _favoritesService.addToFavorites(_testMenuItemId!);
      if (mounted) {
        if (success) {
          SnackBarUtils.showSuccessSnackBar(
            context: context,
            message: 'Added to favorites!',
          );
          await _loadFavorites();

          // Also update the provider
          if (mounted) {
            final favProvider = Provider.of<FavoritesProvider>(context, listen: false);
            await favProvider.loadUserFavorites();
          }
        } else {
          SnackBarUtils.showErrorSnackBar(
            context: context,
            message: 'Failed to add to favorites',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showErrorSnackBar(
          context: context,
          message: 'Error: $e',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testRemoveFromFavorites() async {
    if (_testMenuItemId == null) return;

    setState(() => _isLoading = true);
    try {
      final success = await _favoritesService.removeFromFavorites(_testMenuItemId!);
      if (mounted) {
        if (success) {
          SnackBarUtils.showSuccessSnackBar(
            context: context,
            message: 'Removed from favorites!',
          );
          await _loadFavorites();

          // Also update the provider
          if (mounted) {
            final favProvider = Provider.of<FavoritesProvider>(context, listen: false);
            await favProvider.loadUserFavorites();
          }
        } else {
          SnackBarUtils.showErrorSnackBar(
            context: context,
            message: 'Failed to remove from favorites',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showErrorSnackBar(
          context: context,
          message: 'Error: $e',
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
