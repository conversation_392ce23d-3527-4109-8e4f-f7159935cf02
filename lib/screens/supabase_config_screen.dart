import 'package:flutter/material.dart';
import 'package:unieatsappv0/config/env_config.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class SupabaseConfigScreen extends StatefulWidget {
  const SupabaseConfigScreen({super.key});

  @override
  State<SupabaseConfigScreen> createState() => _SupabaseConfigScreenState();
}

class _SupabaseConfigScreenState extends State<SupabaseConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  final _urlController = TextEditingController(text: EnvConfig.supabaseUrl);
  final _anonKeyController =
      TextEditingController(text: EnvConfig.supabaseAnonKey);

  bool _isLoading = false;
  String? _error;
  String? _success;

  @override
  void dispose() {
    _urlController.dispose();
    _anonKeyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase Configuration'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Supabase Settings',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Configure your Supabase project settings. These settings are used to connect to your Supabase backend.',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 30),
              TextFormField(
                controller: _urlController,
                decoration: const InputDecoration(
                  labelText: 'Supabase URL',
                  hintText: 'https://your-project-id.supabase.co',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter the Supabase URL';
                  }
                  if (!value.startsWith('https://') ||
                      !value.contains('supabase.co')) {
                    return 'Please enter a valid Supabase URL';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              TextFormField(
                controller: _anonKeyController,
                decoration: const InputDecoration(
                  labelText: 'Supabase Anon Key',
                  hintText: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter the Supabase Anon Key';
                  }
                  if (!value.startsWith('eyJ')) {
                    return 'Please enter a valid Supabase Anon Key';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 30),
              if (_error != null)
                Container(
                  padding: const EdgeInsets.all(10),
                  color: Colors.red.shade100,
                  width: double.infinity,
                  child: Text(
                    _error!,
                    style: TextStyle(color: Colors.red.shade900),
                  ),
                ),
              if (_success != null)
                Container(
                  padding: const EdgeInsets.all(10),
                  color: Colors.green.shade100,
                  width: double.infinity,
                  child: Text(
                    _success!,
                    style: TextStyle(color: Colors.green.shade900),
                  ),
                ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveConfiguration,
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Save Configuration'),
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _testConnection,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                  ),
                  child: const Text('Test Connection'),
                ),
              ),
              const SizedBox(height: 30),
              const Divider(),
              const SizedBox(height: 20),
              const Text(
                'Current Configuration',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              _buildInfoRow('Project URL', EnvConfig.supabaseUrl),
              _buildInfoRow(
                  'Project ID', _extractProjectId(EnvConfig.supabaseUrl)),
              _buildInfoRow(
                  'Anon Key', _maskAnonKey(EnvConfig.supabaseAnonKey)),
              const SizedBox(height: 20),
              const Text(
                'Note: Changes to these settings require app restart to take effect.',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _extractProjectId(String url) {
    final uri = Uri.parse(url);
    final host = uri.host;
    final parts = host.split('.');
    return parts.isNotEmpty ? parts[0] : 'unknown';
  }

  String _maskAnonKey(String key) {
    if (key.length <= 10) return '****';
    return '${key.substring(0, 10)}****${key.substring(key.length - 4)}';
  }

  Future<void> _saveConfiguration() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _success = null;
    });

    try {
      // In a real app, you would save these values to secure storage
      // and update the EnvConfig class

      // For now, just simulate success
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _success = 'Configuration saved successfully!';
      });
    } catch (e) {
      setState(() {
        _error = 'Error saving configuration: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testConnection() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _success = null;
    });

    try {
      final supabaseService = SupabaseService();

      // Test the connection
      await supabaseService.init();

      setState(() {
        _success = 'Connection successful! Supabase is properly configured.';
      });
    } catch (e) {
      setState(() {
        _error = 'Connection failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
