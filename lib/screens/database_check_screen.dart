import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';

class DatabaseCheckScreen extends StatefulWidget {
  const DatabaseCheckScreen({super.key});

  @override
  State<DatabaseCheckScreen> createState() => _DatabaseCheckScreenState();
}

class _DatabaseCheckScreenState extends State<DatabaseCheckScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  final List<String> _results = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkDatabase();
  }

  Future<void> _checkDatabase() async {
    setState(() {
      _isLoading = true;
      _results.clear();
    });

    try {
      _results.add('🔍 Checking Supabase Database Structure...\n');

      // List of tables to check
      final tablesToCheck = [
        'profiles',
        'cafeterias',
        'menu_items',
        'orders',
        'order_items',
        'favorites',
        'ratings',
        'notifications',
        'chat_conversations',
        'chat_messages',
        'support_tickets',
      ];

      _results.add('📋 Checking ${tablesToCheck.length} tables:\n');

      for (final table in tablesToCheck) {
        await _checkTable(table);
      }

      _results.add('\n✅ Database check completed!');
    } catch (e) {
      _results.add('\n❌ Error during database check: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _checkTable(String tableName) async {
    try {
      // Try to query the table
      final response = await _supabaseService.client
          .from(tableName)
          .select('*')
          .limit(1);

      _results.add('✅ $tableName: EXISTS (${response.length} sample records)');

      // For chat tables, let's get more details
      if (tableName == 'chat_conversations' || 
          tableName == 'chat_messages' || 
          tableName == 'support_tickets') {
        final count = await _getTableCount(tableName);
        _results.add('   📊 Total records: $count');
      }
    } catch (e) {
      if (e.toString().contains('relation') && e.toString().contains('does not exist')) {
        _results.add('❌ $tableName: MISSING');
      } else {
        _results.add('⚠️ $tableName: ERROR - ${e.toString().substring(0, 50)}...');
      }
    }
  }

  Future<int> _getTableCount(String tableName) async {
    try {
      final response = await _supabaseService.client
          .from(tableName)
          .select('*');
      return response.length;
    } catch (e) {
      return 0;
    }
  }

  Future<void> _checkTableStructure(String tableName) async {
    try {
      _results.add('\n🔍 Checking structure of $tableName:');
      
      // Try to get a sample record to see the structure
      final response = await _supabaseService.client
          .from(tableName)
          .select('*')
          .limit(1);

      if (response.isNotEmpty) {
        final sample = response.first;
        _results.add('   Columns: ${sample.keys.join(', ')}');
      } else {
        _results.add('   No data available to check structure');
      }
    } catch (e) {
      _results.add('   Error checking structure: $e');
    }
  }

  Future<void> _testChatFunctionality() async {
    setState(() {
      _results.clear();
      _results.add('🧪 Testing Chat Functionality...\n');
    });

    try {
      // Test 1: Check if we can query chat_conversations
      _results.add('1. Testing chat_conversations table...');
      final conversations = await _supabaseService.client
          .from('chat_conversations')
          .select('*')
          .limit(5);
      _results.add('   ✅ Found ${conversations.length} conversations');

      // Test 2: Check if we can query chat_messages
      _results.add('\n2. Testing chat_messages table...');
      final messages = await _supabaseService.client
          .from('chat_messages')
          .select('*')
          .limit(5);
      _results.add('   ✅ Found ${messages.length} messages');

      // Test 3: Check if we can query support_tickets
      _results.add('\n3. Testing support_tickets table...');
      final tickets = await _supabaseService.client
          .from('support_tickets')
          .select('*')
          .limit(5);
      _results.add('   ✅ Found ${tickets.length} tickets');

      // Test 4: Check favorites table structure
      _results.add('\n4. Testing favorites table...');
      final favorites = await _supabaseService.client
          .from('favorites')
          .select('*')
          .limit(5);
      _results.add('   ✅ Found ${favorites.length} favorites');

      if (favorites.isNotEmpty) {
        final sample = favorites.first;
        _results.add('   📋 Favorites columns: ${sample.keys.join(', ')}');
      }

      _results.add('\n✅ All chat functionality tests passed!');
    } catch (e) {
      _results.add('\n❌ Chat functionality test failed: $e');
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Check'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkDatabase,
          ),
          IconButton(
            icon: const Icon(Icons.chat),
            onPressed: _testChatFunctionality,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            if (_isLoading)
              const LinearProgressIndicator()
            else
              Row(
                children: [
                  ElevatedButton(
                    onPressed: _checkDatabase,
                    child: const Text('Check All Tables'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: _testChatFunctionality,
                    child: const Text('Test Chat Tables'),
                  ),
                ],
              ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _results.join('\n'),
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
