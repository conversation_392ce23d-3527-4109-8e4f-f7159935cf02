// lib/screens/profile_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/auth_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/widgets/profile_picture_picker.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';
import 'dart:math';

// Add a simple theme notifier for demonstration
class ThemeNotifier extends InheritedWidget {
  final ValueNotifier<ThemeMode> themeModeNotifier;
  const ThemeNotifier(
      {required this.themeModeNotifier, required super.child, super.key});
  static ThemeNotifier? of(BuildContext context) =>
      context.dependOnInheritedWidgetOfExactType<ThemeNotifier>();
  @override
  bool updateShouldNotify(ThemeNotifier oldWidget) =>
      themeModeNotifier != oldWidget.themeModeNotifier;
}

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  // No need for UserService anymore, we'll use SimpleAuthProvider
  Map<String, dynamic> _profile = {};

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _loadProfile();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  Future<void> _loadProfile() async {
    // Get profile from SimpleAuthProvider
    final authProvider =
        Provider.of<SimpleAuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user != null) {
      debugPrint(
          'Profile Screen: Loading profile for user: ${user.fullName}, email: ${user.email}');
      setState(() {
        _profile = {
          'name': user.fullName,
          'email': user.email,
          'phone': user.phone,
          'role': user.roleId,
          'notification_enabled': user.notificationEnabled,
        };
      });
    } else {
      debugPrint('Profile Screen: No user found in SimpleAuthProvider');
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final oldAuthProvider = Provider.of<AuthProvider>(context);
    final simpleAuthProvider = Provider.of<SimpleAuthProvider>(context);

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildModernAppBar(),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(ModernTheme.spaceM),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                children: [
                  // Compact Profile Header
                  _buildCompactProfileHeader(simpleAuthProvider, oldAuthProvider),
                  const SizedBox(height: ModernTheme.spaceL),
                  // Quick Stats
                  _buildQuickStats(),
                  const SizedBox(height: ModernTheme.spaceL),
                  // Menu Section - Expanded to fill remaining space
                  Expanded(
                    child: _buildCompactMenuSection(simpleAuthProvider, oldAuthProvider),
                  ),
                  const SizedBox(height: ModernTheme.spaceM),
                  // Logout Section
                  _buildLogoutSection(simpleAuthProvider, oldAuthProvider),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: ModernTheme.primaryGradient,
              borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
            ),
            child: const Icon(
              Icons.person,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: ModernTheme.spaceM),
          Text(
            'My Profile',
            style: ModernTheme.headingMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildModernSliverAppBar(SimpleAuthProvider simpleAuthProvider, AuthProvider oldAuthProvider) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: ModernTheme.primaryGradient,
          ),
          child: Stack(
            children: [
              _buildFloatingElements(),
              Positioned(
                bottom: 40,
                left: 20,
                right: 20,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: ModernTheme.spaceM),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'My Profile',
                              style: ModernTheme.headingMedium.copyWith(
                                color: Colors.white,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              'Manage your account',
                              style: ModernTheme.bodyMedium.copyWith(
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingElements() {
    return Stack(
      children: [
        Positioned(
          top: 60,
          right: 30,
          child: _buildFloatingCircle(25, Colors.white.withOpacity(0.1)),
        ),
        Positioned(
          top: 120,
          left: 40,
          child: _buildFloatingCircle(15, ModernTheme.secondaryColor.withOpacity(0.2)),
        ),
        Positioned(
          bottom: 100,
          right: 60,
          child: _buildFloatingCircle(20, Colors.white.withOpacity(0.15)),
        ),
      ],
    );
  }

  Widget _buildFloatingCircle(double size, Color color) {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            sin(_fadeController.value * 2 * pi) * 5,
            cos(_fadeController.value * 2 * pi) * 5,
          ),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileHeader(SimpleAuthProvider simpleAuthProvider, AuthProvider oldAuthProvider) {
    final userName = _profile['name']?.toString() ??
        (simpleAuthProvider.currentUser?.fullName ??
            oldAuthProvider.currentUser?.name ??
            'Guest');
    final userEmail = _profile['email']?.toString() ??
        (simpleAuthProvider.currentUser?.email ??
            oldAuthProvider.currentUser?.email ??
            '');
    final userPhone = _profile['phone']?.toString() ??
        (simpleAuthProvider.currentUser?.phone ?? '');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: ModernTheme.spaceM),
      padding: const EdgeInsets.all(ModernTheme.spaceL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusXLarge),
        boxShadow: ModernTheme.largeShadow,
      ),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: ModernTheme.primaryGradient.scale(0.3),
                  boxShadow: [
                    BoxShadow(
                      color: ModernTheme.primaryColor.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: ProfilePicturePicker(
                  size: 120,
                  onImageSelected: (path) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.white),
                            const SizedBox(width: 8),
                            const Text('Profile picture updated'),
                          ],
                        ),
                        backgroundColor: ModernTheme.accentColor,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        margin: const EdgeInsets.all(16),
                      ),
                    );
                  },
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: ModernTheme.accentGradient,
                    shape: BoxShape.circle,
                    boxShadow: ModernTheme.softShadow,
                  ),
                  child: const Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: ModernTheme.spaceL),
          Text(
            userName,
            textAlign: TextAlign.center,
            style: ModernTheme.headingMedium,
          ),
          const SizedBox(height: ModernTheme.spaceS),
          Text(
            userEmail,
            textAlign: TextAlign.center,
            style: ModernTheme.bodyMedium.copyWith(
              color: ModernTheme.textSecondary,
            ),
          ),
          if (userPhone.isNotEmpty) ...[
            const SizedBox(height: ModernTheme.spaceS),
            Text(
              userPhone,
              textAlign: TextAlign.center,
              style: ModernTheme.bodySmall.copyWith(
                color: ModernTheme.textTertiary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: ModernTheme.spaceM),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              icon: Icons.shopping_bag,
              title: 'Orders',
              value: '12',
              gradient: ModernTheme.primaryGradient,
            ),
          ),
          const SizedBox(width: ModernTheme.spaceM),
          Expanded(
            child: _buildStatCard(
              icon: Icons.favorite,
              title: 'Favorites',
              value: '8',
              gradient: ModernTheme.accentGradient,
            ),
          ),
          const SizedBox(width: ModernTheme.spaceM),
          Expanded(
            child: _buildStatCard(
              icon: Icons.star,
              title: 'Reviews',
              value: '5',
              gradient: ModernTheme.secondaryGradient,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required LinearGradient gradient,
  }) {
    return Container(
      padding: const EdgeInsets.all(ModernTheme.spaceM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: ModernTheme.spaceS),
          Text(
            value,
            style: ModernTheme.headingSmall.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          Text(
            title,
            style: ModernTheme.bodySmall.copyWith(
              color: ModernTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernMenuSection(SimpleAuthProvider simpleAuthProvider, AuthProvider oldAuthProvider) {
    final menuItems = [
      {
        'icon': Icons.person_outline,
        'title': 'Account Settings',
        'subtitle': 'Manage your personal information',
        'gradient': ModernTheme.primaryGradient,
        'onTap': () async {
          await Navigator.pushNamed(context, '/profile/account-settings');
          _loadProfile();
        },
      },
      {
        'icon': Icons.history,
        'title': 'Order History',
        'subtitle': 'View your past orders',
        'gradient': ModernTheme.accentGradient,
        'onTap': () => Navigator.pushNamed(context, '/orders'),
      },
      {
        'icon': Icons.favorite_outline,
        'title': 'Favorites',
        'subtitle': 'Your favorite items and cafeterias',
        'gradient': LinearGradient(
          colors: [Colors.pink.shade400, Colors.pink.shade600],
        ),
        'onTap': () => Navigator.pushNamed(context, '/favorites'),
      },
      {
        'icon': Icons.help_outline,
        'title': 'Help & Support',
        'subtitle': 'Get help and contact support',
        'gradient': ModernTheme.secondaryGradient,
        'onTap': () => Navigator.pushNamed(context, '/profile/help'),
      },
      {
        'icon': Icons.chat_bubble_outline,
        'title': 'Talk to Us',
        'subtitle': 'Chat with our support team',
        'gradient': LinearGradient(
          colors: [Colors.blue.shade400, Colors.blue.shade600],
        ),
        'onTap': () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const MiniChatScreen(),
            ),
          );
        },
      },
      {
        'icon': Icons.info_outline,
        'title': 'About Us',
        'subtitle': 'Learn more about UniEats',
        'gradient': LinearGradient(
          colors: [Colors.purple.shade400, Colors.purple.shade600],
        ),
        'onTap': () => Navigator.pushNamed(context, '/profile/about-us'),
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: ModernTheme.spaceM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: ModernTheme.spaceM),
            child: Text(
              'Menu',
              style: ModernTheme.headingMedium,
            ),
          ),
          const SizedBox(height: ModernTheme.spaceL),
          ...menuItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;

            return AnimatedBuilder(
              animation: _slideController,
              builder: (context, child) {
                final delay = index * 0.1;
                final animationValue = Curves.easeOut.transform(
                  (_slideController.value - delay).clamp(0.0, 1.0),
                );

                return Transform.translate(
                  offset: Offset(0, (1 - animationValue) * 30),
                  child: Opacity(
                    opacity: animationValue,
                    child: Container(
                      margin: const EdgeInsets.only(bottom: ModernTheme.spaceM),
                      child: _buildModernMenuItem(
                        icon: item['icon'] as IconData,
                        title: item['title'] as String,
                        subtitle: item['subtitle'] as String,
                        gradient: item['gradient'] as LinearGradient,
                        onTap: item['onTap'] as VoidCallback,
                      ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildModernMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(ModernTheme.spaceM),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: ModernTheme.spaceM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: ModernTheme.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: ModernTheme.bodySmall.copyWith(
                          color: ModernTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: ModernTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                  ),
                  child: Icon(
                    Icons.chevron_right,
                    color: ModernTheme.textSecondary,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutSection(SimpleAuthProvider simpleAuthProvider, AuthProvider oldAuthProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: ModernTheme.spaceM),
      child: ModernButton(
        text: 'Logout',
        icon: Icons.logout,
        onPressed: () {
          _showLogoutDialog(simpleAuthProvider, oldAuthProvider);
        },
        width: double.infinity,
      ),
    );
  }

  void _showLogoutDialog(SimpleAuthProvider simpleAuthProvider, AuthProvider oldAuthProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.red.shade400, Colors.red.shade600],
                ),
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.logout,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: ModernTheme.spaceM),
            Text(
              'Logout',
              style: ModernTheme.headingSmall,
            ),
          ],
        ),
        content: Text(
          'Are you sure you want to logout? You will need to sign in again to access your account.',
          style: ModernTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: ModernTheme.labelLarge.copyWith(
                color: ModernTheme.textSecondary,
              ),
            ),
          ),
          ModernButton(
            text: 'Logout',
            onPressed: () {
              simpleAuthProvider.logout();
              oldAuthProvider.logout();
              Navigator.of(context).pop();
              Navigator.pushReplacementNamed(context, '/login');
            },
            isSecondary: false,
          ),
        ],
      ),
    );
  }
  Widget _buildCompactProfileHeader(SimpleAuthProvider simpleAuthProvider, AuthProvider oldAuthProvider) {
    final userName = _profile['name']?.toString() ??
        (simpleAuthProvider.currentUser?.fullName ??
            oldAuthProvider.currentUser?.name ??
            'Guest');
    final userEmail = _profile['email']?.toString() ??
        (simpleAuthProvider.currentUser?.email ??
            oldAuthProvider.currentUser?.email ??
            '');

    return Container(
      padding: const EdgeInsets.all(ModernTheme.spaceM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: ModernTheme.primaryGradient.scale(0.3),
              boxShadow: [
                BoxShadow(
                  color: ModernTheme.primaryColor.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ProfilePicturePicker(
              size: 60,
              onImageSelected: (path) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.white),
                        const SizedBox(width: 8),
                        const Text('Profile picture updated'),
                      ],
                    ),
                    backgroundColor: ModernTheme.accentColor,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    margin: const EdgeInsets.all(16),
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: ModernTheme.spaceM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  userName,
                  style: ModernTheme.headingSmall,
                ),
                const SizedBox(height: 4),
                Text(
                  userEmail,
                  style: ModernTheme.bodyMedium.copyWith(
                    color: ModernTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactMenuSection(SimpleAuthProvider simpleAuthProvider, AuthProvider oldAuthProvider) {
    final menuItems = [
      {
        'icon': Icons.person_outline,
        'title': 'Account Settings',
        'gradient': ModernTheme.primaryGradient,
        'onTap': () async {
          await Navigator.pushNamed(context, '/profile/account-settings');
          _loadProfile();
        },
      },
      {
        'icon': Icons.history,
        'title': 'Order History',
        'gradient': ModernTheme.accentGradient,
        'onTap': () => Navigator.pushNamed(context, '/orders'),
      },
      {
        'icon': Icons.favorite_outline,
        'title': 'Favorites',
        'gradient': LinearGradient(
          colors: [Colors.pink.shade400, Colors.pink.shade600],
        ),
        'onTap': () => Navigator.pushNamed(context, '/favorites'),
      },
      {
        'icon': Icons.help_outline,
        'title': 'Help & Support',
        'gradient': ModernTheme.secondaryGradient,
        'onTap': () => Navigator.pushNamed(context, '/profile/help'),
      },
      {
        'icon': Icons.chat_bubble_outline,
        'title': 'Talk to Us',
        'gradient': LinearGradient(
          colors: [Colors.blue.shade400, Colors.blue.shade600],
        ),
        'onTap': () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const MiniChatScreen(),
            ),
          );
        },
      },
      {
        'icon': Icons.info_outline,
        'title': 'About Us',
        'gradient': LinearGradient(
          colors: [Colors.purple.shade400, Colors.purple.shade600],
        ),
        'onTap': () => Navigator.pushNamed(context, '/profile/about-us'),
      },
    ];

    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Menu',
            style: ModernTheme.headingMedium,
          ),
          const SizedBox(height: ModernTheme.spaceM),
          Expanded(
            child: GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: ModernTheme.spaceM,
                mainAxisSpacing: ModernTheme.spaceM,
                childAspectRatio: 1.2,
              ),
              itemCount: menuItems.length,
              itemBuilder: (context, index) {
                final item = menuItems[index];
                return _buildCompactMenuItem(
                  icon: item['icon'] as IconData,
                  title: item['title'] as String,
                  gradient: item['gradient'] as LinearGradient,
                  onTap: item['onTap'] as VoidCallback,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactMenuItem({
    required IconData icon,
    required String title,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(ModernTheme.spaceM),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(height: ModernTheme.spaceS),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: ModernTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MiniChatScreen extends StatefulWidget {
  const MiniChatScreen({super.key});
  @override
  State<MiniChatScreen> createState() => _MiniChatScreenState();
}

class _MiniChatScreenState extends State<MiniChatScreen> {
  final List<String> _messages = [
    'Hi! How can we help you today?',
  ];
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final text = _controller.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _messages.add(text);
        _controller.clear();
      });
      // Simulate a bot reply
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _messages.add(
                'Thank you for reaching out! We will get back to you soon.');
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(title: const Text('Talk to Us')),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final isUser = index % 2 == 1;
                return Align(
                  alignment:
                      isUser ? Alignment.centerRight : Alignment.centerLeft,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    decoration: BoxDecoration(
                      color: isUser
                          ? theme.primaryColor.withAlpha(50)
                          : theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(_messages[index]),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      hintText: 'Type a message...',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
