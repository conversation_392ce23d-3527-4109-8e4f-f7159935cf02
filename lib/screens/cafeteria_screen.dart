import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/providers/cafeteria_ratings_provider.dart';
import 'package:unieatsappv0/screens/cafeteria_ratings_screen.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/widgets/menu_item_card.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'dart:math';

class CafeteriaScreen extends StatefulWidget {
  final String cafeteriaId;
  final SupabaseCafeteria? cafeteria;

  const CafeteriaScreen({
    super.key,
    required this.cafeteriaId,
    this.cafeteria,
  });

  @override
  State<CafeteriaScreen> createState() => _CafeteriaScreenState();
}

class _CafeteriaScreenState extends State<CafeteriaScreen>
    with TickerProviderStateMixin {
  String? selectedCategory;
  late AnimationController _headerAnimationController;
  late AnimationController _contentAnimationController;
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _headerSlideAnimation;
  late Animation<double> _contentFadeAnimation;

  @override
  void initState() {
    super.initState();

    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _contentAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _headerFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOut,
    ));

    _headerSlideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _contentFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentAnimationController,
      curve: Curves.easeOut,
    ));

    _headerAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _contentAnimationController.forward();
    });

    // Load menu items for this cafeteria
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCafeteriaData();
    });
  }

  Future<void> _loadCafeteriaData() async {
    final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);

    // Select this cafeteria and load its menu items
    await supabaseProvider.selectCafeteria(widget.cafeteriaId);

    debugPrint('🍽️ Loaded menu items for cafeteria: ${widget.cafeteriaId}');
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _contentAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final favoritesProvider = Provider.of<FavoritesProvider>(context);
    final supabaseProvider = Provider.of<SupabaseProvider>(context);

    // Get cafeteria from Supabase
    final displayCafeteria = widget.cafeteria ??
        supabaseProvider.cafeterias.firstWhere(
          (c) => c.id == widget.cafeteriaId,
          orElse: () => supabaseProvider.cafeterias.first,
        );

    // We don't need to select the cafeteria here anymore
    // It should already be selected from the dashboard screen

    // Get menu items from Supabase
    final List<SupabaseMenuItem> cafeteriaMenuItems = supabaseProvider.menuItems
        .where((item) => item.cafeteriaId == displayCafeteria.id)
        .toList();

    // Get categories
    final List<String> categories = [
      'All',
      ...supabaseProvider.menuItemsByCategory.keys.toList()
    ];

    // Filter items by category
    final filteredItems = selectedCategory == null || selectedCategory == 'All'
        ? cafeteriaMenuItems
        : cafeteriaMenuItems
            .where((item) => item.category == selectedCategory)
            .toList();

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      body: CustomScrollView(
        slivers: [
          _buildModernSliverAppBar(displayCafeteria, favoritesProvider),
          _buildModernInfoSection(displayCafeteria, categories),
          _buildModernMenuList(filteredItems),
        ],
      ),
    );
  }

  Widget _buildModernSliverAppBar(SupabaseCafeteria cafeteria, FavoritesProvider favoritesProvider) {
    return SliverAppBar(
      expandedHeight: 280,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          children: [
            // Background Image with Gradient Overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.3),
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
              child: cafeteria.imageUrl != null
                  ? Image.network(
                      cafeteria.imageUrl!,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultBackground();
                      },
                    )
                  : _buildDefaultBackground(),
            ),

            // Floating Elements
            _buildFloatingElements(),

            // Content
            Positioned(
              bottom: 60,
              left: 20,
              right: 20,
              child: FadeTransition(
                opacity: _headerFadeAnimation,
                child: SlideTransition(
                  position: _headerSlideAnimation,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        cafeteria.name,
                        style: ModernTheme.headingLarge.copyWith(
                          color: Colors.white,
                          fontSize: 32,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.5),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildStatusBadge(cafeteria.isActive),
                          const SizedBox(width: 12),
                          Icon(
                            Icons.access_time,
                            color: Colors.white.withOpacity(0.9),
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '8:00 AM - 6:00 PM',
                            style: ModernTheme.bodyMedium.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 16, top: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: IconButton(
            icon: Icon(
              favoritesProvider.isCafeteriaFavorite(cafeteria.id)
                  ? Icons.favorite
                  : Icons.favorite_border,
              color: favoritesProvider.isCafeteriaFavorite(cafeteria.id)
                  ? ModernTheme.primaryColor
                  : Colors.white,
            ),
            onPressed: () => _toggleFavorite(cafeteria, favoritesProvider),
          ),
        ),
      ],
    );
  }

  Widget _buildDefaultBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: ModernTheme.primaryGradient,
      ),
      child: Center(
        child: Icon(
          Icons.restaurant,
          color: Colors.white.withOpacity(0.3),
          size: 120,
        ),
      ),
    );
  }

  Widget _buildFloatingElements() {
    return Stack(
      children: [
        Positioned(
          top: 60,
          right: 30,
          child: _buildFloatingCircle(25, Colors.white.withOpacity(0.1)),
        ),
        Positioned(
          top: 120,
          left: 40,
          child: _buildFloatingCircle(15, ModernTheme.secondaryColor.withOpacity(0.2)),
        ),
        Positioned(
          bottom: 100,
          right: 60,
          child: _buildFloatingCircle(20, ModernTheme.primaryColor.withOpacity(0.2)),
        ),
      ],
    );
  }

  Widget _buildFloatingCircle(double size, Color color) {
    return AnimatedBuilder(
      animation: _headerAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            sin(_headerAnimationController.value * 2 * pi) * 5,
            cos(_headerAnimationController.value * 2 * pi) * 5,
          ),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusBadge(bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: isActive
            ? ModernTheme.accentGradient
            : LinearGradient(
                colors: [Colors.red.shade400, Colors.red.shade600],
              ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: (isActive ? ModernTheme.accentColor : Colors.red).withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            isActive ? 'Open Now' : 'Closed',
            style: ModernTheme.labelMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _toggleFavorite(SupabaseCafeteria cafeteria, FavoritesProvider favoritesProvider) async {
    await favoritesProvider.toggleSupabaseCafeteriaFavorite(cafeteria);

    if (mounted) {
      final isFavorite = favoritesProvider.isCafeteriaFavorite(cafeteria.id);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                isFavorite
                    ? 'Added ${cafeteria.name} to favorites!'
                    : 'Removed ${cafeteria.name} from favorites!',
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
          backgroundColor: isFavorite ? ModernTheme.accentColor : ModernTheme.primaryColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  Widget _buildDescriptionCard(String description) {
    return Container(
      padding: const EdgeInsets.all(ModernTheme.spaceM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: ModernTheme.accentGradient,
                  borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: ModernTheme.spaceM),
              Text(
                'About Us',
                style: ModernTheme.headingSmall,
              ),
            ],
          ),
          const SizedBox(height: ModernTheme.spaceM),
          Text(
            description,
            style: ModernTheme.bodyMedium.copyWith(
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernCategoryChips(List<String> categories) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: categories.asMap().entries.map((entry) {
          final index = entry.key;
          final category = entry.value;
          final isSelected = selectedCategory == category ||
              (selectedCategory == null && category == 'All');

          return AnimatedBuilder(
            animation: _contentAnimationController,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(
                  (1 - _contentAnimationController.value) * 50 * (index + 1),
                  0,
                ),
                child: Opacity(
                  opacity: _contentAnimationController.value,
                  child: Container(
                    margin: const EdgeInsets.only(right: ModernTheme.spaceM),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedCategory = category == 'All' ? null : category;
                        });
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          gradient: isSelected
                              ? ModernTheme.primaryGradient
                              : null,
                          color: isSelected
                              ? null
                              : Colors.white,
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: isSelected
                              ? [
                                  BoxShadow(
                                    color: ModernTheme.primaryColor.withOpacity(0.3),
                                    blurRadius: 12,
                                    offset: const Offset(0, 4),
                                  ),
                                ]
                              : ModernTheme.softShadow,
                          border: isSelected
                              ? null
                              : Border.all(
                                  color: ModernTheme.primaryColor.withOpacity(0.2),
                                  width: 1,
                                ),
                        ),
                        child: Text(
                          category,
                          style: ModernTheme.labelMedium.copyWith(
                            color: isSelected
                                ? Colors.white
                                : ModernTheme.textPrimary,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildModernMenuList(List<SupabaseMenuItem> items) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: ModernTheme.spaceM),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return AnimatedBuilder(
              animation: _contentAnimationController,
              builder: (context, child) {
                final delay = index * 0.1;
                final animationValue = Curves.easeOut.transform(
                  (_contentAnimationController.value - delay).clamp(0.0, 1.0),
                );

                return Transform.translate(
                  offset: Offset(0, (1 - animationValue) * 30),
                  child: Opacity(
                    opacity: animationValue,
                    child: Container(
                      margin: const EdgeInsets.only(bottom: ModernTheme.spaceM),
                      child: _buildModernMenuItemCard(items[index]),
                    ),
                  ),
                );
              },
            );
          },
          childCount: items.length,
        ),
      ),
    );
  }

  Widget _buildModernMenuItemCard(SupabaseMenuItem item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
          onTap: () {
            // Navigate to item details
          },
          child: Padding(
            padding: const EdgeInsets.all(ModernTheme.spaceM),
            child: Row(
              children: [
                // Item Image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                    gradient: ModernTheme.secondaryGradient,
                  ),
                  child: item.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                          child: Image.network(
                            item.imageUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.fastfood,
                                color: Colors.white,
                                size: 32,
                              );
                            },
                          ),
                        )
                      : const Icon(
                          Icons.fastfood,
                          color: Colors.white,
                          size: 32,
                        ),
                ),

                const SizedBox(width: ModernTheme.spaceM),

                // Item Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name,
                        style: ModernTheme.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (item.description != null)
                        Text(
                          item.description!,
                          style: ModernTheme.bodyMedium.copyWith(
                            color: ModernTheme.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      const SizedBox(height: 4),
                      // Rating Row
                      Row(
                        children: [
                          Row(
                            children: List.generate(5, (index) {
                              final rating = item.rating ?? 4.0;
                              return Icon(
                                index < rating.floor()
                                    ? Icons.star
                                    : (index < rating ? Icons.star_half : Icons.star_border),
                                color: Colors.amber,
                                size: 14,
                              );
                            }),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${(item.rating ?? 4.0).toStringAsFixed(1)}',
                            style: ModernTheme.bodySmall.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              gradient: ModernTheme.primaryGradient,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${item.price.toStringAsFixed(2)} EGP',
                              style: ModernTheme.labelMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          const Spacer(),
                          if (!item.isAvailable)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.red.shade100,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'Unavailable',
                                style: ModernTheme.bodySmall.copyWith(
                                  color: Colors.red.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Add Button
                Container(
                  decoration: BoxDecoration(
                    gradient: ModernTheme.accentGradient,
                    borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                    boxShadow: [
                      BoxShadow(
                        color: ModernTheme.accentColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                      onTap: item.isAvailable ? () {
                        final cartProvider = Provider.of<CartProvider>(context, listen: false);
                        final cartItem = CartItem(
                          id: item.id,
                          name: item.name,
                          price: item.price,
                          quantity: 1,
                          image: item.imageUrl ?? 'assets/images/placeholder.png',
                          cafeteriaName: widget.cafeteria?.name ?? 'Unknown',
                          buildingName: widget.cafeteria?.location ?? 'Campus',
                          customizations: {},
                        );
                        cartProvider.addItem(cartItem);

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                const Icon(Icons.check_circle, color: Colors.white),
                                const SizedBox(width: 8),
                                Text('${item.name} added to cart'),
                              ],
                            ),
                            backgroundColor: ModernTheme.accentColor,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            margin: const EdgeInsets.all(16),
                          ),
                        );
                      } : null,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        child: const Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernInfoSection(SupabaseCafeteria cafeteria, List<String> categories) {
    return SliverToBoxAdapter(
      child: FadeTransition(
        opacity: _contentFadeAnimation,
        child: Container(
          margin: const EdgeInsets.all(ModernTheme.spaceM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Info Cards Row
              Row(
                children: [
                  Expanded(
                    child: _buildInfoCard(
                      icon: Icons.location_on,
                      title: 'Location',
                      subtitle: cafeteria.location ?? 'Campus',
                      gradient: ModernTheme.primaryGradient,
                    ),
                  ),
                  const SizedBox(width: ModernTheme.spaceM),
                  Expanded(
                    child: _buildRatingCard(cafeteria),
                  ),
                ],
              ),

              const SizedBox(height: ModernTheme.spaceL),

              // Description Card
              if (cafeteria.description != null && cafeteria.description!.isNotEmpty)
                _buildDescriptionCard(cafeteria.description!),

              const SizedBox(height: ModernTheme.spaceXL),

              // Menu Section Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: ModernTheme.secondaryGradient,
                      borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                      boxShadow: ModernTheme.softShadow,
                    ),
                    child: const Icon(
                      Icons.restaurant_menu,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: ModernTheme.spaceM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Our Menu',
                          style: ModernTheme.headingMedium,
                        ),
                        Text(
                          'Fresh ingredients, amazing taste',
                          style: ModernTheme.bodyMedium.copyWith(
                            color: ModernTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: ModernTheme.spaceL),

              // Modern Category Chips
              _buildModernCategoryChips(categories),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required LinearGradient gradient,
  }) {
    return Container(
      padding: const EdgeInsets.all(ModernTheme.spaceM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(height: ModernTheme.spaceS),
          Text(
            title,
            style: ModernTheme.labelMedium.copyWith(
              color: ModernTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: ModernTheme.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingCard(SupabaseCafeteria cafeteria) {
    return Consumer<CafeteriaRatingsProvider>(
      builder: (context, ratingsProvider, child) {
        final avgRating = ratingsProvider.getAverageRatingForCafeteria(cafeteria.id);
        final ratings = ratingsProvider.getRatingsForCafeteria(cafeteria.id);
        final displayRating = avgRating > 0 ? avgRating : (cafeteria.rating ?? 4.0);

        return GestureDetector(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => CafeteriaRatingsScreen(
                  cafeteriaId: cafeteria.id,
                  cafeteriaName: cafeteria.name,
                ),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(ModernTheme.spaceM),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
              boxShadow: ModernTheme.mediumShadow,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: ModernTheme.accentGradient,
                    borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                  ),
                  child: const Icon(
                    Icons.star,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(height: ModernTheme.spaceS),
                Text(
                  'Rating',
                  style: ModernTheme.labelMedium.copyWith(
                    color: ModernTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      displayRating.toStringAsFixed(1),
                      style: ModernTheme.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < displayRating.floor()
                              ? Icons.star
                              : (index < displayRating ? Icons.star_half : Icons.star_border),
                          color: ModernTheme.secondaryColor,
                          size: 12,
                        );
                      }),
                    ),
                  ],
                ),
                Text(
                  '${ratings.length} reviews',
                  style: ModernTheme.bodySmall.copyWith(
                    color: ModernTheme.textTertiary,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
