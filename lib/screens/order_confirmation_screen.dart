import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:flutter/services.dart';

class OrderConfirmationScreen extends StatelessWidget {
  final Order order;
  const OrderConfirmationScreen({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Confirmation'),
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Success message
            Text(
              '🎉 Your order has been placed successfully!\nPlease be ready to pick it up at ${order.pickupTime}.',
              style: theme.textTheme.displaySmall?.copyWith(color: theme.primaryColor),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            // Order number
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Order #${order.orderNumber}', style: theme.textTheme.titleMedium),
                IconButton(
                  icon: const Icon(Icons.copy, size: 18),
                  tooltip: 'Copy Order Number',
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: order.orderNumber));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Order number copied!')),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Order details
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Order Details', style: theme.textTheme.titleMedium),
                    const SizedBox(height: 8),
                    ...order.items.map((item) => Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(child: Text('${item.quantity}x ${item.name}', style: theme.textTheme.bodyMedium)),
                        Text('${item.calculateItemTotal().toStringAsFixed(2)} EGP', style: theme.textTheme.bodyMedium),
                      ],
                    )),
                    const Divider(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Pickup Time:', style: theme.textTheme.bodyMedium),
                        Text(order.pickupTime, style: theme.textTheme.bodyMedium),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Total:', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
                        Text('${order.totalPrice.toStringAsFixed(2)} EGP', style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            // Get Help button
            ElevatedButton.icon(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (ctx) => AlertDialog(
                    title: const Text('Get Help'),
                    content: const Text('For support, contact <NAME_EMAIL> or call 123-456-7890.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(ctx).pop(),
                        child: const Text('Close'),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.help_outline),
              label: const Text('Get Help'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).pushNamedAndRemoveUntil('/dashboard', (route) => false);
                  },
                  child: const Text('Home'),
                ),
                OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).pushNamedAndRemoveUntil('/profile', (route) => false);
                  },
                  child: const Text('Order History'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 