import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/menu_item.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/services/supabase_cafeteria_service.dart';
import 'package:unieatsappv0/services/rating_service.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';

class ItemDetailsScreenNew extends StatefulWidget {
  // Accept either MenuItem or SupabaseMenuItem
  final dynamic menuItem;

  const ItemDetailsScreenNew({
    super.key,
    required this.menuItem,
  });

  @override
  State<ItemDetailsScreenNew> createState() => _ItemDetailsScreenNewState();
}

class _ItemDetailsScreenNewState extends State<ItemDetailsScreenNew> {
  int _quantity = 1;
  String _selectedTab = 'Details';
  final List<String> _tabs = ['Details', 'Ingredients', 'Nutrition', 'Reviews'];
  final TextEditingController _notesController = TextEditingController();
  final SupabaseCafeteriaService _cafeteriaService = SupabaseCafeteriaService();

  SupabaseCafeteria? _cafeteria;
  List<Map<String, dynamic>> _ratings = [];
  bool _isLoading = true;
  bool _isFavorite = false;

  // Helper getters to work with both MenuItem and SupabaseMenuItem
  String get itemId => widget.menuItem is SupabaseMenuItem
      ? (widget.menuItem as SupabaseMenuItem).id
      : (widget.menuItem as MenuItem).id;

  String get itemName => widget.menuItem is SupabaseMenuItem
      ? (widget.menuItem as SupabaseMenuItem).name
      : (widget.menuItem as MenuItem).name;

  String get itemDescription => widget.menuItem is SupabaseMenuItem
      ? (widget.menuItem as SupabaseMenuItem).description ?? 'No description available'
      : (widget.menuItem as MenuItem).description;

  double get itemPrice => widget.menuItem is SupabaseMenuItem
      ? (widget.menuItem as SupabaseMenuItem).price
      : (widget.menuItem as MenuItem).price;

  String get itemImage => widget.menuItem is SupabaseMenuItem
      ? (widget.menuItem as SupabaseMenuItem).imageUrl ?? 'assets/images/food-placeholder.png'
      : (widget.menuItem as MenuItem).image;

  String get itemCategory => widget.menuItem is SupabaseMenuItem
      ? (widget.menuItem as SupabaseMenuItem).category ?? 'Other'
      : (widget.menuItem as MenuItem).category;

  String get cafeteriaId => widget.menuItem is SupabaseMenuItem
      ? (widget.menuItem as SupabaseMenuItem).cafeteriaId
      : (widget.menuItem as MenuItem).cafeteriaId;

  @override
  void initState() {
    super.initState();
    _loadItemData();

    // Debug logging
    debugPrint('🔍 ItemDetailsScreen - Menu item type: ${widget.menuItem.runtimeType}');
    if (widget.menuItem is SupabaseMenuItem) {
      final item = widget.menuItem as SupabaseMenuItem;
      debugPrint('🔍 SupabaseMenuItem - Ingredients: ${item.ingredients}');
      debugPrint('🔍 SupabaseMenuItem - Nutrition: ${item.nutritionInfo}');
    } else if (widget.menuItem is MenuItem) {
      final item = widget.menuItem as MenuItem;
      debugPrint('🔍 MenuItem - Ingredients: ${item.ingredients}');
      debugPrint('🔍 MenuItem - Nutrition: ${item.nutritionInfo}');
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadItemData() async {
    try {
      // Load cafeteria data
      final cafeteria = await _cafeteriaService.getCafeteriaById(cafeteriaId);

      // Load ratings for this menu item
      final ratings = await RatingService.getMenuItemRatings(itemId);

      if (mounted) {
        // Check if item is favorite
        final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);
        final isFavorite = favoritesProvider.isMenuItemFavorite(itemId);

        setState(() {
          _cafeteria = cafeteria;
          _ratings = ratings;
          _isFavorite = isFavorite;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading item data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cartProvider = Provider.of<CartProvider>(context);
    final favoritesProvider = Provider.of<FavoritesProvider>(context);

    if (_isLoading) {
      return Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              // Back button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.black87),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Back to Menu',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              // Loading indicator
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content - scrollable
            Padding(
              padding:
                  const EdgeInsets.only(bottom: 80), // Space for bottom bar
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Back button
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.black87,
                            ),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Back to Menu',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.primaryColor,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: Icon(
                              _isFavorite ? Icons.favorite : Icons.favorite_border,
                              color: _isFavorite ? Colors.red : Colors.black87,
                            ),
                            onPressed: () {
                              favoritesProvider.toggleMenuItemFavorite(itemId);
                              setState(() {
                                _isFavorite = !_isFavorite;
                              });
                            },
                          ),
                        ],
                      ),
                    ),

                    // Item image
                    Stack(
                      children: [
                        SizedBox(
                          width: double.infinity,
                          height: 200,
                          child: _buildItemImage(itemImage, theme),
                        ),
                      ],
                    ),

                    // Item details
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Item name and price
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  itemName,
                                  style:
                                      theme.textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Text(
                                '${itemPrice.toStringAsFixed(2)} EGP',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // Cafeteria info
                          if (_cafeteria != null) ...[
                            Text(
                              _cafeteria!.name,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              _cafeteria!.location ?? 'Location not available',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                          ] else ...[
                            Text(
                              'Loading cafeteria info...',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                          ],

                          // Category tag
                          if (itemCategory.isNotEmpty)
                            Container(
                              margin: const EdgeInsets.symmetric(vertical: 12),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color:
                                    theme.colorScheme.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                itemCategory,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),

                          // Tabs
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: _tabs.map((tab) {
                                final isSelected = _selectedTab == tab;
                                return Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: ElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        _selectedTab = tab;
                                      });
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isSelected
                                          ? theme.primaryColor
                                          : theme.colorScheme.surface,
                                      foregroundColor: isSelected
                                          ? Colors.white
                                          : AppTheme.textColor,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 20, vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                        side: BorderSide(
                                          color: isSelected
                                              ? Colors.transparent
                                              : theme.dividerColor,
                                        ),
                                      ),
                                    ),
                                    child: Text(tab),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Tab content
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: _buildTabContent(),
                    ),
                  ],
                ),
              ),
            ),

            // Quantity and add to cart - fixed at bottom
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Quantity selector
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.dividerColor),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.remove),
                            onPressed: () {
                              if (_quantity > 1) {
                                setState(() {
                                  _quantity--;
                                });
                              }
                            },
                          ),
                          Text(
                            '$_quantity',
                            style: theme.textTheme.titleMedium,
                          ),
                          IconButton(
                            icon: const Icon(Icons.add),
                            onPressed: () {
                              setState(() {
                                _quantity++;
                              });
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Add to cart button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _cafeteria != null ? () {
                          final cartItem = CartItem(
                            id: itemId,
                            name: itemName,
                            price: itemPrice,
                            image: itemImage,
                            cafeteriaName: _cafeteria!.name,
                            buildingName: _cafeteria!.location ?? 'Unknown location',
                            quantity: _quantity,
                            customizations: {},
                            notes: _notesController.text.isNotEmpty
                                ? _notesController.text
                                : null,
                            menuItem: widget.menuItem is MenuItem ? widget.menuItem : null,
                          );
                          cartProvider.addItem(cartItem);

                          // Show custom cart notification
                          SnackBarUtils.showCartSnackBar(
                            context: context,
                            message: '$itemName added to cart',
                            onViewCart: () {
                              Navigator.of(context).pushNamed('/cart');
                            },
                          );
                        } : null,
                        child: Text(_cafeteria != null ? 'Add to Cart' : 'Loading...'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    final theme = Theme.of(context);

    switch (_selectedTab) {
      case 'Details':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              itemDescription,
              style: theme.textTheme.bodyMedium?.copyWith(
                height: 1.5,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  size: 16,
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Preparation time: 10-12 minutes',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Show ingredients if available (for both MenuItem and SupabaseMenuItem)
            if (_getIngredients() != null && _getIngredients()!.isNotEmpty) ...[
              Row(
                children: [
                  const Icon(
                    Icons.warning_amber_rounded,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Ingredients: ${_getIngredients()!.join(", ")}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ] else ...[
              Row(
                children: [
                  const Icon(
                    Icons.warning_amber_rounded,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Allergens: Please check with cafeteria',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
            ],
            const SizedBox(height: 12),

            // Special instructions/notes
            Text(
              'Special Instructions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _notesController,
              decoration: InputDecoration(
                hintText: 'Add notes (e.g., no onions, extra sauce, etc.)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 24),
          ],
        );
      case 'Ingredients':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_getIngredients() != null && _getIngredients()!.isNotEmpty) ...[
              Text(
                'Ingredients:',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ..._getIngredients()!.map((ingredient) =>
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      const Icon(Icons.circle, size: 6, color: AppTheme.textSecondaryColor),
                      const SizedBox(width: 8),
                      Text(
                        ingredient,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ).toList(),
            ] else ...[
              Text(
                'Ingredients information not available.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
            const SizedBox(height: 24),
          ],
        );
      case 'Nutrition':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_getNutritionInfo() != null && _getNutritionInfo()!.isNotEmpty) ...[
              Text(
                'Nutrition Information:',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ..._getNutritionInfo()!.entries.map((entry) =>
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _formatNutritionKey(entry.key),
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        _formatNutritionValue(entry.key, entry.value),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ).toList(),
            ] else ...[
              Text(
                'Nutrition information not available.',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
            const SizedBox(height: 24),
          ],
        );
      case 'Reviews':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Reviews:',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            if (_ratings.isNotEmpty) ...[
              ..._ratings.map((rating) =>
                Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            ...List.generate(5, (index) => Icon(
                              index < rating['rating'] ? Icons.star : Icons.star_border,
                              size: 16,
                              color: Colors.amber,
                            )),
                            const SizedBox(width: 8),
                            Text(
                              rating['profiles']?['full_name'] ?? 'Anonymous',
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        if (rating['review_comment'] != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            rating['review_comment'],
                            style: theme.textTheme.bodyMedium,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ).toList(),
            ] else ...[
              Text(
                'No reviews yet. Be the first to review this item!',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
            const SizedBox(height: 24),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  /// Build item image widget with support for different image types
  Widget _buildItemImage(String imageUrl, ThemeData theme) {
    return RobustImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      timeout: const Duration(seconds: 8), // Longer timeout for detail view
    );
  }

  /// Get ingredients from either MenuItem or SupabaseMenuItem
  List<String>? _getIngredients() {
    if (widget.menuItem is SupabaseMenuItem) {
      return (widget.menuItem as SupabaseMenuItem).ingredients;
    } else if (widget.menuItem is MenuItem) {
      return (widget.menuItem as MenuItem).ingredients;
    }
    return null;
  }

  /// Get nutrition info from either MenuItem or SupabaseMenuItem
  Map<String, dynamic>? _getNutritionInfo() {
    if (widget.menuItem is SupabaseMenuItem) {
      return (widget.menuItem as SupabaseMenuItem).nutritionInfo;
    } else if (widget.menuItem is MenuItem) {
      return (widget.menuItem as MenuItem).nutritionInfo;
    }
    return null;
  }

  /// Format nutrition key for display
  String _formatNutritionKey(String key) {
    switch (key.toLowerCase()) {
      case 'calories':
        return 'Calories';
      case 'protein':
        return 'Protein';
      case 'carbs':
        return 'Carbohydrates';
      case 'fat':
        return 'Fat';
      case 'fiber':
        return 'Fiber';
      case 'sugar':
        return 'Sugar';
      default:
        return key.substring(0, 1).toUpperCase() + key.substring(1);
    }
  }

  /// Format nutrition value for display with units
  String _formatNutritionValue(String key, dynamic value) {
    switch (key.toLowerCase()) {
      case 'calories':
        return '$value kcal';
      case 'protein':
      case 'carbs':
      case 'fat':
      case 'fiber':
      case 'sugar':
        return '${value}g';
      default:
        return value.toString();
    }
  }
}
