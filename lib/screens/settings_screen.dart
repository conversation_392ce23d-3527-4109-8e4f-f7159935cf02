import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/user_service.dart';
import 'package:unieatsappv0/services/local_storage_service.dart';
import 'package:unieatsappv0/widgets/profile_picture_picker.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final UserService _userService = UserService();
  final LocalStorageService _storageService = LocalStorageService();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  Map<String, dynamic> _settings = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await _userService.init();
    await _storageService.init();

    final profile = await _userService.getUserProfile();
    final settings = await _userService.getSettings();

    setState(() {
      _settings = settings;

      _nameController.text = profile['name']?.toString() ?? '';
      _emailController.text = profile['email']?.toString() ?? '';
      _phoneController.text = profile['phone']?.toString() ?? '';
      _addressController.text = profile['address']?.toString() ?? '';
    });
  }

  Future<void> _saveProfile() async {
    await _userService.saveUserProfile(
      name: _nameController.text,
      email: _emailController.text,
      phone: _phoneController.text,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profile updated successfully')),
      );
    }
  }

  Future<void> _saveSettings() async {
    await _userService.saveSettings(
      notifications: _settings['notifications'] ?? false,
      darkMode: _settings['dark_mode'] ?? false,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Settings updated successfully')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () {
              _saveProfile();
              _saveSettings();
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Profile Picture',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Center(
              child: ProfilePicturePicker(
                size: 120,
                onImageSelected: (path) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Profile picture updated')),
                  );
                },
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Personal Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Preferences',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Notifications'),
              value: _settings['notifications'] ?? true,
              onChanged: (value) {
                setState(() {
                  _settings['notifications'] = value;
                });
              },
            ),
            SwitchListTile(
              title: const Text('Dark Mode'),
              value: _settings['dark_mode'] ?? false,
              onChanged: (value) {
                setState(() {
                  _settings['dark_mode'] = value;
                });
              },
            ),
            ListTile(
              title: const Text('Language'),
              subtitle: Text(_settings['language'] ?? 'English'),
              onTap: () {
                // Show language selection dialog
              },
            ),
            ListTile(
              title: const Text('Currency'),
              subtitle: Text(_settings['currency'] ?? 'USD'),
              onTap: () {
                // Show currency selection dialog
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }
}
