import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/widgets/glass_container.dart';
import 'package:unieatsappv0/widgets/modern_button.dart';
import 'package:unieatsappv0/widgets/animated_widgets.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/utils/animations.dart';

class ModernSearchScreen extends StatefulWidget {
  const ModernSearchScreen({super.key});

  @override
  State<ModernSearchScreen> createState() => _ModernSearchScreenState();
}

class _ModernSearchScreenState extends State<ModernSearchScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _searchController;
  
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  List<dynamic> _searchResults = [];
  bool _isSearching = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _searchController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _searchController.forward();
    _textController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _searchController.dispose();
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _textController.text.trim();
    if (query != _searchQuery) {
      setState(() {
        _searchQuery = query;
      });
      
      if (query.isNotEmpty) {
        _performSearch(query);
      } else {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
      }
    }
  }

  void _performSearch(String query) async {
    setState(() {
      _isSearching = true;
    });
    
    try {
      final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
      
      // Search in menu items
      final results = supabaseProvider.menuItems
          .where((item) => 
              item.name.toLowerCase().contains(query.toLowerCase()) ||
              (item.description?.toLowerCase().contains(query.toLowerCase()) ?? false))
          .toList();
      
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppTheme.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Animated background
            _buildAnimatedBackground(),
            
            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Search Header
                  _buildSearchHeader(),
                  
                  // Search Content
                  Expanded(
                    child: _buildSearchContent(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Stack(
          children: [
            Positioned(
              top: 80 + 30 * (0.5 + 0.5 * math.sin(_backgroundController.value * 2 * math.pi)),
              left: 50 + 25 * (0.5 + 0.5 * math.cos(_backgroundController.value * 2 * math.pi)),
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.accentColor.withOpacity(0.1),
                      AppTheme.accentColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchHeader() {
    return AnimatedBuilder(
      animation: _searchController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _searchController.value)),
          child: Opacity(
            opacity: _searchController.value,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    children: [
                      ModernIconButton(
                        icon: Icons.arrow_back,
                        onPressed: () => Navigator.pop(context),
                        color: AppTheme.textColor,
                        backgroundColor: AppTheme.glassColor,
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Text(
                          'Search Menu',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // Search Bar
                  GlassContainer(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: TextField(
                      controller: _textController,
                      focusNode: _focusNode,
                      style: const TextStyle(
                        color: AppTheme.textColor,
                        fontSize: 16,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Search for food...',
                        hintStyle: const TextStyle(
                          color: AppTheme.textSecondaryColor,
                        ),
                        prefixIcon: const Icon(
                          Icons.search,
                          color: AppTheme.primaryColor,
                        ),
                        suffixIcon: _searchQuery.isNotEmpty
                            ? IconButton(
                                icon: const Icon(
                                  Icons.clear,
                                  color: AppTheme.textSecondaryColor,
                                ),
                                onPressed: () {
                                  _textController.clear();
                                  _focusNode.requestFocus();
                                },
                              )
                            : null,
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchContent() {
    if (_searchQuery.isEmpty) {
      return _buildEmptySearch();
    } else if (_isSearching) {
      return _buildLoadingState();
    } else if (_searchResults.isEmpty) {
      return _buildNoResultsState();
    } else {
      return _buildSearchResults();
    }
  }

  Widget _buildEmptySearch() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedWidgets.breathingAnimation(
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppTheme.accentGradient,
                ),
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(
                Icons.search,
                size: 50,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'Search for delicious food',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Find your favorite meals and snacks',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedWidgets.pulsingLoader(
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppTheme.accentGradient,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.search,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Searching for "$_searchQuery"...',
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppTheme.accentGradient,
              ),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.search_off,
              size: 50,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'No results found',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Try searching for something else',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final item = _searchResults[index];
        return AnimatedWidgets.staggeredListItem(
          index: index,
          child: _buildResultCard(item),
        );
      },
    );
  }

  Widget _buildResultCard(dynamic item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: FloatingGlassCard(
        onTap: () => Navigator.pushNamed(context, '/item-details', arguments: item),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: RobustImage(
                imageUrl: item.imageUrl ?? '',
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.description ?? 'Delicious item',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  AnimatedWidgets.animatedPrice(
                    value: item.price,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
