import 'package:flutter/material.dart';

class MigrationScreen extends StatefulWidget {
  const MigrationScreen({super.key});

  @override
  State<MigrationScreen> createState() => _MigrationScreenState();
}

class _MigrationScreenState extends State<MigrationScreen> {
  // Migration service is not used directly in this demo screen
  bool _isMigrating = false;
  String _status = 'Ready to migrate data from Firebase to Supabase';
  double _progress = 0.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Migration'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Firebase to Supabase Migration',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              _status,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            if (_isMigrating) ...[
              LinearProgressIndicator(value: _progress),
              const SizedBox(height: 10),
              Text('Progress: ${(_progress * 100).toStringAsFixed(1)}%'),
            ],
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: _isMigrating ? null : () => _startMigration(context),
              child: const Text('Start Migration'),
            ),
            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 20),
            const Text(
              'Migration Steps:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const Text('1. User accounts and profiles'),
            const Text('2. Cafeterias'),
            const Text('3. Menu items'),
            const Text('4. Orders and order items'),
            const Text('5. Ratings and reviews'),
            const SizedBox(height: 20),
            const Text(
              'Note: This process may take some time depending on the amount of data.',
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _startMigration(BuildContext context) async {
    setState(() {
      _isMigrating = true;
      _status = 'Starting migration...';
      _progress = 0.0;
    });

    // This is a placeholder for the actual migration logic
    // In a real implementation, you would:
    // 1. Fetch data from Firebase
    // 2. Transform it to Supabase format
    // 3. Insert it into Supabase
    // 4. Update progress as you go

    // Simulate migration steps
    await _simulateMigrationStep('Migrating user accounts...', 0.2);
    await _simulateMigrationStep('Migrating cafeterias...', 0.4);
    await _simulateMigrationStep('Migrating menu items...', 0.6);
    await _simulateMigrationStep('Migrating orders...', 0.8);
    await _simulateMigrationStep('Migrating ratings...', 1.0);

    setState(() {
      _status = 'Migration completed successfully!';
      _isMigrating = false;
    });

    // Show completion dialog - only if still mounted
    _showCompletionDialog();
  }

  // Separate method to show the completion dialog
  void _showCompletionDialog() {
    if (!mounted) return;

    // Now we're in a synchronous context, so it's safe to use context
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Migration Complete'),
        content: const Text(
          'All data has been successfully migrated from Firebase to Supabase.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _simulateMigrationStep(String status, double progress) async {
    setState(() {
      _status = status;
      _progress = progress;
    });

    // Simulate work being done
    await Future.delayed(const Duration(seconds: 2));
  }
}
