import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/theme/app_theme.dart';

class OrderConfirmationScreenNew extends StatefulWidget {
  final Order order;
  final bool supabaseOrderCreated;

  const OrderConfirmationScreenNew({
    super.key,
    required this.order,
    this.supabaseOrderCreated = false,
  });

  @override
  State<OrderConfirmationScreenNew> createState() =>
      _OrderConfirmationScreenNewState();
}

class _OrderConfirmationScreenNewState
    extends State<OrderConfirmationScreenNew> {
  bool _autoNavigate = true;

  @override
  void initState() {
    super.initState();
    _scheduleNavigation();
  }

  void _scheduleNavigation() {
    // Capture the navigator before the async gap
    final navigator = Navigator.of(context);

    // Automatically navigate to order tracking after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _autoNavigate) {
        navigator.pushReplacementNamed(
          '/order_tracking',
          arguments: widget.order,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Success icon
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: AppTheme.successColor.withAlpha(30),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: AppTheme.successColor,
                        size: 60,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Success message
                    Text(
                      'Order Placed!',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Order description
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Column(
                        children: [
                          Text(
                            'Your order has been successfully placed and is being prepared.',
                            textAlign: TextAlign.center,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          if (widget.supabaseOrderCreated) ...[
                            const SizedBox(height: 8),
                            Text(
                              'Order synchronized with online system.',
                              textAlign: TextAlign.center,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Pickup time
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 32),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest
                            .withAlpha(50),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Pick-up Time',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.order.pickupTime,
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // View order details button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: ElevatedButton(
                onPressed: () {
                  // Cancel auto-navigation
                  setState(() {
                    _autoNavigate = false;
                  });

                  // Navigate to order tracking screen
                  Navigator.of(context).pushReplacementNamed(
                    '/order_tracking',
                    arguments: widget.order,
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.visibility),
                    SizedBox(width: 8),
                    Text(
                      'View Order Details',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Order tracking message
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'You\'ll be redirected to the order tracking page in a moment...',
                textAlign: TextAlign.center,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
