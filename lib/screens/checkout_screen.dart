import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cartProvider = Provider.of<CartProvider>(context);
    final cartItems = cartProvider.items;
    final subtotal = cartProvider.subtotal;
    final serviceFee = cartProvider.serviceFee;
    final total = cartProvider.total;

    // Get cafeteria info from arguments or cart
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final cafeteriaName = args?['cafeteriaName'] ??
        (cartItems.isNotEmpty ? cartItems.first.cafeteriaName : 'Cafeteria');
    final cafeteriaLocation = args?['cafeteriaLocation'] ??
        (cartItems.isNotEmpty ? cartItems.first.buildingName : '');

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildModernAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: cartItems.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 80,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Your cart is empty',
                    style: theme.textTheme.displaySmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add items to your cart to checkout',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.textTheme.bodySmall?.color,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacementNamed('/dashboard');
                    },
                    child: const Text('Browse Menu'),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'Checkout for $cafeteriaName',
                    style: theme.textTheme.headlineMedium,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Total: ${total.toStringAsFixed(2)} EGP',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _handleCheckout,
                    child: const Text('Place Order'),
                  ),
                ],
              ),
            ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: FadeTransition(
        opacity: _fadeAnimation,
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: ModernTheme.primaryGradient,
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.payment,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: ModernTheme.spaceM),
            Text(
              'Checkout',
              style: ModernTheme.headingMedium,
            ),
          ],
        ),
      ),
    );
  }

  void _handleCheckout() async {
    // Simple checkout implementation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Order placed successfully!'),
        backgroundColor: Colors.green,
      ),
    );
    
    // Navigate to order tracking or success screen
    Navigator.of(context).pushReplacementNamed('/dashboard');
  }
}
