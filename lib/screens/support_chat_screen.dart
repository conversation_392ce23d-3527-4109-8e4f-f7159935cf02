import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/chat_service.dart';
import 'package:unieatsappv0/services/support_ticket_service.dart';
import 'package:unieatsappv0/services/supabase_service.dart';
import 'package:unieatsappv0/models/chat_models.dart';
import 'package:intl/intl.dart';

class SupportChatScreen extends StatefulWidget {
  final String? orderId;
  final String? orderNumber;

  const SupportChatScreen({
    super.key,
    this.orderId,
    this.orderNumber,
  });

  @override
  State<SupportChatScreen> createState() => _SupportChatScreenState();
}

class _SupportChatScreenState extends State<SupportChatScreen> {
  final ChatService _chatService = ChatService();
  final SupabaseService _supabaseService = SupabaseService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<ChatConversation> _conversations = [];
  List<ChatMessage> _messages = [];
  ChatConversation? _selectedConversation;
  bool _isLoading = false;
  bool _isLoadingMessages = false;

  @override
  void initState() {
    super.initState();
    _loadConversations();

    // If order details are provided, automatically create or find order-related conversation
    if (widget.orderId != null && widget.orderNumber != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _createOrderHelpConversation();
      });
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    if (_selectedConversation != null) {
      _chatService.unsubscribeFromConversation(_selectedConversation!.id);
    }
    super.dispose();
  }

  Future<void> _loadConversations() async {
    setState(() => _isLoading = true);
    try {
      final conversations = await _chatService.getUserConversations();
      setState(() {
        _conversations = conversations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load conversations: $e');
    }
  }

  Future<void> _loadMessages(String conversationId) async {
    setState(() => _isLoadingMessages = true);
    try {
      final messages = await _chatService.getConversationMessages(conversationId);
      setState(() {
        _messages = messages;
        // Ensure messages are sorted by creation time (oldest first)
        _messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        _isLoadingMessages = false;
      });
      _scrollToBottom();
      await _chatService.markMessagesAsRead(conversationId);
    } catch (e) {
      setState(() => _isLoadingMessages = false);
      _showErrorSnackBar('Failed to load messages: $e');
    }
  }

  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _selectedConversation == null) return;

    _messageController.clear();

    try {
      final message = await _chatService.sendMessage(
        conversationId: _selectedConversation!.id,
        content: content,
      );

      if (message != null) {
        setState(() {
          _messages.add(message);
          // Sort messages by creation time to ensure proper order
          _messages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        });
        _scrollToBottom();

        // Refresh messages after a short delay to ensure we get any admin responses
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted && _selectedConversation != null) {
            _loadMessages(_selectedConversation!.id);
          }
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to send message: $e');
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _selectConversation(ChatConversation conversation) {
    if (_selectedConversation != null) {
      _chatService.unsubscribeFromConversation(_selectedConversation!.id);
    }

    setState(() {
      _selectedConversation = conversation;
      _messages.clear();
    });

    _loadMessages(conversation.id);

    // Subscribe to real-time updates
    _chatService.subscribeToConversation(conversation.id, (message) {
      setState(() {
        _messages.add(message);
      });
      _scrollToBottom();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Mobile-friendly layout: show either conversations list or chat area
    return Scaffold(
      appBar: AppBar(
        title: Text(_selectedConversation == null
            ? 'Support Chat'
            : _selectedConversation!.subject),
        leading: _selectedConversation != null
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    _selectedConversation = null;
                  });
                },
              )
            : null,
        actions: [
          if (_selectedConversation == null)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showNewConversationDialog,
              tooltip: 'Start New Conversation',
            ),
          if (_selectedConversation != null && _selectedConversation!.isOpen)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => _closeConversation(),
              tooltip: 'Close Conversation',
            ),
        ],
      ),
      body: _selectedConversation == null
          ? _buildConversationsList()
          : _buildChatArea(),
    );
  }

  Widget _buildConversationsList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_conversations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No conversations yet'),
            Text('Start a new conversation to get help'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _conversations.length,
      itemBuilder: (context, index) {
        final conversation = _conversations[index];
        final isSelected = _selectedConversation?.id == conversation.id;

        return ListTile(
          selected: isSelected,
          leading: CircleAvatar(
            backgroundColor: _getStatusColor(conversation.status),
            child: Icon(
              _getStatusIcon(conversation.status),
              color: Colors.white,
              size: 20,
            ),
          ),
          title: Text(
            conversation.subject,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                conversation.category.replaceAll('_', ' ').toUpperCase(),
                style: const TextStyle(fontSize: 12),
              ),
              Text(
                DateFormat('MMM dd, HH:mm').format(conversation.updatedAt),
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: _getPriorityColor(conversation.priority),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  conversation.priority.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          onTap: () => _selectConversation(conversation),
        );
      },
    );
  }



  Widget _buildChatArea() {
    return Column(
      children: [
        // Chat header with status
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
          ),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: _getStatusColor(_selectedConversation!.status),
                radius: 12,
                child: Icon(
                  _getStatusIcon(_selectedConversation!.status),
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status: ${_selectedConversation!.status.toUpperCase()}',
                      style: TextStyle(
                        color: _getStatusColor(_selectedConversation!.status),
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Priority: ${_selectedConversation!.priority.toUpperCase()}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Messages
        Expanded(
          child: _isLoadingMessages
              ? const Center(child: CircularProgressIndicator())
              : _buildMessagesList(),
        ),
        // Message input
        if (_selectedConversation!.isOpen) _buildMessageInput(),
      ],
    );
  }

  Widget _buildMessagesList() {
    if (_messages.isEmpty) {
      return const Center(
        child: Text('No messages yet. Start the conversation!'),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    // Check if message is from current user
    final currentUser = _supabaseService.currentUser;
    final isFromUser = currentUser != null && message.senderId == currentUser.id;

    return Align(
      alignment: isFromUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isFromUser ? Theme.of(context).primaryColor : Colors.grey[200],
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(18),
            topRight: const Radius.circular(18),
            bottomLeft: Radius.circular(isFromUser ? 18 : 4),
            bottomRight: Radius.circular(isFromUser ? 4 : 18),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isFromUser)
              Text(
                'Support Team',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
            if (!isFromUser) const SizedBox(height: 4),
            Text(
              message.content,
              style: TextStyle(
                color: isFromUser ? Colors.white : Colors.black87,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              DateFormat('HH:mm').format(message.createdAt),
              style: TextStyle(
                fontSize: 12,
                color: isFromUser ? Colors.white70 : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                hintText: 'Type your message...',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: null,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: _sendMessage,
            color: Colors.blue,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.green;
      case 'closed':
        return Colors.grey;
      case 'resolved':
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Icons.chat;
      case 'closed':
        return Icons.check_circle;
      case 'resolved':
        return Icons.done_all;
      default:
        return Icons.help;
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'high':
        return Colors.red;
      case 'urgent':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Future<void> _closeConversation() async {
    final success = await _chatService.closeConversation(_selectedConversation!.id);
    if (success) {
      setState(() {
        _selectedConversation = _selectedConversation!.copyWith(status: 'closed');
      });
      _loadConversations(); // Refresh the list
    }
  }

  Future<void> _createOrderHelpConversation() async {
    try {
      // Check if there's already a conversation for this order
      final existingConversation = _conversations.firstWhere(
        (conv) => conv.orderId == widget.orderId,
        orElse: () => throw StateError('No conversation found'),
      );

      // If found, select it
      _selectConversation(existingConversation);
      return;
    } catch (e) {
      // No existing conversation found, create a new one
    }

    try {
      final conversation = await _chatService.createConversation(
        subject: 'Help with Order #${widget.orderNumber}',
        category: 'order_issue',
        priority: 'medium',
        orderId: widget.orderId,
      );

      if (conversation != null) {
        // Send initial message
        await _chatService.sendMessage(
          conversationId: conversation.id,
          content: 'I need help with my order #${widget.orderNumber}. Order ID: ${widget.orderId}',
        );

        setState(() {
          _conversations.insert(0, conversation);
        });
        _selectConversation(conversation);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to create order help conversation: $e');
    }
  }

  void _showNewConversationDialog() {
    showDialog(
      context: context,
      builder: (context) => NewConversationDialog(
        onConversationCreated: (conversation) {
          setState(() {
            _conversations.insert(0, conversation);
          });
          _selectConversation(conversation);
        },
      ),
    );
  }
}

// Extension to add copyWith method to ChatConversation
extension ChatConversationExtension on ChatConversation {
  ChatConversation copyWith({
    String? status,
    DateTime? updatedAt,
  }) {
    return ChatConversation(
      id: id,
      userId: userId,
      supportAgentId: supportAgentId,
      subject: subject,
      status: status ?? this.status,
      priority: priority,
      category: category,
      orderId: orderId,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      closedAt: closedAt,
      rating: rating,
      feedback: feedback,
    );
  }
}

class NewConversationDialog extends StatefulWidget {
  final Function(ChatConversation) onConversationCreated;

  const NewConversationDialog({
    super.key,
    required this.onConversationCreated,
  });

  @override
  State<NewConversationDialog> createState() => _NewConversationDialogState();
}

class _NewConversationDialogState extends State<NewConversationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _descriptionController = TextEditingController();
  final ChatService _chatService = ChatService();

  String _selectedCategory = 'general_inquiry';
  String _selectedPriority = 'medium';
  bool _isCreating = false;

  final List<Map<String, String>> _categories = [
    {'value': 'general_inquiry', 'label': 'General Inquiry'},
    {'value': 'order_issue', 'label': 'Order Issue'},
    {'value': 'payment_problem', 'label': 'Payment Problem'},
    {'value': 'account_issue', 'label': 'Account Issue'},
    {'value': 'technical_support', 'label': 'Technical Support'},
    {'value': 'feature_request', 'label': 'Feature Request'},
    {'value': 'bug_report', 'label': 'Bug Report'},
    {'value': 'refund_request', 'label': 'Refund Request'},
    {'value': 'other', 'label': 'Other'},
  ];

  final List<Map<String, String>> _priorities = [
    {'value': 'low', 'label': 'Low'},
    {'value': 'medium', 'label': 'Medium'},
    {'value': 'high', 'label': 'High'},
    {'value': 'urgent', 'label': 'Urgent'},
  ];

  @override
  void dispose() {
    _subjectController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _createConversation() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isCreating = true);

    try {
      debugPrint('Starting conversation creation...');
      debugPrint('Subject: ${_subjectController.text.trim()}');
      debugPrint('Category: $_selectedCategory');
      debugPrint('Priority: $_selectedPriority');

      // Test connection first
      debugPrint('Running connection test...');
      final connectionOk = await _chatService.testConnection();
      if (!connectionOk) {
        debugPrint('Connection test failed');
        _showError('Connection test failed. Please check your authentication and try again.');
        return;
      }
      debugPrint('Connection test passed');

      final conversation = await _chatService.createConversation(
        subject: _subjectController.text.trim(),
        category: _selectedCategory,
        priority: _selectedPriority,
      );

      if (conversation != null) {
        debugPrint('Conversation created successfully: ${conversation.id}');

        // Send initial message with description
        final message = await _chatService.sendMessage(
          conversationId: conversation.id,
          content: _descriptionController.text.trim(),
        );

        if (message != null) {
          debugPrint('Initial message sent successfully');
        } else {
          debugPrint('Warning: Failed to send initial message');
        }

        widget.onConversationCreated(conversation);
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        debugPrint('Conversation creation returned null');
        _showError('Failed to create conversation. Please check your internet connection and try again.');
      }
    } catch (e) {
      debugPrint('Exception during conversation creation: $e');
      _showError('Error creating conversation: ${e.toString()}');
    } finally {
      setState(() => _isCreating = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Start New Conversation'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _subjectController,
                decoration: const InputDecoration(
                  labelText: 'Subject',
                  hintText: 'Brief description of your issue',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a subject';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(labelText: 'Category'),
                items: _categories.map((category) {
                  return DropdownMenuItem(
                    value: category['value'],
                    child: Text(category['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() => _selectedCategory = value!);
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedPriority,
                decoration: const InputDecoration(labelText: 'Priority'),
                items: _priorities.map((priority) {
                  return DropdownMenuItem(
                    value: priority['value'],
                    child: Text(priority['label']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() => _selectedPriority = value!);
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Provide more details about your issue',
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please provide a description';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isCreating ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createConversation,
          child: _isCreating
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Start Conversation'),
        ),
      ],
    );
  }
}
