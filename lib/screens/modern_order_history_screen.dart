import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/widgets/glass_container.dart';
import 'package:unieatsappv0/widgets/modern_button.dart';
import 'package:unieatsappv0/widgets/animated_widgets.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/utils/animations.dart';

class ModernOrderHistoryScreen extends StatefulWidget {
  const ModernOrderHistoryScreen({super.key});

  @override
  State<ModernOrderHistoryScreen> createState() => _ModernOrderHistoryScreenState();
}

class _ModernOrderHistoryScreenState extends State<ModernOrderHistoryScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _listController;
  late AnimationController _filterController;
  
  String _selectedFilter = 'All';
  final List<String> _filters = ['All', 'Completed', 'Pending', 'Cancelled'];

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _listController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _filterController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _listController.forward();
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _listController.dispose();
    _filterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppTheme.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Animated background
            _buildAnimatedBackground(),
            
            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Modern App Bar
                  _buildModernAppBar(),
                  
                  // Filter Tabs
                  ModernAnimations.slideInFromBottom(
                    delay: const Duration(milliseconds: 200),
                    child: _buildFilterTabs(),
                  ),
                  
                  // Order History Content
                  Expanded(
                    child: Consumer<OrderHistoryProvider>(
                      builder: (context, orderProvider, child) {
                        return _buildOrderHistoryContent(orderProvider);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Stack(
          children: [
            // Floating orb 1
            Positioned(
              top: 120 + 50 * (0.5 + 0.5 * math.sin(_backgroundController.value * 2 * math.pi)),
              left: 70 + 35 * (0.5 + 0.5 * math.cos(_backgroundController.value * 2 * math.pi)),
              child: Container(
                width: 110,
                height: 110,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.secondaryColor.withOpacity(0.1),
                      AppTheme.secondaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Floating orb 2
            Positioned(
              top: 350 + 40 * (0.5 + 0.5 * math.sin((_backgroundController.value + 0.6) * 2 * math.pi)),
              right: 50 + 30 * (0.5 + 0.5 * math.cos((_backgroundController.value + 0.4) * 2 * math.pi)),
              child: Container(
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.primaryColor.withOpacity(0.1),
                      AppTheme.primaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModernAppBar() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          ModernIconButton(
            icon: Icons.arrow_back,
            onPressed: () => Navigator.pop(context),
            color: AppTheme.textColor,
            backgroundColor: AppTheme.glassColor,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: GlassContainer(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: AppTheme.secondaryGradient,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: AnimatedWidgets.rotatingRefresh(
                      isRefreshing: false,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order History',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textColor,
                        ),
                      ),
                      Text(
                        'Your past orders',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          ModernIconButton(
            icon: Icons.refresh,
            onPressed: _refreshOrders,
            color: AppTheme.textColor,
            backgroundColor: AppTheme.glassColor,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GlassContainer(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: _filters.map((filter) {
            final isSelected = filter == _selectedFilter;
            return Expanded(
              child: AnimatedWidgets.rippleButton(
                onPressed: () => _selectFilter(filter),
                borderRadius: BorderRadius.circular(12),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? const LinearGradient(colors: AppTheme.primaryGradient)
                        : null,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    filter,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildOrderHistoryContent(OrderHistoryProvider orderProvider) {
    if (orderProvider.isLoading) {
      return _buildLoadingState();
    }

    final filteredOrders = _getFilteredOrders(orderProvider.orders);

    if (filteredOrders.isEmpty) {
      return _buildEmptyState();
    }

    return _buildOrdersList(filteredOrders);
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedWidgets.pulsingLoader(
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppTheme.secondaryGradient,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.receipt_long,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
          const SizedBox(height: 24),
          AnimatedWidgets.typewriterText(
            text: 'Loading your orders...',
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return ModernAnimations.fadeIn(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedWidgets.breathingAnimation(
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: AppTheme.secondaryGradient,
                  ),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.receipt_long_outlined,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 32),
            Text(
              _selectedFilter == 'All' ? 'No orders yet' : 'No $_selectedFilter orders',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _selectedFilter == 'All' 
                  ? 'Start ordering to see your history here'
                  : 'No orders found for this filter',
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            if (_selectedFilter == 'All')
              ModernButton(
                text: 'Browse Menu',
                onPressed: () => Navigator.pushReplacementNamed(context, '/dashboard'),
                gradientColors: AppTheme.secondaryGradient,
                elevation: 8,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersList(List<dynamic> orders) {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return AnimatedWidgets.staggeredListItem(
          index: index,
          child: _buildOrderCard(order),
        );
      },
    );
  }

  Widget _buildOrderCard(dynamic order) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: FloatingGlassCard(
        onTap: () => _showOrderDetails(order),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Order Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _getStatusGradient(order.status),
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    order.status.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  _formatDate(order.createdAt),
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Order Details
            Text(
              'Order #${order.id.substring(0, 8)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.textColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              order.cafeteriaName ?? 'Unknown Cafeteria',
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 12),
            
            // Order Total
            Row(
              children: [
                const Text(
                  'Total: ',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                AnimatedWidgets.animatedPrice(
                  value: order.totalAmount ?? 0.0,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const Spacer(),
                if (order.status == 'completed' && (order.rating == null || order.rating == 0))
                  ModernIconButton(
                    icon: Icons.star_outline,
                    onPressed: () => _rateOrder(order),
                    color: AppTheme.warningColor,
                    size: 32,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getStatusGradient(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.secondaryGradient;
      case 'pending':
        return AppTheme.primaryGradient;
      case 'cancelled':
        return [AppTheme.errorColor, AppTheme.errorColor];
      default:
        return AppTheme.accentGradient;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return '${date.day}/${date.month}/${date.year}';
  }

  List<dynamic> _getFilteredOrders(List<dynamic> orders) {
    if (_selectedFilter == 'All') return orders;
    return orders.where((order) => 
        order.status.toLowerCase() == _selectedFilter.toLowerCase()).toList();
  }

  void _selectFilter(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    _filterController.forward().then((_) => _filterController.reverse());
  }

  void _refreshOrders() {
    final orderProvider = Provider.of<OrderHistoryProvider>(context, listen: false);
    orderProvider.loadOrders();
  }

  void _showOrderDetails(dynamic order) {
    Navigator.pushNamed(context, '/order-details', arguments: order);
  }

  void _rateOrder(dynamic order) {
    // Show rating dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          'Rate Order',
          style: TextStyle(color: AppTheme.textColor),
        ),
        content: const Text(
          'How was your experience with this order?',
          style: TextStyle(color: AppTheme.textSecondaryColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Later'),
          ),
          ModernButton(
            text: 'Rate',
            onPressed: () {
              Navigator.pop(context);
              // Navigate to rating screen
            },
            gradientColors: AppTheme.warningColor,
          ),
        ],
      ),
    );
  }
}
