import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/auth_provider.dart';
import 'package:unieatsappv0/widgets/bottom_navigation.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'dart:math';
import 'login_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _backgroundController;
  late Animation<double> _logoScale;
  late Animation<double> _logoRotation;
  late Animation<double> _textFade;
  late Animation<Offset> _textSlide;
  late Animation<double> _backgroundOpacity;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _logoScale = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: Curves.elasticOut,
      ),
    );

    _logoRotation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoController,
        curve: Curves.easeInOut,
      ),
    );

    _textFade = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _textController,
        curve: Curves.easeInOut,
      ),
    );

    _textSlide = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _textController,
        curve: Curves.easeOutCubic,
      ),
    );

    _backgroundOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _backgroundController,
        curve: Curves.easeInOut,
      ),
    );
  }

  void _startAnimations() {
    _backgroundController.forward();

    Future.delayed(const Duration(milliseconds: 500), () {
      _logoController.forward();
    });

    Future.delayed(const Duration(milliseconds: 1000), () {
      _textController.forward();
    });
  }

  Future<void> _initializeApp() async {
    // Initialize auth provider and check for saved credentials
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.init();

    // Wait for animations to complete
    await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      if (authProvider.isAuthenticated) {
        // User is already logged in, go to main screen
        Navigator.pushReplacement(
          context,
          _createSlideTransition(const BottomNavigation(currentIndex: 0)),
        );
      } else {
        // No saved credentials, go to login screen
        Navigator.pushReplacement(
          context,
          _createSlideTransition(const LoginScreen()),
        );
      }
    }
  }

  PageRouteBuilder _createSlideTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 800),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFAFAFA),
              Color(0xFFFFFFFF),
            ],
          ),
        ),
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _logoController,
            _textController,
            _backgroundController,
          ]),
          builder: (context, child) {
            return Stack(
              children: [
                // Floating background elements
                _buildFloatingElements(),

                // Main content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // UniEats Logo with Animation
                      Transform.scale(
                        scale: _logoScale.value,
                        child: Transform.rotate(
                          angle: _logoRotation.value * 0.02, // Very subtle rotation
                          child: _buildUniEatsLogo(),
                        ),
                      ),

                      const SizedBox(height: 40),

                      // Animated text
                      SlideTransition(
                        position: _textSlide,
                        child: FadeTransition(
                          opacity: _textFade,
                          child: Column(
                            children: [
                              ShaderMask(
                                shaderCallback: (bounds) => ModernTheme.primaryGradient.createShader(bounds), // Original orange gradient
                                child: const Text(
                                  'UniEats',
                                  style: TextStyle(
                                    fontSize: 48,
                                    fontWeight: FontWeight.w800,
                                    color: Colors.white,
                                    letterSpacing: -1,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'Your Campus Food Companion',
                                style: ModernTheme.bodyLarge.copyWith(
                                  color: ModernTheme.textSecondary,
                                  fontSize: 16,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 80),

                      // Animated loading indicator
                      FadeTransition(
                        opacity: _textFade,
                        child: _buildModernLoader(),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildFloatingElements() {
    return Stack(
      children: [
        // Floating circles
        Positioned(
          top: 100,
          left: 30,
          child: _buildFloatingCircle(20, ModernTheme.primaryColor.withOpacity(0.1)),
        ),
        Positioned(
          top: 200,
          right: 50,
          child: _buildFloatingCircle(15, ModernTheme.secondaryColor.withOpacity(0.1)),
        ),
        Positioned(
          bottom: 150,
          left: 60,
          child: _buildFloatingCircle(25, ModernTheme.accentColor.withOpacity(0.1)),
        ),
        Positioned(
          bottom: 300,
          right: 30,
          child: _buildFloatingCircle(18, ModernTheme.primaryLight.withOpacity(0.1)),
        ),
      ],
    );
  }

  Widget _buildFloatingCircle(double size, Color color) {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            sin(_backgroundController.value * 2 * pi) * 10,
            cos(_backgroundController.value * 2 * pi) * 10,
          ),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildUniEatsLogo() {
    return Container(
      width: 160,
      height: 160,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFFFFF),
            Color(0xFFF8FAFC),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: ModernTheme.primaryColor.withOpacity(0.1),
            blurRadius: 40,
            spreadRadius: 0,
            offset: const Offset(0, 15),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main circle background with gradient
          Container(
            width: 130,
            height: 130,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: ModernTheme.secondaryGradient, // Original yellow gradient
              boxShadow: [
                BoxShadow(
                  color: ModernTheme.secondaryColor.withOpacity(0.3),
                  blurRadius: 25,
                  spreadRadius: 0,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
          ),

          // Modern food icon with gradient
          ShaderMask(
            shaderCallback: (bounds) => const LinearGradient(
              colors: [Colors.white, Color(0xFFF8FAFC)],
            ).createShader(bounds),
            child: const Icon(
              Icons.restaurant_menu,
              size: 60,
              color: Colors.white,
            ),
          ),

          // Chat bubble indicator
          Positioned(
            bottom: 15,
            right: 15,
            child: Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                gradient: ModernTheme.primaryGradient, // Original orange gradient
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: ModernTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 15,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.chat_bubble_rounded,
                size: 18,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernLoader() {
    return Column(
      children: [
        SizedBox(
          width: 50,
          height: 50,
          child: Stack(
            children: [
              // Outer ring
              SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    ModernTheme.primaryColor.withOpacity(0.3),
                  ),
                  backgroundColor: ModernTheme.primaryColor.withOpacity(0.1),
                ),
              ),
              // Inner ring
              Center(
                child: SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      ModernTheme.secondaryColor,
                    ),
                    backgroundColor: Colors.transparent,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Loading...',
          style: ModernTheme.bodySmall.copyWith(
            color: ModernTheme.textTertiary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}