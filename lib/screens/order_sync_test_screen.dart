import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/order_provider.dart';
import '../providers/order_history_provider.dart';
import '../providers/notification_provider.dart';
import '../services/order_sync_service.dart';

/// Test screen to verify order sync functionality
class OrderSyncTestScreen extends StatefulWidget {
  const OrderSyncTestScreen({super.key});

  @override
  State<OrderSyncTestScreen> createState() => _OrderSyncTestScreenState();
}

class _OrderSyncTestScreenState extends State<OrderSyncTestScreen> {
  bool _isTestingSync = false;
  String _testResults = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Sync Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Order Sync & Notification Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'This test verifies that order status updates from the web app are reflected in the mobile app in real-time.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),

            // Test Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test Instructions:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    const Text('1. Create an order from the mobile app'),
                    const Text('2. Go to the web app and mark the order as "Ready"'),
                    const Text('3. Return to mobile app and check:'),
                    const Text('   • Order status updates automatically'),
                    const Text('   • Notification appears with correct time'),
                    const Text('   • Tapping notification opens order details'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Current Orders
            Consumer<OrderProvider>(
              builder: (context, orderProvider, child) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Orders (${orderProvider.orders.length})',
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        if (orderProvider.orders.isEmpty)
                          const Text('No current orders')
                        else
                          ...orderProvider.orders.map((order) => ListTile(
                            title: Text('Order #${order.orderNumber}'),
                            subtitle: Text('Status: ${order.status}'),
                            trailing: Text('${order.items.length} items'),
                          )),
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),

            // Notifications
            Consumer<NotificationProvider>(
              builder: (context, notificationProvider, child) {
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Recent Notifications (${notificationProvider.notifications.length})',
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        if (notificationProvider.notifications.isEmpty)
                          const Text('No notifications')
                        else
                          ...notificationProvider.notifications.take(3).map((notification) => ListTile(
                            title: Text(notification.title),
                            subtitle: Text(notification.message),
                            trailing: Text(_formatTime(notification.timestamp)),
                            dense: true,
                          )),
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),

            // Test Sync Button
            ElevatedButton(
              onPressed: _isTestingSync ? null : _testOrderSync,
              child: _isTestingSync
                  ? const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Testing Sync...'),
                      ],
                    )
                  : const Text('Test Order Sync Service'),
            ),

            if (_testResults.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Test Results:',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(_testResults),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final localTimestamp = timestamp.isUtc ? timestamp.toLocal() : timestamp;
    final difference = now.difference(localTimestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${difference.inHours}h ago';
    }
  }

  Future<void> _testOrderSync() async {
    setState(() {
      _isTestingSync = true;
      _testResults = '';
    });

    try {
      // Test OrderSyncService initialization
      final orderSyncService = OrderSyncService();
      await orderSyncService.initialize(context);
      
      setState(() {
        _testResults = '✅ OrderSyncService initialized successfully\n';
        _testResults += '✅ Real-time subscriptions active\n';
        _testResults += '✅ Ready to receive order updates from web app\n\n';
        _testResults += 'Now test by updating an order status from the web app!';
      });
    } catch (e) {
      setState(() {
        _testResults = '❌ Error testing order sync: $e';
      });
    } finally {
      setState(() {
        _isTestingSync = false;
      });
    }
  }
}
