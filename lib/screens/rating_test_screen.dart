import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/menu_item_rating_service.dart';
import 'package:unieatsappv0/widgets/menu_item_rating_dialog.dart';
import 'package:unieatsappv0/models/supabase_models.dart';


class RatingTestScreen extends StatefulWidget {
  const RatingTestScreen({super.key});

  @override
  State<RatingTestScreen> createState() => _RatingTestScreenState();
}

class _RatingTestScreenState extends State<RatingTestScreen> {
  final MenuItemRatingService _ratingService = MenuItemRatingService();
  List<SupabaseMenuItemRating> _userRatings = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserRatings();
  }

  Future<void> _loadUserRatings() async {
    setState(() => _isLoading = true);
    try {
      final ratings = await _ratingService.getUserRatings();
      setState(() {
        _userRatings = ratings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      debugPrint('Error loading user ratings: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rating System Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUserRatings,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Menu Item Rating System Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'Test the menu item rating functionality that gets triggered when users complete orders.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),

            // Test rating dialog button
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test Rating Dialog',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Simulate rating a menu item after order completion.',
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => _showTestRatingDialog(),
                      child: const Text('Rate Test Menu Item'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // User ratings list
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Your Ratings',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total ratings: ${_userRatings.length}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Ratings list
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _userRatings.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.star_border, size: 64, color: Colors.grey),
                              SizedBox(height: 16),
                              Text(
                                'No ratings yet',
                                style: TextStyle(fontSize: 18, color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Rate some menu items to see them here!',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          itemCount: _userRatings.length,
                          itemBuilder: (context, index) {
                            final rating = _userRatings[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.amber,
                                  child: Text(
                                    rating.rating.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text('Menu Item: ${rating.menuItemId}'),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (rating.reviewComment != null)
                                      Text(rating.reviewComment!),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Rated on: ${rating.createdAt.toLocal().toString().split('.')[0]}',
                                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                  ],
                                ),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: List.generate(5, (starIndex) {
                                    return Icon(
                                      starIndex < rating.rating ? Icons.star : Icons.star_border,
                                      color: Colors.amber,
                                      size: 16,
                                    );
                                  }),
                                ),
                              ),
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showTestRatingDialog() async {
    // Simulate a test menu item and order
    const testMenuItemId = 'test-menu-item-123';
    const testMenuItemName = 'Test Cheeseburger';
    const testOrderId = 'test-order-456';

    final result = await showMenuItemRatingDialog(
      context: context,
      menuItemId: testMenuItemId,
      menuItemName: testMenuItemName,
      orderId: testOrderId,
    );

    if (result == true) {
      // Rating was submitted successfully, refresh the list
      _loadUserRatings();
    }
  }
}
