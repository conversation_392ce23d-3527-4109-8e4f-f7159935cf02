import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/widgets/glass_container.dart';
import 'package:unieatsappv0/widgets/modern_button.dart';
import 'package:unieatsappv0/widgets/animated_widgets.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/utils/animations.dart';

class ModernDashboardScreen extends StatefulWidget {
  const ModernDashboardScreen({super.key});

  @override
  State<ModernDashboardScreen> createState() => _ModernDashboardScreenState();
}

class _ModernDashboardScreenState extends State<ModernDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _headerController;
  late AnimationController _contentController;
  
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _headerController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _contentController.forward();
    });
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _headerController.dispose();
    _contentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppTheme.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Animated background
            _buildAnimatedBackground(),
            
            // Main content
            SafeArea(
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Modern App Bar
                  _buildModernAppBar(),
                  
                  // Content
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // Quick Actions
                          _buildQuickActions(),
                          const SizedBox(height: 24),
                          
                          // Featured Cafeterias
                          _buildFeaturedCafeterias(),
                          const SizedBox(height: 24),
                          
                          // Popular Items
                          _buildPopularItems(),
                          const SizedBox(height: 24),
                          
                          // Recent Orders
                          _buildRecentOrders(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Stack(
          children: [
            // Floating orb 1
            Positioned(
              top: 100 + 50 * (0.5 + 0.5 * math.sin(_backgroundController.value * 2 * math.pi)),
              left: 60 + 40 * (0.5 + 0.5 * math.cos(_backgroundController.value * 2 * math.pi)),
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.primaryColor.withOpacity(0.1),
                      AppTheme.primaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Floating orb 2
            Positioned(
              top: 300 + 45 * (0.5 + 0.5 * math.sin((_backgroundController.value + 0.6) * 2 * math.pi)),
              right: 40 + 35 * (0.5 + 0.5 * math.cos((_backgroundController.value + 0.4) * 2 * math.pi)),
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.secondaryColor.withOpacity(0.1),
                      AppTheme.secondaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Floating orb 3
            Positioned(
              bottom: 150 + 40 * (0.5 + 0.5 * math.sin((_backgroundController.value + 0.3) * 2 * math.pi)),
              left: 100 + 30 * (0.5 + 0.5 * math.cos((_backgroundController.value + 0.7) * 2 * math.pi)),
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.accentColor.withOpacity(0.1),
                      AppTheme.accentColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: AnimatedBuilder(
        animation: _headerController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, -30 * (1 - _headerController.value)),
            child: Opacity(
              opacity: _headerController.value,
              child: FlexibleSpaceBar(
                background: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Consumer<SimpleAuthProvider>(
                              builder: (context, authProvider, child) {
                                return AnimatedWidgets.typewriterText(
                                  text: 'Hello, ${authProvider.currentUser?.fullName ?? 'Student'}!',
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textColor,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'What would you like to eat today?',
                              style: TextStyle(
                                fontSize: 16,
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      ModernIconButton(
                        icon: Icons.search,
                        onPressed: () => Navigator.pushNamed(context, '/modern-search'),
                        color: AppTheme.textColor,
                        backgroundColor: AppTheme.glassColor,
                      ),
                      const SizedBox(width: 12),
                      ModernIconButton(
                        icon: Icons.notifications_outlined,
                        onPressed: () => Navigator.pushNamed(context, '/modern-notifications'),
                        color: AppTheme.textColor,
                        backgroundColor: AppTheme.glassColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildQuickActions() {
    return AnimatedBuilder(
      animation: _contentController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _contentController.value)),
          child: Opacity(
            opacity: _contentController.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textColor,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionCard(
                        'Browse Menu',
                        Icons.restaurant_menu,
                        AppTheme.primaryGradient,
                        () => Navigator.pushNamed(context, '/cafeterias'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildQuickActionCard(
                        'Order History',
                        Icons.history,
                        AppTheme.secondaryGradient,
                        () => Navigator.pushNamed(context, '/modern-order-history'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActionCard(String title, IconData icon, List<Color> gradient, VoidCallback onTap) {
    return AnimatedWidgets.rippleButton(
      onPressed: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(colors: gradient),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: gradient.first.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          children: [
            AnimatedWidgets.breathingAnimation(
              child: Icon(
                icon,
                size: 32,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedCafeterias() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Featured Cafeterias',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pushNamed(context, '/cafeterias'),
              child: const Text('See All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Consumer<SupabaseProvider>(
          builder: (context, provider, child) {
            final cafeterias = provider.cafeterias.take(3).toList();
            
            return SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: cafeterias.length,
                itemBuilder: (context, index) {
                  final cafeteria = cafeterias[index];
                  return AnimatedWidgets.staggeredListItem(
                    index: index,
                    child: _buildCafeteriaCard(cafeteria),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildCafeteriaCard(dynamic cafeteria) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: FloatingGlassCard(
        width: 200,
        onTap: () => Navigator.pushNamed(
          context,
          '/cafeteria',
          arguments: {
            'id': cafeteria.id,
            'cafeteria': cafeteria,
          },
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: RobustImage(
                imageUrl: cafeteria.imageUrl ?? '',
                width: 60,
                height: 60,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    cafeteria.name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    cafeteria.location ?? 'Campus',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularItems() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Popular Items',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pushNamed(context, '/popular-items'),
              child: const Text('See All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Consumer<SupabaseProvider>(
          builder: (context, provider, child) {
            final items = provider.menuItems.take(5).toList();
            
            return SizedBox(
              height: 140,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index];
                  return AnimatedWidgets.staggeredListItem(
                    index: index,
                    child: _buildPopularItemCard(item),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPopularItemCard(dynamic item) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: FloatingGlassCard(
        width: 120,
        onTap: () => Navigator.pushNamed(
          context,
          '/item-details',
          arguments: item,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: RobustImage(
                imageUrl: item.imageUrl ?? '',
                width: double.infinity,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              item.name,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppTheme.textColor,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const Spacer(),
            AnimatedWidgets.animatedPrice(
              value: item.price,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentOrders() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Orders',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pushNamed(context, '/modern-order-history'),
              child: const Text('See All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GlassContainer(
          padding: const EdgeInsets.all(16),
          child: const Center(
            child: Text(
              'No recent orders',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
