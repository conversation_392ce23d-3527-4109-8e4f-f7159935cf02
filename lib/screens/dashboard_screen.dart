import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/widgets/cafeteria_card.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/screens/order_tracking_screen.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';

import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/providers/cafeteria_ratings_provider.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  // Cache for favorites to prevent reloading
  List<SupabaseMenuItem>? _cachedFavorites;
  bool _favoritesLoaded = false;

  // Animation controllers for modern interactions
  late AnimationController _headerAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _headerSlideAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _headerFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOut,
    ));

    _headerSlideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _headerAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _cardAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Get user info from SimpleAuthProvider and data from SupabaseProvider
    final supabaseProvider = Provider.of<SupabaseProvider>(context);
    final authProvider = Provider.of<SimpleAuthProvider>(context);
    final userName = authProvider.currentUser?.fullName ??
        supabaseProvider.currentUser?.fullName ??
        'there';

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildModernAppBar(context),
      body: RefreshIndicator(
        color: ModernTheme.primaryColor,
        onRefresh: () async {
          // Get providers before async operations
          final supabaseProvider =
              Provider.of<SupabaseProvider>(context, listen: false);
          final notificationProvider =
              Provider.of<NotificationProvider>(context, listen: false);
          final ratingsProvider =
              Provider.of<CafeteriaRatingsProvider>(context, listen: false);

          // Refresh data from Supabase
          await supabaseProvider.loadCafeterias();

          // Refresh ratings for all cafeterias
          for (final cafeteria in supabaseProvider.cafeterias) {
            ratingsProvider.fetchRatingsForCafeteria(cafeteria.id);
          }

          // Also refresh notifications
          await notificationProvider.refresh();

          debugPrint(
              'Refreshed cafeterias: ${supabaseProvider.cafeterias.length}');
        },
        child: ListView(
          padding: const EdgeInsets.all(ModernTheme.spaceM),
          children: [
            // Modern Welcome Section
            _buildWelcomeSection(userName),
            const SizedBox(height: ModernTheme.spaceXL),

            // Modern Search Bar
            _buildModernSearchBar(context),
            const SizedBox(height: ModernTheme.spaceXL),

            // Cafeterias section
            _buildSectionHeader(
              context,
              title: 'Cafeterias',
              onSeeAll: () {
                Navigator.of(context).pushNamed('/cafeterias');
              },
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 280, // Increased height to accommodate the card content
              child: Consumer<SupabaseProvider>(
                builder: (context, supabaseProvider, _) {
                  final cafeterias = supabaseProvider.cafeterias;

                  // Force reload cafeterias if empty
                  if (cafeterias.isEmpty) {
                    debugPrint(
                        'Dashboard: No cafeterias available, forcing reload');

                    // Get ratings provider before async operation
                    final ratingsProvider = Provider.of<CafeteriaRatingsProvider>(context, listen: false);

                    // Use Future.microtask to avoid setState during build
                    Future.microtask(() async {
                      await supabaseProvider.loadCafeterias();

                      // Also load ratings after cafeterias are loaded
                      for (final cafeteria in supabaseProvider.cafeterias) {
                        ratingsProvider.fetchRatingsForCafeteria(cafeteria.id);
                      }
                    });
                    return _buildCafeteriaLoadingSkeleton();
                  }

                  debugPrint(
                      'Dashboard: Displaying ${cafeterias.length} cafeterias');

                  // Debug each cafeteria
                  for (var cafeteria in cafeterias) {
                    debugPrint(
                        'Dashboard cafeteria: ${cafeteria.name}, ID: ${cafeteria.id}, isActive: ${cafeteria.isActive}');
                  }

                  return ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: cafeterias.length,
                    itemBuilder: (ctx, i) {
                      final cafeteria = cafeterias[i];
                      return GestureDetector(
                        onTap: () async {
                          // First select this cafeteria in the provider
                          // This ensures the data is loaded before navigation
                          await supabaseProvider.selectCafeteria(cafeteria.id);

                          // Then navigate to cafeteria detail
                          if (context.mounted) {
                            Navigator.of(context).pushNamed(
                              '/cafeteria',
                              arguments: {
                                'id': cafeteria.id,
                                'cafeteria': cafeteria,
                              },
                            );
                          }
                        },
                        child: SizedBox(
                          width:
                              250, // Fixed width that matches the card's internal width
                          child: CafeteriaCard(
                            cafeteria: cafeteria,
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 24),

            // Popular items section
            _buildSectionHeader(
              context,
              title: 'Reorder',
              onSeeAll: () {
                Navigator.of(context).pushNamed('/popular_items');
              },
            ),
            const SizedBox(height: 12),
            _buildFavoritesSection(context),

            // Current Orders Section
            _buildCurrentOrdersSection(context),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesSection(BuildContext context) {
    return Consumer<OrderHistoryProvider>(
      builder: (context, orderHistoryProvider, child) {
        // Show loading if order history is still loading
        if (orderHistoryProvider.isLoading) {
          return _buildFavoritesLoadingSkeleton();
        }

        // If we have cached favorites and they're loaded, use them
        if (_favoritesLoaded && _cachedFavorites != null) {
          debugPrint('🎯 Using cached favorites: ${_cachedFavorites!.length} items');

          if (_cachedFavorites!.isEmpty) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('No favorite items yet. Order something to see your favorites here!'),
              ),
            );
          }

          return ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: _cachedFavorites!.length,
            itemBuilder: (ctx, i) {
              return _buildPopularItemCard(context, _cachedFavorites![i]);
            },
          );
        }

        // Load favorites for the first time
        return FutureBuilder<List<SupabaseMenuItem>>(
          future: _loadAndCacheFavorites(context, orderHistoryProvider.orders),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            if (snapshot.hasError) {
              debugPrint('Error loading menu items for favorites: ${snapshot.error}');
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text('Error loading favorites'),
                ),
              );
            }

            final userFavoriteItems = snapshot.data ?? [];

            if (userFavoriteItems.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text('No favorite items yet. Order something to see your favorites here!'),
                ),
              );
            }

            return ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: userFavoriteItems.length,
              itemBuilder: (ctx, i) {
                return _buildPopularItemCard(context, userFavoriteItems[i]);
              },
            );
          },
        );
      },
    );
  }

  Future<List<SupabaseMenuItem>> _loadAndCacheFavorites(BuildContext context, List<Order> userOrders) async {
    try {
      debugPrint('🔄 Loading and caching favorites...');

      // Get current user for filtering
      final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
      final currentUserId = authProvider.currentUser?.id;
      debugPrint('🔍 Current user ID: $currentUserId');
      debugPrint('📋 Total orders from provider: ${userOrders.length}');

      // Filter orders to only include current user's orders
      final currentUserOrders = userOrders.where((order) => order.userId == currentUserId).toList();
      debugPrint('📋 Current user orders: ${currentUserOrders.length}');

      // Debug: Show user IDs of first few orders
      for (final order in userOrders.take(5)) {
        debugPrint('   📦 Order ${order.orderNumber}: userId=${order.userId}, current=${currentUserId}');
      }

      // Load all menu items
      final allMenuItems = await _loadAllMenuItems(context);

      // Get user's previously ordered items (using filtered orders)
      final userFavoriteItems = _getUserPreviouslyOrderedItemsSync(currentUserOrders, allMenuItems);

      // Cache the results
      setState(() {
        _cachedFavorites = userFavoriteItems;
        _favoritesLoaded = true;
      });

      debugPrint('✅ Cached ${userFavoriteItems.length} favorite items');
      return userFavoriteItems;
    } catch (e) {
      debugPrint('❌ Error loading and caching favorites: $e');
      return [];
    }
  }

  Future<List<SupabaseMenuItem>> _loadAllMenuItems(BuildContext context) async {
    try {
      final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
      final List<SupabaseMenuItem> allMenuItems = [];

      debugPrint('🔍 Loading menu items from ${supabaseProvider.cafeterias.length} cafeterias');

      for (final cafeteria in supabaseProvider.cafeterias) {
        try {
          // Use the menu service from the provider
          final menuService = supabaseProvider.menuService;
          final cafeteriaMenuItems = await menuService.getMenuItemsByCafeteria(cafeteria.id);
          allMenuItems.addAll(cafeteriaMenuItems);
          debugPrint('   ✅ Loaded ${cafeteriaMenuItems.length} items from ${cafeteria.name}');
        } catch (e) {
          debugPrint('   ❌ Error loading menu items for cafeteria ${cafeteria.name}: $e');
        }
      }

      debugPrint('📋 Total menu items loaded: ${allMenuItems.length}');
      return allMenuItems;
    } catch (e) {
      debugPrint('❌ Error loading all menu items: $e');
      return [];
    }
  }

  Widget _buildSectionHeader(
    BuildContext context, {
    required String title,
    required VoidCallback onSeeAll,
  }) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: ModernTheme.headingMedium,
        ),
        TextButton(
          onPressed: onSeeAll,
          child: const Text('See All'),
        ),
      ],
    );
  }

  Widget _buildPopularItemCard(BuildContext context, SupabaseMenuItem item) {
    final theme = Theme.of(context);
    return InkWell(
      onTap: () {
        Navigator.of(context).pushNamed(
          '/item_details',
          arguments: item.toMenuItem(), // Convert to MenuItem
        );
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildMenuItemImage(item, theme),
              ),
              const SizedBox(width: 12),
              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.description ?? 'No description available',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodySmall?.color,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${item.price.toStringAsFixed(2)} EGP',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.primaryColor,
                          ),
                        ),
                        // We don't have ratings in Supabase yet, so we'll just show availability
                        Row(
                          children: [
                            Icon(
                              item.isAvailable
                                  ? Icons.check_circle
                                  : Icons.cancel,
                              size: 16,
                              color:
                                  item.isAvailable ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              item.isAvailable ? 'Available' : 'Unavailable',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentOrdersSection(BuildContext context) {
    final theme = Theme.of(context);
    final orders = Provider.of<OrderProvider>(context).orders;
    final currentOrders = orders
        .where(
            (order) => order.status != 'Completed' && order.status != 'Missed')
        .toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.only(bottom: 24),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.local_shipping, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text('Current Orders',
                    style: theme.textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 12),
            if (currentOrders.isEmpty)
              Text(
                  'No current orders. When you place an order, it will appear here!',
                  style: theme.textTheme.bodyMedium)
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: currentOrders.length,
                separatorBuilder: (_, __) => const Divider(),
                itemBuilder: (context, index) {
                  final order = currentOrders[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: Icon(Icons.receipt, color: theme.primaryColor),
                    title: Text('Order #${order.orderNumber}'),
                    subtitle:
                        Text('Pickup: ${order.pickupTime}  •  ${order.status}'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => OrderTrackingScreen(order: order),
                        ),
                      );
                    },
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  /// Get menu items that the user has previously ordered (synchronous version)
  List<SupabaseMenuItem> _getUserPreviouslyOrderedItemsSync(
      List<Order> userOrders, List<SupabaseMenuItem> allMenuItems) {
    try {
      debugPrint('🔍 Getting user\'s previously ordered items (sync)...');
      debugPrint('📋 Found ${userOrders.length} orders in history');
      debugPrint('📋 Available menu items: ${allMenuItems.length}');

      // Debug: Show first few orders
      for (final order in userOrders.take(3)) {
        debugPrint('   📦 Order ${order.orderNumber}: ${order.items.length} items');
        for (final item in order.items.take(2)) {
          debugPrint('      - Item ID: ${item.id}, Name: ${item.name}');
        }
      }

      // Extract all menu item IDs from user's order history
      final Set<String> orderedItemIds = {};
      for (final order in userOrders) {
        for (final item in order.items) {
          orderedItemIds.add(item.id);
          debugPrint('   🔗 Added item ID to set: ${item.id}');
        }
      }

      debugPrint('🍽️ User has ordered ${orderedItemIds.length} unique items');
      debugPrint('🔗 Ordered item IDs: ${orderedItemIds.take(5).toList()}');

      // Filter menu items to only include those the user has ordered
      final List<SupabaseMenuItem> userFavoriteItems = allMenuItems
          .where((menuItem) => orderedItemIds.contains(menuItem.id))
          .toList();

      // Sort by availability first, then by name
      userFavoriteItems.sort((a, b) {
        if (a.isAvailable && !b.isAvailable) return -1;
        if (!a.isAvailable && b.isAvailable) return 1;
        return a.name.compareTo(b.name);
      });

      // Limit to top 5 items
      final topUserFavorites = userFavoriteItems.take(5).toList();

      debugPrint('✅ Returning ${topUserFavorites.length} user favorite items');
      for (final item in topUserFavorites) {
        debugPrint('   - ${item.name} (Available: ${item.isAvailable})');
      }

      return topUserFavorites;
    } catch (e) {
      debugPrint('❌ Error getting user previously ordered items: $e');
      return [];
    }
  }

  /// Build menu item image widget with support for different image types
  Widget _buildMenuItemImage(SupabaseMenuItem item, ThemeData theme) {
    return RobustImage(
      imageUrl: item.imageUrl,
      width: 80,
      height: 80,
      fit: BoxFit.cover,
      timeout: const Duration(seconds: 5), // Shorter timeout for better UX
    );
  }

  Widget _buildFavoritesLoadingSkeleton() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: List.generate(3, (index) =>
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[300],
            ),
            child: TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 1000),
              tween: Tween(begin: 0.3, end: 1.0),
              builder: (context, value, child) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 500),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [
                        Colors.grey[300]!.withOpacity(value),
                        Colors.grey[100]!.withOpacity(value),
                        Colors.grey[300]!.withOpacity(value),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCafeteriaLoadingSkeleton() {
    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: 250,
            margin: const EdgeInsets.only(right: 16),
            child: TweenAnimationBuilder<double>(
              duration: Duration(milliseconds: 1000 + (index * 200)),
              tween: Tween(begin: 0.3, end: 1.0),
              builder: (context, value, child) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 500),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      colors: [
                        Colors.grey[300]!.withOpacity(value),
                        Colors.grey[100]!.withOpacity(value),
                        Colors.grey[300]!.withOpacity(value),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  // Modern UI Components
  PreferredSizeWidget _buildModernAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: ShaderMask(
        shaderCallback: (bounds) => ModernTheme.primaryGradient.createShader(bounds),
        child: const Text(
          'UniEats',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w800,
            color: Colors.white,
          ),
        ),
      ),
      automaticallyImplyLeading: false,
      actions: [
        Consumer<NotificationProvider>(
          builder: (context, notificationProvider, child) {
            final unreadCount = notificationProvider.unreadCount;
            return Container(
              margin: const EdgeInsets.only(right: ModernTheme.spaceM),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: ModernTheme.softShadow,
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.notifications_outlined,
                        color: ModernTheme.textPrimary,
                      ),
                      onPressed: () {
                        Navigator.of(context).pushNamed('/notifications');
                      },
                    ),
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          gradient: ModernTheme.primaryGradient,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: ModernTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 20,
                          minHeight: 20,
                        ),
                        child: Text(
                          unreadCount > 9 ? '9+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildWelcomeSection(String userName) {
    return FadeTransition(
      opacity: _headerFadeAnimation,
      child: SlideTransition(
        position: _headerSlideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Good morning, $userName! 👋',
              style: ModernTheme.headingLarge.copyWith(
                color: ModernTheme.textPrimary,
              ),
            ),
            const SizedBox(height: ModernTheme.spaceS),
            Text(
              'What would you like to eat today?',
              style: ModernTheme.bodyLarge.copyWith(
                color: ModernTheme.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernSearchBar(BuildContext context) {
    return FadeTransition(
      opacity: _headerFadeAnimation,
      child: GestureDetector(
        onTap: () {
          Navigator.pushNamed(context, '/search');
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: ModernTheme.spaceM,
            vertical: ModernTheme.spaceM,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
            boxShadow: ModernTheme.mediumShadow,
            border: Border.all(
              color: ModernTheme.primaryColor.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: ModernTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                ),
                child: const Icon(
                  Icons.search,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: ModernTheme.spaceM),
              Expanded(
                child: Text(
                  'Search for food, cafeterias...',
                  style: ModernTheme.bodyMedium.copyWith(
                    color: ModernTheme.textTertiary,
                  ),
                ),
              ),
              Icon(
                Icons.tune,
                color: ModernTheme.textTertiary,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
