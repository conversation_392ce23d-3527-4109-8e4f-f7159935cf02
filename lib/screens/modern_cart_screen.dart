import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'package:unieatsappv0/providers/modern_cart_provider.dart';
import 'package:unieatsappv0/widgets/glass_container.dart';
import 'package:unieatsappv0/widgets/modern_button.dart';
import 'package:unieatsappv0/widgets/animated_widgets.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/utils/animations.dart';

class ModernCartScreen extends StatefulWidget {
  const ModernCartScreen({super.key});

  @override
  State<ModernCartScreen> createState() => _ModernCartScreenState();
}

class _ModernCartScreenState extends State<ModernCartScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _listController;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _listController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _listController.forward();
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppTheme.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Animated background
            _buildAnimatedBackground(),
            
            // Main content
            SafeArea(
              child: Consumer<ModernCartProvider>(
                builder: (context, cartProvider, child) {
                  return Column(
                    children: [
                      // Modern App Bar
                      _buildModernAppBar(cartProvider),
                      
                      // Cart Content
                      Expanded(
                        child: cartProvider.items.isEmpty
                            ? _buildEmptyCart()
                            : _buildCartItems(cartProvider),
                      ),
                      
                      // Bottom Summary (if cart has items)
                      if (cartProvider.items.isNotEmpty)
                        _buildBottomSummary(cartProvider),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Stack(
          children: [
            // Floating orb 1
            Positioned(
              top: 150 + 60 * (0.5 + 0.5 * math.sin(_backgroundController.value * 2 * math.pi)),
              left: 80 + 40 * (0.5 + 0.5 * math.cos(_backgroundController.value * 2 * math.pi)),
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.primaryColor.withOpacity(0.1),
                      AppTheme.primaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Floating orb 2
            Positioned(
              top: 400 + 50 * (0.5 + 0.5 * math.sin((_backgroundController.value + 0.5) * 2 * math.pi)),
              right: 50 + 30 * (0.5 + 0.5 * math.cos((_backgroundController.value + 0.3) * 2 * math.pi)),
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.secondaryColor.withOpacity(0.1),
                      AppTheme.secondaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModernAppBar(ModernCartProvider cartProvider) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          ModernIconButton(
            icon: Icons.arrow_back,
            onPressed: () => Navigator.pop(context),
            color: AppTheme.textColor,
            backgroundColor: AppTheme.glassColor,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: GlassContainer(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: AppTheme.primaryGradient,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: AnimatedWidgets.bouncingCartIcon(
                      icon: Icons.shopping_cart,
                      color: Colors.white,
                      size: 20,
                      isAnimating: cartProvider.isAnimating,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Your Cart',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textColor,
                        ),
                      ),
                      AnimatedWidgets.animatedCounter(
                        value: cartProvider.itemCount,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          if (cartProvider.items.isNotEmpty)
            ModernIconButton(
              icon: Icons.delete_outline,
              onPressed: () => _showClearCartDialog(cartProvider),
              color: AppTheme.errorColor,
              backgroundColor: AppTheme.glassColor,
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyCart() {
    return ModernAnimations.fadeIn(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedWidgets.breathingAnimation(
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: AppTheme.primaryGradient,
                  ),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.shopping_cart_outlined,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 32),
            const Text(
              'Your cart is empty',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Add some delicious items to get started',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ModernButton(
              text: 'Browse Menu',
              onPressed: () => Navigator.pushReplacementNamed(context, '/dashboard'),
              gradientColors: AppTheme.primaryGradient,
              elevation: 8,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartItems(ModernCartProvider cartProvider) {
    return AnimatedList(
      key: GlobalKey<AnimatedListState>(),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemBuilder: (context, index, animation) {
        if (index >= cartProvider.items.length) return const SizedBox.shrink();
        
        final item = cartProvider.items[index];
        return AnimatedWidgets.staggeredListItem(
          index: index,
          child: _buildCartItemCard(item, cartProvider),
        );
      },
    );
  }

  Widget _buildCartItemCard(dynamic item, ModernCartProvider cartProvider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: FloatingGlassCard(
        child: Row(
          children: [
            // Item Image
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: RobustImage(
                imageUrl: item.image ?? '',
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 16),
            
            // Item Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.cafeteriaName,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      AnimatedWidgets.animatedPrice(
                        value: item.price,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const Spacer(),
                      _buildQuantityControls(item, cartProvider),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityControls(dynamic item, ModernCartProvider cartProvider) {
    return Row(
      children: [
        AnimatedWidgets.rippleButton(
          onPressed: () => cartProvider.updateQuantity(
            item.menuItem.id,
            item.quantity - 1,
          ),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.remove,
              size: 16,
              color: AppTheme.textColor,
            ),
          ),
        ),
        const SizedBox(width: 12),
        AnimatedWidgets.animatedCounter(
          value: item.quantity,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textColor,
          ),
        ),
        const SizedBox(width: 12),
        AnimatedWidgets.rippleButton(
          onPressed: () => cartProvider.updateQuantity(
            item.menuItem.id,
            item.quantity + 1,
          ),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppTheme.primaryGradient,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.add,
              size: 16,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomSummary(ModernCartProvider cartProvider) {
    return GlassContainer(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Subtotal',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              AnimatedWidgets.animatedPrice(
                value: cartProvider.subtotal,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppTheme.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Service Fee',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              AnimatedWidgets.animatedPrice(
                value: cartProvider.serviceFee,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppTheme.textColor,
                ),
              ),
            ],
          ),
          const Divider(height: 24, color: AppTheme.dividerColor),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textColor,
                ),
              ),
              AnimatedWidgets.animatedPrice(
                value: cartProvider.total,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ModernButton(
            text: 'Proceed to Checkout',
            onPressed: () => _proceedToCheckout(cartProvider),
            width: double.infinity,
            height: 56,
            gradientColors: AppTheme.primaryGradient,
            elevation: 8,
            isLoading: cartProvider.isLoading,
          ),
        ],
      ),
    );
  }

  void _showClearCartDialog(ModernCartProvider cartProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: const Text(
          'Clear Cart',
          style: TextStyle(color: AppTheme.textColor),
        ),
        content: const Text(
          'Are you sure you want to remove all items from your cart?',
          style: TextStyle(color: AppTheme.textSecondaryColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ModernButton(
            text: 'Clear',
            onPressed: () {
              Navigator.pop(context);
              cartProvider.clearCart();
            },
            gradientColors: [AppTheme.errorColor, AppTheme.errorColor],
          ),
        ],
      ),
    );
  }

  void _proceedToCheckout(ModernCartProvider cartProvider) {
    // Navigate to checkout screen
    Navigator.pushNamed(context, '/checkout');
  }
}
