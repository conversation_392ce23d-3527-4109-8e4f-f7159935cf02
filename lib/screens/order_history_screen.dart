import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';

class OrderHistoryScreen extends StatefulWidget {
  const OrderHistoryScreen({super.key});

  @override
  State<OrderHistoryScreen> createState() => _OrderHistoryScreenState();
}

class _OrderHistoryScreenState extends State<OrderHistoryScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    final orderHistoryProvider =
        Provider.of<OrderHistoryProvider>(context, listen: false);
    await orderHistoryProvider.loadOrders();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final orderHistoryProvider = Provider.of<OrderHistoryProvider>(context);
    final orders = orderHistoryProvider.orders;

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildModernAppBar(orders.isNotEmpty, orderHistoryProvider),
      body: _isLoading
          ? _buildLoadingState()
          : orders.isEmpty
              ? _buildEmptyState()
              : _buildOrdersList(orders),
    );
  }

  PreferredSizeWidget _buildModernAppBar(bool hasOrders, OrderHistoryProvider provider) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: ModernTheme.primaryGradient,
              borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
            ),
            child: const Icon(
              Icons.history,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: ModernTheme.spaceM),
          Text(
            'Order History',
            style: ModernTheme.headingMedium,
          ),
        ],
      ),
      actions: [
        if (hasOrders)
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.delete_sweep,
                color: Colors.red,
                size: 20,
              ),
            ),
            onPressed: () => _showClearHistoryDialog(provider),
          ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: ModernTheme.primaryGradient,
              borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
            ),
            child: const CircularProgressIndicator(
              color: Colors.white,
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: ModernTheme.spaceL),
          Text(
            'Loading your orders...',
            style: ModernTheme.bodyLarge.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(ModernTheme.spaceL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: ModernTheme.primaryGradient,
                borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
              ),
              child: const Icon(
                Icons.receipt_long_outlined,
                size: 64,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: ModernTheme.spaceXL),
            Text(
              'No Orders Yet',
              style: ModernTheme.headingLarge,
            ),
            const SizedBox(height: ModernTheme.spaceM),
            Text(
              'Your order history will appear here once you place your first order',
              style: ModernTheme.bodyLarge.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: ModernTheme.spaceXL),
            ModernButton(
              text: 'Browse Menu',
              onPressed: () {
                Navigator.of(context).pushReplacementNamed('/dashboard');
              },
              icon: Icons.restaurant_menu,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersList(List orders) {
    return ListView.builder(
      padding: const EdgeInsets.all(ModernTheme.spaceM),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildOrderCard(order);
      },
    );
  }

  Widget _buildOrderCard(dynamic order) {
    return Container(
      margin: const EdgeInsets.only(bottom: ModernTheme.spaceM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          Navigator.of(context).pushNamed(
            '/order_tracking',
            arguments: order,
          );
        },
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(ModernTheme.spaceL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOrderHeader(order),
              const SizedBox(height: ModernTheme.spaceM),
              _buildOrderStatus(order),
              const SizedBox(height: ModernTheme.spaceM),
              _buildOrderTotal(order),
              const SizedBox(height: ModernTheme.spaceM),
              _buildOrderItems(order),
              if (order.rating != null && order.status == 'Completed')
                _buildOrderRating(order),
              if (order.comment != null && order.comment!.isNotEmpty && order.status == 'Completed')
                _buildOrderComment(order),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderHeader(dynamic order) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: ModernTheme.primaryGradient,
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.receipt,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: ModernTheme.spaceS),
            Text(
              'Order #${order.orderNumber}',
              style: ModernTheme.headingSmall,
            ),
          ],
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 6,
          ),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
          ),
          child: Text(
            DateFormat('MMM dd, yyyy').format(order.dateTime),
            style: ModernTheme.bodySmall.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderStatus(dynamic order) {
    final statusColor = _getStatusColor(order.status);
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
        border: Border.all(
          color: statusColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            order.status,
            style: ModernTheme.bodyMedium.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderTotal(dynamic order) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ModernTheme.accentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
      ),
      child: Row(
        children: [
          Icon(
            Icons.payments,
            color: ModernTheme.accentColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Total: ',
            style: ModernTheme.bodyMedium,
          ),
          Text(
            '${order.totalPrice.toStringAsFixed(2)} EGP',
            style: ModernTheme.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: ModernTheme.accentColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItems(dynamic order) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.restaurant,
                color: Colors.grey.shade600,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Items (${order.items.length})',
                style: ModernTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...order.items.map<Widget>((item) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: ModernTheme.accentColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${item.name} x${item.quantity}',
                      style: ModernTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildOrderRating(dynamic order) {
    return Container(
      margin: const EdgeInsets.only(top: ModernTheme.spaceM),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.1),
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.star,
            color: Colors.amber,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Your Rating: ',
            style: ModernTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          ...List.generate(5, (index) {
            return Icon(
              index < (order.rating ?? 0) ? Icons.star : Icons.star_border,
              color: index < (order.rating ?? 0) ? Colors.amber : Colors.grey,
              size: 16,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildOrderComment(dynamic order) {
    return Container(
      margin: const EdgeInsets.only(top: ModernTheme.spaceM),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.comment,
                color: Colors.blue,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Your Comment:',
                style: ModernTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            order.comment!,
            style: ModernTheme.bodyMedium.copyWith(
              fontStyle: FontStyle.italic,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  void _showClearHistoryDialog(OrderHistoryProvider provider) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.warning,
                color: Colors.red,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text('Clear Order History'),
          ],
        ),
        content: const Text(
          'Are you sure you want to clear your order history? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              provider.clearOrderHistory();
              Navigator.of(ctx).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      const Text('Order history cleared'),
                    ],
                  ),
                  backgroundColor: ModernTheme.accentColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  margin: const EdgeInsets.all(16),
                ),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('CLEAR'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'ready for pickup':
        return Colors.blue;
      case 'preparing':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
