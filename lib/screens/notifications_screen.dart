import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:intl/intl.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final notificationProvider = Provider.of<NotificationProvider>(context);
    final notifications = notificationProvider.notifications;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          if (notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.done_all),
              tooltip: 'Mark all as read',
              onPressed: () {
                notificationProvider.markAllAsRead();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('All notifications marked as read')),
                );
              },
            ),
          if (notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              tooltip: 'Clear all',
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (ctx) => AlertDialog(
                    title: const Text('Clear All Notifications'),
                    content: const Text(
                        'Are you sure you want to clear all notifications?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(ctx).pop(),
                        child: const Text('CANCEL'),
                      ),
                      TextButton(
                        onPressed: () {
                          notificationProvider.clearAll();
                          Navigator.of(ctx).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('All notifications cleared')),
                          );
                        },
                        child: const Text('CLEAR'),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
      body: notifications.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.notifications_none,
                    size: 80,
                    color: theme.colorScheme.onSurface
                        .withAlpha(102), // ~0.4 opacity
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Notifications',
                    style: theme.textTheme.headlineMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You don\'t have any notifications yet',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurface
                          .withAlpha(153), // ~0.6 opacity
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return Dismissible(
                  key: Key(notification.id),
                  background: Container(
                    color: theme.colorScheme.error,
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(right: 20),
                    child: const Icon(
                      Icons.delete,
                      color: Colors.white,
                    ),
                  ),
                  direction: DismissDirection.endToStart,
                  onDismissed: (direction) {
                    notificationProvider.removeNotification(notification.id);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Notification removed')),
                    );
                  },
                  child: Card(
                    margin:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                      leading: _buildNotificationIcon(notification, theme),
                      title: Text(
                        notification.title,
                        style: TextStyle(
                          fontWeight: notification.isRead
                              ? FontWeight.normal
                              : FontWeight.bold,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(notification.message),
                          const SizedBox(height: 4),
                          Text(
                            _formatTimestamp(notification.timestamp),
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                      isThreeLine: true,
                      onTap: () {
                        if (!notification.isRead) {
                          notificationProvider.markAsRead(notification.id);
                        }

                        // Navigate to order history for order-related notifications
                        if (notification.orderId != null && notification.orderId!.isNotEmpty) {
                          debugPrint('🔗 Navigating to order history for order: ${notification.orderId}');
                          Navigator.of(context).pushNamed('/order_history');
                        } else {
                          debugPrint('⚠️ No order ID found for notification: ${notification.id}');
                          Navigator.of(context).pushNamed('/order_history');
                        }

                        // Mark notification as read for all types
                        notificationProvider.markAsRead(notification.id);
                      },
                    ),
                  ),
                );
              },
            ),
    );
  }

  Widget _buildNotificationIcon(AppNotification notification, ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case 'order':
        iconData = Icons.receipt;
        iconColor = theme.colorScheme.primary;
        break;
      case 'promo':
        iconData = Icons.local_offer;
        iconColor = theme.colorScheme.secondary;
        break;
      case 'system':
      default:
        iconData = Icons.info;
        iconColor = theme.colorScheme.tertiary;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withAlpha(25), // ~0.1 opacity
      child: Icon(
        iconData,
        color: iconColor,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();

    // Convert both to local time for accurate comparison
    final localNow = now.toLocal();
    final localTimestamp = timestamp.toLocal();

    // Calculate the difference in local time
    final difference = localNow.difference(localTimestamp);

    debugPrint('🕐 Timestamp formatting:');
    debugPrint('   Original timestamp: $timestamp');
    debugPrint('   Local timestamp: $localTimestamp');
    debugPrint('   Local now: $localNow');
    debugPrint('   Difference: ${difference.inMinutes} minutes');

    // Handle negative differences (future timestamps)
    if (difference.isNegative) {
      return 'Just now';
    }

    if (difference.inDays > 0) {
      // For dates more than a day old, show the actual date
      final localTimestamp = timestamp.toLocal();
      return DateFormat('MMM d, h:mm a').format(localTimestamp);
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }
}
