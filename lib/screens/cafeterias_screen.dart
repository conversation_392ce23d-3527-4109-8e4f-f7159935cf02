import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/widgets/cafeteria_card.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';

class CafeteriasScreen extends StatelessWidget {
  const CafeteriasScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample data - in a real app, this would come from a database or API
    // final List<Cafeteria> cafeterias = [...];

    return Scaffold(
      appBar: AppBar(
        title: const Text('All Cafeterias'),
      ),
      body: Consumer<SupabaseProvider>(
        builder: (context, supabaseProvider, _) {
          final cafeterias = supabaseProvider.cafeterias;

          if (cafeterias.isEmpty) {
            return const Center(
              child: Text('No cafeterias available'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: cafeterias.length,
            itemBuilder: (context, index) {
              final cafeteria = cafeterias[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: GestureDetector(
                  onTap: () {
                    // Navigate to cafeteria detail
                    Navigator.of(context).pushNamed(
                      '/cafeteria',
                      arguments: {
                        'id': cafeteria.id,
                        'cafeteria': cafeteria,
                      },
                    );

                    // Also select this cafeteria in the provider
                    supabaseProvider.selectCafeteria(cafeteria.id);
                  },
                  child: CafeteriaCard(
                    cafeteria: cafeteria,
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
