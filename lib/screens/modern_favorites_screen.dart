import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/widgets/glass_container.dart';
import 'package:unieatsappv0/widgets/modern_button.dart';
import 'package:unieatsappv0/widgets/animated_widgets.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/utils/animations.dart';

class ModernFavoritesScreen extends StatefulWidget {
  const ModernFavoritesScreen({super.key});

  @override
  State<ModernFavoritesScreen> createState() => _ModernFavoritesScreenState();
}

class _ModernFavoritesScreenState extends State<ModernFavoritesScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _listController;
  late AnimationController _searchController;
  
  bool _isSearchExpanded = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _listController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _searchController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _listController.forward();
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _listController.dispose();
    _searchController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppTheme.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Animated background
            _buildAnimatedBackground(),
            
            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Modern App Bar
                  _buildModernAppBar(),
                  
                  // Search Bar
                  ModernAnimations.slideInFromBottom(
                    delay: const Duration(milliseconds: 200),
                    child: _buildSearchBar(),
                  ),
                  
                  // Favorites Content
                  Expanded(
                    child: Consumer<FavoritesProvider>(
                      builder: (context, favoritesProvider, child) {
                        return _buildFavoritesContent(favoritesProvider);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Stack(
          children: [
            // Floating orb 1
            Positioned(
              top: 100 + 40 * (0.5 + 0.5 * math.sin(_backgroundController.value * 2 * math.pi)),
              left: 60 + 30 * (0.5 + 0.5 * math.cos(_backgroundController.value * 2 * math.pi)),
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.primaryColor.withOpacity(0.1),
                      AppTheme.primaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Floating orb 2
            Positioned(
              top: 300 + 35 * (0.5 + 0.5 * math.sin((_backgroundController.value + 0.5) * 2 * math.pi)),
              right: 40 + 25 * (0.5 + 0.5 * math.cos((_backgroundController.value + 0.3) * 2 * math.pi)),
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.accentColor.withOpacity(0.1),
                      AppTheme.accentColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModernAppBar() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: GlassContainer(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: AppTheme.accentGradient,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: AnimatedWidgets.breathingAnimation(
                      child: const Icon(
                        Icons.favorite,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'My Favorites',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textColor,
                        ),
                      ),
                      Text(
                        'Your saved items',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 16),
          ModernIconButton(
            icon: Icons.search,
            onPressed: _toggleSearch,
            color: AppTheme.textColor,
            backgroundColor: AppTheme.glassColor,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: AnimatedWidgets.expandingSearchBar(
        controller: _searchController,
        isExpanded: _isSearchExpanded,
        onTap: _toggleSearch,
        hintText: 'Search favorites...',
      ),
    );
  }

  Widget _buildFavoritesContent(FavoritesProvider favoritesProvider) {
    if (favoritesProvider.isLoading) {
      return _buildLoadingState();
    }

    if (favoritesProvider.favorites.isEmpty) {
      return _buildEmptyState();
    }

    return _buildFavoritesList(favoritesProvider);
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedWidgets.pulsingLoader(
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppTheme.primaryGradient,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.favorite,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
          const SizedBox(height: 24),
          AnimatedWidgets.typewriterText(
            text: 'Loading your favorites...',
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return ModernAnimations.fadeIn(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedWidgets.breathingAnimation(
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: AppTheme.accentGradient,
                  ),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.favorite_border,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 32),
            const Text(
              'No favorites yet',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Start adding items to your favorites\nto see them here',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ModernButton(
              text: 'Browse Menu',
              onPressed: () => Navigator.pushReplacementNamed(context, '/dashboard'),
              gradientColors: AppTheme.accentGradient,
              elevation: 8,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesList(FavoritesProvider favoritesProvider) {
    final favorites = favoritesProvider.favorites;
    
    return AnimatedList(
      key: GlobalKey<AnimatedListState>(),
      padding: const EdgeInsets.all(20),
      itemBuilder: (context, index, animation) {
        if (index >= favorites.length) return const SizedBox.shrink();
        
        final favorite = favorites[index];
        return AnimatedWidgets.staggeredListItem(
          index: index,
          child: _buildFavoriteCard(favorite, favoritesProvider),
        );
      },
    );
  }

  Widget _buildFavoriteCard(dynamic favorite, FavoritesProvider favoritesProvider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: FloatingGlassCard(
        onTap: () {
          // Navigate to item details
          Navigator.pushNamed(context, '/item-details', arguments: favorite);
        },
        child: Row(
          children: [
            // Item Image
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: RobustImage(
                imageUrl: favorite.imageUrl ?? '',
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 16),
            
            // Item Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    favorite.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    favorite.description ?? 'Delicious item',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      AnimatedWidgets.animatedPrice(
                        value: favorite.price,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const Spacer(),
                      AnimatedWidgets.rippleButton(
                        onPressed: () => _removeFavorite(favorite, favoritesProvider),
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppTheme.errorColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(
                            Icons.favorite,
                            color: AppTheme.errorColor,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
    });
    
    if (_isSearchExpanded) {
      _searchController.forward();
    } else {
      _searchController.reverse();
      _searchController.clear();
    }
  }

  void _removeFavorite(dynamic favorite, FavoritesProvider favoritesProvider) {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          'Remove Favorite',
          style: TextStyle(color: AppTheme.textColor),
        ),
        content: Text(
          'Remove "${favorite.name}" from your favorites?',
          style: const TextStyle(color: AppTheme.textSecondaryColor),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ModernButton(
            text: 'Remove',
            onPressed: () {
              Navigator.pop(context);
              favoritesProvider.removeFavorite(favorite.id);
            },
            gradientColors: [AppTheme.errorColor, AppTheme.errorColor],
          ),
        ],
      ),
    );
  }
}
