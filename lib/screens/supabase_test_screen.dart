import 'package:flutter/material.dart';
import 'package:unieatsappv0/services/supabase_service.dart';
import 'package:unieatsappv0/services/supabase_cafeteria_service.dart';
import 'package:unieatsappv0/services/supabase_menu_service.dart';

class SupabaseTestScreen extends StatefulWidget {
  const SupabaseTestScreen({super.key});

  @override
  State<SupabaseTestScreen> createState() => _SupabaseTestScreenState();
}

class _SupabaseTestScreenState extends State<SupabaseTestScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  final SupabaseCafeteriaService _cafeteriaService = SupabaseCafeteriaService();
  final SupabaseMenuService _menuService = SupabaseMenuService();

  String _status = 'Checking Supabase connection...';
  bool _isLoading = true;
  final List<String> _testResults = [];

  @override
  void initState() {
    super.initState();
    _runComprehensiveTest();
  }

  Future<void> _runComprehensiveTest() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
      _status = 'Running comprehensive Supabase tests...';
    });

    try {
      // Test 1: Basic Connection
      await _testBasicConnection();

      // Test 2: Database Operations
      await _testDatabaseOperations();

      // Test 3: Authentication
      await _testAuthentication();

      // Test 4: Storage
      await _testStorage();

      // Test 5: Real-time
      await _testRealtime();

      setState(() {
        _status = 'All tests completed!\n\n${_testResults.join('\n')}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Test failed: $e\n\n${_testResults.join('\n')}';
        _isLoading = false;
      });
    }
  }

  Future<void> _testBasicConnection() async {
    try {
      final client = _supabaseService.client;
      final session = client.auth.currentSession;

      _testResults.add('✅ Basic Connection: SUCCESS');
      _testResults.add('   Project: lqtnaxvqkoynaziiinqh');
      _testResults.add('   Session: ${session != null ? 'Active' : 'None'}');
    } catch (e) {
      _testResults.add('❌ Basic Connection: FAILED - $e');
      rethrow;
    }
  }

  Future<void> _testDatabaseOperations() async {
    try {
      // Test cafeterias
      final cafeterias = await _cafeteriaService.getAllCafeterias();
      _testResults.add('✅ Cafeterias: ${cafeterias.length} found');

      if (cafeterias.isNotEmpty) {
        // Test menu items
        final menuItems = await _menuService.getMenuItemsByCafeteria(cafeterias.first.id);
        _testResults.add('✅ Menu Items: ${menuItems.length} found');

        // Test orders (using direct query since getAllOrders doesn't exist)
        final ordersResponse = await _supabaseService.select('orders');
        _testResults.add('✅ Orders: ${ordersResponse.length} found');

        // Test favorites
        final favoritesResponse = await _supabaseService.select('favorites');
        _testResults.add('✅ Favorites: ${favoritesResponse.length} found');

        // Test chat conversations
        final conversationsResponse = await _supabaseService.select('chat_conversations');
        _testResults.add('✅ Chat Conversations: ${conversationsResponse.length} found');

        // Test chat messages
        final messagesResponse = await _supabaseService.select('chat_messages');
        _testResults.add('✅ Chat Messages: ${messagesResponse.length} found');

        // Test support tickets
        final ticketsResponse = await _supabaseService.select('support_tickets');
        _testResults.add('✅ Support Tickets: ${ticketsResponse.length} found');
      }

      _testResults.add('✅ Database Operations: SUCCESS');
    } catch (e) {
      _testResults.add('❌ Database Operations: FAILED - $e');
    }
  }

  Future<void> _testAuthentication() async {
    try {
      final client = _supabaseService.client;
      final user = client.auth.currentUser;

      _testResults.add('✅ Authentication: ${user != null ? 'User logged in' : 'No user (OK)'}');
    } catch (e) {
      _testResults.add('❌ Authentication: FAILED - $e');
    }
  }

  Future<void> _testStorage() async {
    try {
      final client = _supabaseService.client;
      final buckets = await client.storage.listBuckets();

      _testResults.add('✅ Storage: ${buckets.length} buckets found');
      for (final bucket in buckets) {
        _testResults.add('   - ${bucket.name} (${bucket.public ? 'public' : 'private'})');
      }
    } catch (e) {
      _testResults.add('❌ Storage: FAILED - $e');
    }
  }

  Future<void> _testRealtime() async {
    try {
      final client = _supabaseService.client;
      // Just check if realtime is available
      final channel = client.channel('test-channel');
      _testResults.add('✅ Real-time: Channel created successfully');
      await channel.unsubscribe();
    } catch (e) {
      _testResults.add('❌ Real-time: FAILED - $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase Test'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isLoading)
                const CircularProgressIndicator()
              else
                Text(
                  _status,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
              const SizedBox(height: 30),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _status = 'Checking Supabase connection...';
                  });
                  _runComprehensiveTest();
                },
                child: const Text('Test Connection Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
