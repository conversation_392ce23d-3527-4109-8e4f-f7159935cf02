import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/widgets/glass_container.dart';
import 'package:unieatsappv0/widgets/modern_button.dart';
import 'package:unieatsappv0/widgets/animated_widgets.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/utils/animations.dart';

class ModernNotificationsScreen extends StatefulWidget {
  const ModernNotificationsScreen({super.key});

  @override
  State<ModernNotificationsScreen> createState() => _ModernNotificationsScreenState();
}

class _ModernNotificationsScreenState extends State<ModernNotificationsScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _listController;
  late AnimationController _headerController;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _listController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _headerController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _headerController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _listController.forward();
    });

    // Clear old notifications when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
      notificationProvider.clearOldNotifications(); // Clear old notifications to fix timestamp issues
    });
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _listController.dispose();
    _headerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppTheme.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Animated background
            _buildAnimatedBackground(),
            
            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Modern App Bar
                  _buildModernAppBar(),
                  
                  // Notifications Content
                  Expanded(
                    child: Consumer<NotificationProvider>(
                      builder: (context, notificationProvider, child) {
                        return _buildNotificationsContent(notificationProvider);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Stack(
          children: [
            // Floating orb 1
            Positioned(
              top: 100 + 45 * (0.5 + 0.5 * math.sin(_backgroundController.value * 2 * math.pi)),
              left: 60 + 35 * (0.5 + 0.5 * math.cos(_backgroundController.value * 2 * math.pi)),
              child: Container(
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.warningColor.withOpacity(0.1),
                      AppTheme.warningColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Floating orb 2
            Positioned(
              top: 300 + 40 * (0.5 + 0.5 * math.sin((_backgroundController.value + 0.6) * 2 * math.pi)),
              right: 40 + 30 * (0.5 + 0.5 * math.cos((_backgroundController.value + 0.4) * 2 * math.pi)),
              child: Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppTheme.primaryColor.withOpacity(0.1),
                      AppTheme.primaryColor.withOpacity(0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModernAppBar() {
    return AnimatedBuilder(
      animation: _headerController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _headerController.value)),
          child: Opacity(
            opacity: _headerController.value,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  ModernIconButton(
                    icon: Icons.arrow_back,
                    onPressed: () => Navigator.pop(context),
                    color: AppTheme.textColor,
                    backgroundColor: AppTheme.glassColor,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: GlassContainer(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      child: Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [AppTheme.warningColor, AppTheme.warningColor],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: AnimatedWidgets.pulsingLoader(
                              child: const Icon(
                                Icons.notifications,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Notifications',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.textColor,
                                ),
                              ),
                              Text(
                                'Stay updated',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ModernIconButton(
                    icon: Icons.mark_email_read,
                    onPressed: _markAllAsRead,
                    color: AppTheme.textColor,
                    backgroundColor: AppTheme.glassColor,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotificationsContent(NotificationProvider notificationProvider) {
    if (notificationProvider.isLoading) {
      return _buildLoadingState();
    }

    if (notificationProvider.notifications.isEmpty) {
      return _buildEmptyState();
    }

    return _buildNotificationsList(notificationProvider);
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedWidgets.pulsingLoader(
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [AppTheme.warningColor, AppTheme.warningColor],
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.notifications,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
          const SizedBox(height: 24),
          AnimatedWidgets.typewriterText(
            text: 'Loading notifications...',
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return ModernAnimations.fadeIn(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedWidgets.breathingAnimation(
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [AppTheme.warningColor, AppTheme.warningColor],
                  ),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.notifications_none,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 32),
            const Text(
              'No notifications',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'You\'re all caught up!\nWe\'ll notify you when something new happens.',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList(NotificationProvider notificationProvider) {
    return AnimatedBuilder(
      animation: _listController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _listController.value)),
          child: Opacity(
            opacity: _listController.value,
            child: ListView.builder(
              padding: const EdgeInsets.all(20),
              itemCount: notificationProvider.notifications.length,
              itemBuilder: (context, index) {
                final notification = notificationProvider.notifications[index];
                return AnimatedWidgets.staggeredListItem(
                  index: index,
                  child: _buildNotificationCard(notification, notificationProvider),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotificationCard(dynamic notification, NotificationProvider notificationProvider) {
    final isUnread = !(notification.isRead ?? false);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: FloatingGlassCard(
        onTap: () => _handleNotificationTap(notification, notificationProvider),
        child: Row(
          children: [
            // Notification Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _getNotificationGradient(notification.type),
                ),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                _getNotificationIcon(notification.type),
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            
            // Notification Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          notification.title ?? 'Notification',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: isUnread ? FontWeight.w600 : FontWeight.w500,
                            color: AppTheme.textColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (isUnread)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    notification.message ?? 'No message',
                    style: TextStyle(
                      fontSize: 14,
                      color: isUnread 
                          ? AppTheme.textColor 
                          : AppTheme.textSecondaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatTime(notification.timestamp),
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            
            // Action Button
            ModernIconButton(
              icon: Icons.more_vert,
              onPressed: () => _showNotificationOptions(notification, notificationProvider),
              color: AppTheme.textSecondaryColor,
              size: 32,
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getNotificationGradient(String? type) {
    switch (type?.toLowerCase()) {
      case 'order':
        return AppTheme.primaryGradient;
      case 'promotion':
        return AppTheme.secondaryGradient;
      case 'system':
        return AppTheme.accentGradient;
      case 'alert':
        return [AppTheme.errorColor, AppTheme.errorColor];
      default:
        return [AppTheme.warningColor, AppTheme.warningColor];
    }
  }

  IconData _getNotificationIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'order':
        return Icons.shopping_bag;
      case 'promotion':
        return Icons.local_offer;
      case 'system':
        return Icons.info;
      case 'alert':
        return Icons.warning;
      default:
        return Icons.notifications;
    }
  }

  void _handleNotificationTap(dynamic notification, NotificationProvider notificationProvider) async {
    // Mark notification as read
    if (!(notification.isRead ?? false)) {
      await notificationProvider.markAsRead(notification.id);
    }

    // Navigate to order details if it's an order notification
    if (notification.type == 'order' && notification.orderId != null) {
      Navigator.pushNamed(
        context,
        '/order-details',
        arguments: {'orderId': notification.orderId},
      );
    } else {
      // Show notification details in a dialog
      _showNotificationDetails(notification);
    }
  }

  void _showNotificationDetails(dynamic notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title ?? 'Notification'),
        content: Text(notification.message ?? 'No message'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime? dateTime) {
    if (dateTime == null) return 'Unknown';

    final now = DateTime.now();

    // Convert both to local time for accurate comparison
    final localNow = now.toLocal();
    final localDateTime = dateTime.toLocal();

    final difference = localNow.difference(localDateTime);

    // Debug logging
    debugPrint('🕐 Timestamp formatting:');
    debugPrint('   Original timestamp: $dateTime');
    debugPrint('   Local timestamp: $localDateTime');
    debugPrint('   Local now: $localNow');
    debugPrint('   Difference: ${difference.inMinutes} minutes');

    // Handle negative differences (future timestamps)
    if (difference.isNegative) {
      return 'Just now';
    }

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${localDateTime.day}/${localDateTime.month}/${localDateTime.year}';
    }
  }

  void _handleNotificationTap(dynamic notification, NotificationProvider notificationProvider) {
    // Mark as read
    if (!(notification.isRead ?? false)) {
      notificationProvider.markAsRead(notification.id);
    }

    // Handle notification action based on type
    switch (notification.type?.toLowerCase()) {
      case 'order':
        // Navigate to order history screen for order-related notifications
        if (notification.orderId != null && notification.orderId!.isNotEmpty) {
          debugPrint('🔗 Navigating to order history for order: ${notification.orderId}');
          Navigator.pushNamed(context, '/order_history');
        } else {
          debugPrint('⚠️ No order ID found for notification: ${notification.id}');
          Navigator.pushNamed(context, '/order_history');
        }
        break;
      case 'promotion':
        Navigator.pushNamed(context, '/promotions');
        break;
      default:
        // Show notification details
        _showNotificationDetails(notification);
        break;
    }
  }

  void _showNotificationDetails(dynamic notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text(
          notification.title ?? 'Notification',
          style: const TextStyle(color: AppTheme.textColor),
        ),
        content: Text(
          notification.message ?? 'No message',
          style: const TextStyle(color: AppTheme.textSecondaryColor),
        ),
        actions: [
          ModernButton(
            text: 'Close',
            onPressed: () => Navigator.pop(context),
            isOutlined: true,
          ),
        ],
      ),
    );
  }

  void _showNotificationOptions(dynamic notification, NotificationProvider notificationProvider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => GlassContainer(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ModernButton(
              text: notification.isRead ? 'Mark as Unread' : 'Mark as Read',
              onPressed: () {
                Navigator.pop(context);
                if (notification.isRead) {
                  notificationProvider.markAsUnread(notification.id);
                } else {
                  notificationProvider.markAsRead(notification.id);
                }
              },
              width: double.infinity,
              isOutlined: true,
            ),
            const SizedBox(height: 12),
            ModernButton(
              text: 'Delete',
              onPressed: () {
                Navigator.pop(context);
                notificationProvider.deleteNotification(notification.id);
              },
              width: double.infinity,
              gradientColors: [AppTheme.errorColor, AppTheme.errorColor],
            ),
          ],
        ),
      ),
    );
  }

  void _markAllAsRead() {
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    notificationProvider.markAllAsRead();
  }
}
