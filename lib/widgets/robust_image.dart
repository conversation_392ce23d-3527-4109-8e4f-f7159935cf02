import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// A robust image widget that handles various image sources with proper error handling
class RobustImage extends StatelessWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Duration timeout;

  const RobustImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.timeout = const Duration(seconds: 10),
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Default fallback widget
    final defaultErrorWidget = errorWidget ?? Container(
      width: width,
      height: height,
      color: theme.colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.image_not_supported,
        size: (width != null && height != null) ? (width! + height!) / 8 : 40,
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );

    final defaultPlaceholder = placeholder ?? Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );

    // If no image URL, return error widget
    if (imageUrl == null || imageUrl!.isEmpty) {
      return defaultErrorWidget;
    }

    final url = imageUrl!;

    // Handle base64 encoded images
    if (url.startsWith('data:image/')) {
      try {
        // Extract the base64 data
        final base64Data = url.split(',')[1];
        final bytes = base64Decode(base64Data);

        return Image.memory(
          bytes,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('Error loading base64 image: $error');
            return defaultErrorWidget;
          },
        );
      } catch (e) {
        debugPrint('Error decoding base64 image: $e');
        return defaultErrorWidget;
      }
    }

    // Handle network images (HTTP/HTTPS) with timeout
    if (url.startsWith('http') || url.startsWith('https')) {
      // For web, use a more robust approach
      if (kIsWeb) {
        return _WebSafeNetworkImage(
          url: url,
          width: width,
          height: height,
          fit: fit,
          placeholder: defaultPlaceholder,
          errorWidget: defaultErrorWidget,
          timeout: timeout,
        );
      } else {
        return _NetworkImageWithTimeout(
          url: url,
          width: width,
          height: height,
          fit: fit,
          placeholder: defaultPlaceholder,
          errorWidget: defaultErrorWidget,
          timeout: timeout,
        );
      }
    }

    // Handle asset images
    if (url.startsWith('assets/')) {
      return Image.asset(
        url,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          debugPrint('Error loading asset image: $error');
          return defaultErrorWidget;
        },
      );
    }

    // Unknown image format, return error widget
    debugPrint('Unknown image format: $url');
    return defaultErrorWidget;
  }
}

/// Internal widget for handling network images with timeout
class _NetworkImageWithTimeout extends StatefulWidget {
  final String url;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget placeholder;
  final Widget errorWidget;
  final Duration timeout;

  const _NetworkImageWithTimeout({
    required this.url,
    this.width,
    this.height,
    required this.fit,
    required this.placeholder,
    required this.errorWidget,
    required this.timeout,
  });

  @override
  State<_NetworkImageWithTimeout> createState() => _NetworkImageWithTimeoutState();
}

class _NetworkImageWithTimeoutState extends State<_NetworkImageWithTimeout> {
  bool _hasTimedOut = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    
    // Set up timeout
    Future.delayed(widget.timeout, () {
      if (mounted && !_hasError) {
        setState(() {
          _hasTimedOut = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_hasTimedOut) {
      debugPrint('Image loading timed out: ${widget.url}');
      return widget.errorWidget;
    }

    return Image.network(
      widget.url,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          // Image loaded successfully
          return child;
        }
        
        // Show loading progress
        return widget.placeholder;
      },
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Error loading network image: $error');
        _hasError = true;
        return widget.errorWidget;
      },
    );
  }
}

/// Web-safe network image widget that handles CORS and format issues better
class _WebSafeNetworkImage extends StatefulWidget {
  final String url;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget placeholder;
  final Widget errorWidget;
  final Duration timeout;

  const _WebSafeNetworkImage({
    required this.url,
    this.width,
    this.height,
    required this.fit,
    required this.placeholder,
    required this.errorWidget,
    required this.timeout,
  });

  @override
  State<_WebSafeNetworkImage> createState() => _WebSafeNetworkImageState();
}

class _WebSafeNetworkImageState extends State<_WebSafeNetworkImage> {
  bool _hasTimedOut = false;
  bool _hasError = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    // Set up timeout
    Future.delayed(widget.timeout, () {
      if (mounted && _isLoading && !_hasError) {
        setState(() {
          _hasTimedOut = true;
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_hasTimedOut) {
      debugPrint('Image loading timed out: ${widget.url}');
      return widget.errorWidget;
    }

    if (_hasError) {
      return widget.errorWidget;
    }

    // For web, try to handle different image formats and CORS issues
    return Container(
      width: widget.width,
      height: widget.height,
      child: Image.network(
        widget.url,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        // Add headers to handle CORS if needed
        headers: const {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            // Image loaded successfully
            _isLoading = false;
            return child;
          }

          // Show loading progress
          return widget.placeholder;
        },
        errorBuilder: (context, error, stackTrace) {
          debugPrint('Error loading web image: $error');
          debugPrint('Image URL: ${widget.url}');
          setState(() {
            _hasError = true;
            _isLoading = false;
          });

          // Try to provide a fallback with a different approach
          return Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_not_supported,
                  size: 32,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 4),
                Text(
                  'Image unavailable',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
