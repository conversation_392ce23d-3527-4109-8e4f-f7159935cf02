import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/services/menu_item_rating_service.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';

class MenuItemRatingDialog extends StatefulWidget {
  final String menuItemId;
  final String menuItemName;
  final String orderId;
  final SupabaseMenuItemRating? existingRating;

  const MenuItemRatingDialog({
    super.key,
    required this.menuItemId,
    required this.menuItemName,
    required this.orderId,
    this.existingRating,
  });

  @override
  State<MenuItemRatingDialog> createState() => _MenuItemRatingDialogState();
}

class _MenuItemRatingDialogState extends State<MenuItemRatingDialog> {
  final MenuItemRatingService _ratingService = MenuItemRatingService();
  final TextEditingController _commentController = TextEditingController();
  
  int _rating = 0;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    
    // Pre-fill if editing existing rating
    if (widget.existingRating != null) {
      _rating = widget.existingRating!.rating;
      _commentController.text = widget.existingRating!.reviewComment ?? '';
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Rate Menu Item',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.menuItemName,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),

            // Star rating
            Text(
              'How would you rate this item?',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) {
                final starIndex = index + 1;
                return IconButton(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  constraints: const BoxConstraints(),
                  icon: Icon(
                    starIndex <= _rating ? Icons.star : Icons.star_border,
                    color: starIndex <= _rating ? Colors.amber : Colors.grey,
                    size: 36,
                  ),
                  onPressed: () {
                    setState(() {
                      _rating = starIndex;
                    });
                  },
                );
              }),
            ),

            const SizedBox(height: 24),

            // Comment field
            Text(
              'Add a comment (optional)',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            
            TextField(
              controller: _commentController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'Tell others about your experience...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isSubmitting ? null : () {
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: (_rating > 0 && !_isSubmitting) ? _submitRating : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: theme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: _isSubmitting
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(widget.existingRating != null ? 'Update' : 'Submit'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitRating() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      await _ratingService.submitRating(
        menuItemId: widget.menuItemId,
        orderId: widget.orderId,
        rating: _rating,
        comment: _commentController.text.trim().isEmpty 
            ? null 
            : _commentController.text.trim(),
      );

      if (mounted) {
        // Refresh menu items to update ratings display
        final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
        await supabaseProvider.refreshMenuItemsAfterRating();

        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate success

          SnackBarUtils.showSuccessSnackBar(
            context: context,
            message: widget.existingRating != null
                ? 'Rating updated successfully!'
                : 'Thank you for your rating!',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarUtils.showErrorSnackBar(
          context: context,
          message: 'Failed to submit rating. Please try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

/// Helper function to show the rating dialog
Future<bool?> showMenuItemRatingDialog({
  required BuildContext context,
  required String menuItemId,
  required String menuItemName,
  required String orderId,
  SupabaseMenuItemRating? existingRating,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => MenuItemRatingDialog(
      menuItemId: menuItemId,
      menuItemName: menuItemName,
      orderId: orderId,
      existingRating: existingRating,
    ),
  );
}
