import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/widgets/loading_overlay.dart';

/// A widget that wraps its child with a loading overlay based on the SupabaseProvider's loading state
class SupabaseLoadingWrapper extends StatelessWidget {
  final Widget child;
  
  const SupabaseLoadingWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SupabaseProvider>(
      builder: (context, supabaseProvider, _) {
        // Check if any loading state is active
        final bool isLoading = 
            supabaseProvider.isLoadingCafeterias ||
            supabaseProvider.isLoadingMenuItems ||
            supabaseProvider.isLoadingOrders ||
            supabaseProvider.isProcessingCheckout;
            
        // Get the appropriate loading message
        final String? message = supabaseProvider.loadingMessage;
        
        return LoadingOverlay(
          isLoading: isLoading,
          message: message,
          child: child,
        );
      },
    );
  }
}
