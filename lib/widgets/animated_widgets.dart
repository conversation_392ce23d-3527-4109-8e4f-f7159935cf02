import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Collection of animated widgets using only built-in Flutter animations
class AnimatedWidgets {
  
  /// Animated counter with smooth number transitions
  static Widget animatedCounter({
    required int value,
    required TextStyle style,
    Duration duration = const Duration(milliseconds: 500),
  }) {
    return TweenAnimationBuilder<int>(
      tween: IntTween(begin: 0, end: value),
      duration: duration,
      curve: Curves.easeOutCubic,
      builder: (context, animatedValue, child) {
        return Text(
          animatedValue.toString(),
          style: style,
        );
      },
    );
  }

  /// Animated price with currency formatting
  static Widget animatedPrice({
    required double value,
    required TextStyle style,
    String currency = 'EGP',
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: value),
      duration: duration,
      curve: Curves.easeOutCubic,
      builder: (context, animatedValue, child) {
        return Text(
          '${animatedValue.toStringAsFixed(2)} $currency',
          style: style,
        );
      },
    );
  }

  /// Bouncing cart icon animation
  static Widget bouncingCartIcon({
    required IconData icon,
    required Color color,
    double size = 24,
    bool isAnimating = false,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.elasticOut,
      transform: Matrix4.identity()..scale(isAnimating ? 1.2 : 1.0),
      child: Icon(
        icon,
        color: color,
        size: size,
      ),
    );
  }

  /// Sliding notification banner
  static Widget slidingNotification({
    required Widget child,
    required bool isVisible,
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return AnimatedSlide(
      offset: isVisible ? Offset.zero : const Offset(0, -1),
      duration: duration,
      curve: Curves.easeInOutCubic,
      child: AnimatedOpacity(
        opacity: isVisible ? 1.0 : 0.0,
        duration: duration,
        child: child,
      ),
    );
  }

  /// Pulsing loading indicator
  static Widget pulsingLoader({
    required Widget child,
    Duration duration = const Duration(seconds: 1),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        final pulseValue = 0.5 + 0.5 * math.sin(value * 2 * math.pi);
        return Opacity(
          opacity: pulseValue,
          child: Transform.scale(
            scale: 0.9 + 0.1 * pulseValue,
            child: child,
          ),
        );
      },
      onEnd: () {
        // Restart animation
      },
      child: child,
    );
  }

  /// Rotating refresh icon
  static Widget rotatingRefresh({
    required bool isRefreshing,
    double size = 24,
    Color? color,
  }) {
    return AnimatedRotation(
      turns: isRefreshing ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOutCubic,
      child: Icon(
        Icons.refresh,
        size: size,
        color: color,
      ),
    );
  }

  /// Expanding search bar
  static Widget expandingSearchBar({
    required TextEditingController controller,
    required bool isExpanded,
    required VoidCallback onTap,
    String hintText = 'Search...',
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOutCubic,
      width: isExpanded ? 250 : 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: Colors.grey.withOpacity(0.1),
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: GestureDetector(
              onTap: onTap,
              child: const Icon(Icons.search),
            ),
          ),
          if (isExpanded)
            Expanded(
              child: TextField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Floating action button with scale animation
  static Widget animatedFAB({
    required VoidCallback onPressed,
    required Widget child,
    bool isVisible = true,
  }) {
    return AnimatedScale(
      scale: isVisible ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOutBack,
      child: FloatingActionButton(
        onPressed: onPressed,
        child: child,
      ),
    );
  }

  /// Sliding panel from bottom
  static Widget slidingBottomPanel({
    required Widget child,
    required bool isVisible,
    double height = 300,
  }) {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOutCubic,
      bottom: isVisible ? 0 : -height,
      left: 0,
      right: 0,
      height: height,
      child: child,
    );
  }

  /// Morphing container with smooth transitions
  static Widget morphingContainer({
    required Widget child,
    required Color color,
    required BorderRadius borderRadius,
    required EdgeInsets padding,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: Curves.easeInOutCubic,
      decoration: BoxDecoration(
        color: color,
        borderRadius: borderRadius,
      ),
      padding: padding,
      child: child,
    );
  }

  /// Staggered list animation
  static Widget staggeredListItem({
    required Widget child,
    required int index,
    Duration delay = const Duration(milliseconds: 100),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 300 + (index * delay.inMilliseconds)),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// Ripple effect button
  static Widget rippleButton({
    required Widget child,
    required VoidCallback onPressed,
    Color? rippleColor,
    BorderRadius? borderRadius,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        splashColor: rippleColor?.withOpacity(0.3),
        highlightColor: rippleColor?.withOpacity(0.1),
        borderRadius: borderRadius,
        child: child,
      ),
    );
  }

  /// Breathing animation (scale in and out)
  static Widget breathingAnimation({
    required Widget child,
    Duration duration = const Duration(seconds: 2),
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        final scale = minScale + (maxScale - minScale) * 
            (0.5 + 0.5 * math.sin(value * 2 * math.pi));
        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
      onEnd: () {
        // Restart animation
      },
      child: child,
    );
  }

  /// Shimmer loading effect
  static Widget shimmerLoading({
    required Widget child,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: const [
                Colors.grey,
                Colors.white,
                Colors.grey,
              ],
              stops: [
                (value - 0.3).clamp(0.0, 1.0),
                value,
                (value + 0.3).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      onEnd: () {
        // Restart animation
      },
      child: child,
    );
  }

  /// Typewriter text animation
  static Widget typewriterText({
    required String text,
    required TextStyle style,
    Duration duration = const Duration(milliseconds: 50),
  }) {
    return TweenAnimationBuilder<int>(
      tween: IntTween(begin: 0, end: text.length),
      duration: Duration(milliseconds: duration.inMilliseconds * text.length),
      builder: (context, value, child) {
        return Text(
          text.substring(0, value),
          style: style,
        );
      },
    );
  }
}
