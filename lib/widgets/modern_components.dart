import 'package:flutter/material.dart';
import '../theme/modern_theme.dart';

// Modern Button with Gradient
class ModernButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isSecondary;
  final IconData? icon;
  final double? width;
  final double? height;

  const ModernButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isSecondary = false,
    this.icon,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height ?? 56,
      decoration: BoxDecoration(
        gradient: isSecondary ? ModernTheme.secondaryGradient : ModernTheme.primaryGradient,
        borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
        boxShadow: onPressed != null ? ModernTheme.softShadow : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                else ...[
                  if (icon != null) ...[
                    Icon(icon, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: ModernTheme.labelLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Modern Input Field
class ModernTextField extends StatelessWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final bool obscureText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixTap;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? maxLines;

  const ModernTextField({
    Key? key,
    this.label,
    this.hint,
    this.controller,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixTap,
    this.validator,
    this.keyboardType,
    this.maxLines = 1,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: ModernTheme.labelMedium.copyWith(
              color: ModernTheme.textPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            color: ModernTheme.surfaceColor,
            borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
            boxShadow: ModernTheme.softShadow,
          ),
          child: TextFormField(
            controller: controller,
            obscureText: obscureText,
            validator: validator,
            keyboardType: keyboardType,
            maxLines: maxLines,
            style: ModernTheme.bodyLarge,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: ModernTheme.bodyMedium,
              prefixIcon: prefixIcon != null
                  ? Icon(prefixIcon, color: ModernTheme.textSecondary)
                  : null,
              suffixIcon: suffixIcon != null
                  ? IconButton(
                      icon: Icon(suffixIcon, color: ModernTheme.textSecondary),
                      onPressed: onSuffixTap,
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                borderSide: const BorderSide(
                  color: ModernTheme.primaryColor,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.transparent,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Modern Card with Glass Effect
class ModernCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool hasGlassEffect;

  const ModernCard({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.hasGlassEffect = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: hasGlassEffect 
            ? ModernTheme.glassBackground 
            : ModernTheme.surfaceColor,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        border: hasGlassEffect 
            ? Border.all(color: ModernTheme.glassBorder, width: 1)
            : null,
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(ModernTheme.spaceM),
            child: child,
          ),
        ),
      ),
    );
  }
}

// Modern App Bar
class ModernAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final bool hasGradient;

  const ModernAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.hasGradient = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: hasGradient
          ? const BoxDecoration(
              gradient: ModernTheme.primaryGradient,
            )
          : null,
      child: AppBar(
        title: Text(
          title,
          style: ModernTheme.headingSmall.copyWith(
            color: hasGradient ? Colors.white : ModernTheme.textPrimary,
          ),
        ),
        centerTitle: centerTitle,
        backgroundColor: hasGradient ? Colors.transparent : ModernTheme.surfaceColor,
        elevation: 0,
        leading: leading,
        actions: actions,
        iconTheme: IconThemeData(
          color: hasGradient ? Colors.white : ModernTheme.textPrimary,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Modern Loading Indicator
class ModernLoadingIndicator extends StatelessWidget {
  final double size;
  final Color? color;

  const ModernLoadingIndicator({
    Key? key,
    this.size = 24,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 3,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? ModernTheme.primaryColor,
        ),
      ),
    );
  }
}

// Modern Badge
class ModernBadge extends StatelessWidget {
  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final bool isSmall;

  const ModernBadge({
    Key? key,
    required this.text,
    this.backgroundColor,
    this.textColor,
    this.isSmall = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 8 : 12,
        vertical: isSmall ? 4 : 6,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? ModernTheme.primaryColor,
        borderRadius: BorderRadius.circular(isSmall ? 12 : 16),
      ),
      child: Text(
        text,
        style: (isSmall ? ModernTheme.bodySmall : ModernTheme.labelMedium).copyWith(
          color: textColor ?? Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

// Modern Bottom Sheet
class ModernBottomSheet extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool isDismissible;

  const ModernBottomSheet({
    Key? key,
    required this.child,
    this.title,
    this.isDismissible = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: ModernTheme.surfaceColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(ModernTheme.radiusXLarge),
          topRight: Radius.circular(ModernTheme.radiusXLarge),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isDismissible)
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                color: ModernTheme.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.all(ModernTheme.spaceM),
              child: Text(
                title!,
                style: ModernTheme.headingSmall,
              ),
            ),
            const Divider(height: 1),
          ],
          Flexible(child: child),
        ],
      ),
    );
  }
}
