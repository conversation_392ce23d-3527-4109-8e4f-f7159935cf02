import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/theme/app_theme.dart';

class MultiItemRatingDialog extends StatefulWidget {
  final List<CartItem> items;
  final Function(List<ItemRating> ratings, int overallRating, String? overallComment) onSubmit;

  const MultiItemRatingDialog({
    super.key,
    required this.items,
    required this.onSubmit,
  });

  @override
  State<MultiItemRatingDialog> createState() => _MultiItemRatingDialogState();
}

class ItemRating {
  final String itemId;
  final String itemName;
  final int rating;
  final String? comment;

  ItemRating({
    required this.itemId,
    required this.itemName,
    required this.rating,
    this.comment,
  });
}

class _MultiItemRatingDialogState extends State<MultiItemRatingDialog> {
  late List<ItemRating> _itemRatings;
  int _overallRating = 0;
  final TextEditingController _overallCommentController = TextEditingController();
  final Map<String, TextEditingController> _itemCommentControllers = {};

  @override
  void initState() {
    super.initState();
    
    // Initialize ratings for each item
    _itemRatings = widget.items.map((item) => ItemRating(
      itemId: item.id,
      itemName: item.name,
      rating: 0,
    )).toList();

    // Initialize comment controllers for each item
    for (final item in widget.items) {
      _itemCommentControllers[item.id] = TextEditingController();
    }
  }

  @override
  void dispose() {
    _overallCommentController.dispose();
    for (final controller in _itemCommentControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _updateItemRating(int index, int rating) {
    setState(() {
      _itemRatings[index] = ItemRating(
        itemId: _itemRatings[index].itemId,
        itemName: _itemRatings[index].itemName,
        rating: rating,
        comment: _itemCommentControllers[_itemRatings[index].itemId]?.text,
      );
    });
  }

  bool get _canSubmit {
    // At least overall rating or all item ratings should be provided
    return _overallRating > 0 || _itemRatings.every((rating) => rating.rating > 0);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.star, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Rate Your Order',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Overall rating section
                    Text(
                      'Overall Experience',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    // Overall rating stars
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(5, (index) {
                        final starIndex = index + 1;
                        return IconButton(
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          constraints: const BoxConstraints(),
                          icon: Icon(
                            starIndex <= _overallRating ? Icons.star : Icons.star_border,
                            color: starIndex <= _overallRating ? Colors.amber : Colors.grey,
                            size: 32,
                          ),
                          onPressed: () {
                            setState(() {
                              _overallRating = starIndex;
                            });
                          },
                        );
                      }),
                    ),

                    const SizedBox(height: 16),

                    // Overall comment
                    TextField(
                      controller: _overallCommentController,
                      decoration: InputDecoration(
                        labelText: 'Overall comment (optional)',
                        hintText: 'Tell us about your experience...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      maxLines: 2,
                    ),

                    const SizedBox(height: 24),

                    // Individual items section
                    Text(
                      'Rate Individual Items',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Individual item ratings
                    ...widget.items.asMap().entries.map((entry) {
                      final index = entry.key;
                      final item = entry.value;
                      final itemRating = _itemRatings[index];

                      return Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Item name
                              Text(
                                item.name,
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),

                              // Item rating stars
                              Row(
                                children: List.generate(5, (starIndex) {
                                  final star = starIndex + 1;
                                  return IconButton(
                                    padding: const EdgeInsets.symmetric(horizontal: 2),
                                    constraints: const BoxConstraints(),
                                    icon: Icon(
                                      star <= itemRating.rating ? Icons.star : Icons.star_border,
                                      color: star <= itemRating.rating ? Colors.amber : Colors.grey,
                                      size: 24,
                                    ),
                                    onPressed: () {
                                      _updateItemRating(index, star);
                                    },
                                  );
                                }),
                              ),

                              const SizedBox(height: 8),

                              // Item comment
                              TextField(
                                controller: _itemCommentControllers[item.id],
                                decoration: InputDecoration(
                                  labelText: 'Comment for ${item.name} (optional)',
                                  hintText: 'How was this item?',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  isDense: true,
                                ),
                                maxLines: 1,
                                onChanged: (value) {
                                  _updateItemRating(index, itemRating.rating);
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ],
                ),
              ),
            ),

            // Action buttons
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Skip'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _canSubmit
                          ? () {
                              // Update item ratings with final comments
                              final finalItemRatings = _itemRatings.asMap().entries.map((entry) {
                                final index = entry.key;
                                final rating = entry.value;
                                return ItemRating(
                                  itemId: rating.itemId,
                                  itemName: rating.itemName,
                                  rating: rating.rating,
                                  comment: _itemCommentControllers[rating.itemId]?.text.trim().isEmpty == true
                                      ? null
                                      : _itemCommentControllers[rating.itemId]?.text.trim(),
                                );
                              }).toList();

                              widget.onSubmit(
                                finalItemRatings,
                                _overallRating,
                                _overallCommentController.text.trim().isEmpty
                                    ? null
                                    : _overallCommentController.text.trim(),
                              );
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: theme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Submit'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
