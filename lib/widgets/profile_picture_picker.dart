import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:unieatsappv0/services/local_storage_service.dart';
import 'package:unieatsappv0/services/supabase_service.dart';

class ProfilePicturePicker extends StatefulWidget {
  final double size;
  final Function(String)? onImageSelected;

  const ProfilePicturePicker({
    super.key,
    this.size = 100,
    this.onImageSelected,
  });

  @override
  State<ProfilePicturePicker> createState() => _ProfilePicturePickerState();
}

class _ProfilePicturePickerState extends State<ProfilePicturePicker> {
  final LocalStorageService _storageService = LocalStorageService();
  final SupabaseService _supabaseService = SupabaseService();
  String? _imagePath;
  String? _imageUrl;
  Uint8List? _webImageBytes;

  @override
  void initState() {
    super.initState();
    _loadProfilePicture();
  }

  Future<void> _loadProfilePicture() async {
    if (kIsWeb) {
      // For web, try to load from Supabase
      try {
        final url = await _supabaseService.getUserProfileImageUrl();
        if (url != null && mounted) {
          setState(() {
            _imageUrl = url;
          });
        }
      } catch (e) {
        debugPrint('Error loading profile image from Supabase: $e');
      }
    } else {
      // For mobile, use local storage
      final path = await _storageService.getProfilePicturePath();
      if (path != null && File(path).existsSync()) {
        setState(() {
          _imagePath = path;
        });
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        if (kIsWeb) {
          // For web, upload to Supabase
          final bytes = await image.readAsBytes();

          // Show loading indicator
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 12),
                    Text('Uploading profile picture...'),
                  ],
                ),
                duration: Duration(seconds: 10),
              ),
            );
          }

          try {
            final url = await _supabaseService.uploadProfileImage(bytes, image.name);
            if (url != null && mounted) {
              setState(() {
                _imageUrl = url;
                _webImageBytes = bytes;
              });
              widget.onImageSelected?.call(url);

              // Show success message
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.white),
                      SizedBox(width: 8),
                      Text('Profile picture updated successfully!'),
                    ],
                  ),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 3),
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error uploading image: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } else {
          // For mobile, use local storage
          final String savedPath =
              await _storageService.saveImageToLocalStorage(File(image.path));
          if (mounted) {
            setState(() {
              _imagePath = savedPath;
            });
            widget.onImageSelected?.call(savedPath);
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking image: $e')),
        );
      }
    }
  }

  void _showImagePickerModal() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take a Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildImageWidget() {
    if (kIsWeb) {
      // For web, show image from bytes or URL
      if (_webImageBytes != null) {
        return Image.memory(
          _webImageBytes!,
          fit: BoxFit.cover,
          width: widget.size,
          height: widget.size,
        );
      } else if (_imageUrl != null) {
        return Image.network(
          _imageUrl!,
          fit: BoxFit.cover,
          width: widget.size,
          height: widget.size,
          errorBuilder: (context, error, stackTrace) {
            return Icon(
              Icons.person,
              size: widget.size * 0.6,
              color: Colors.grey,
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
          },
        );
      }
    } else {
      // For mobile, show image from file
      if (_imagePath != null) {
        return Image.file(
          File(_imagePath!),
          fit: BoxFit.cover,
          width: widget.size,
          height: widget.size,
        );
      }
    }

    // Default icon
    return Icon(
      Icons.person,
      size: widget.size * 0.6,
      color: Colors.grey,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showImagePickerModal,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Theme.of(context).primaryColor,
            width: 2,
          ),
        ),
        child: ClipOval(
          child: _buildImageWidget(),
        ),
      ),
    );
  }
}
