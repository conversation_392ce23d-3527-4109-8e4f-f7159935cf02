import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:unieatsappv0/services/local_storage_service.dart';

class ProfilePicturePicker extends StatefulWidget {
  final double size;
  final Function(String)? onImageSelected;

  const ProfilePicturePicker({
    super.key,
    this.size = 100,
    this.onImageSelected,
  });

  @override
  State<ProfilePicturePicker> createState() => _ProfilePicturePickerState();
}

class _ProfilePicturePickerState extends State<ProfilePicturePicker> {
  final LocalStorageService _storageService = LocalStorageService();
  String? _imagePath;

  @override
  void initState() {
    super.initState();
    _loadProfilePicture();
  }

  Future<void> _loadProfilePicture() async {
    final path = await _storageService.getProfilePicturePath();
    if (path != null && File(path).existsSync()) {
      setState(() {
        _imagePath = path;
      });
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        final String savedPath =
            await _storageService.saveImageToLocalStorage(File(image.path));
        if (mounted) {
          setState(() {
            _imagePath = savedPath;
          });
          widget.onImageSelected?.call(savedPath);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking image: $e')),
        );
      }
    }
  }

  void _showImagePickerModal() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take a Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showImagePickerModal,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: Theme.of(context).primaryColor,
            width: 2,
          ),
        ),
        child: ClipOval(
          child: _imagePath != null
              ? Image.file(
                  File(_imagePath!),
                  fit: BoxFit.cover,
                  width: widget.size,
                  height: widget.size,
                )
              : Icon(
                  Icons.person,
                  size: widget.size * 0.6,
                  color: Colors.grey,
                ),
        ),
      ),
    );
  }
}
