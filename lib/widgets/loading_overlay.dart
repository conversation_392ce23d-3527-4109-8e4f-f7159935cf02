import 'package:flutter/material.dart';

/// A loading overlay widget that shows a loading indicator with an optional message
class LoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isLoading;
  final Widget? child;

  const LoadingOverlay({
    super.key,
    this.message,
    required this.isLoading,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // The main content
        if (child != null) child!,

        // The loading overlay
        if (isLoading)
          Container(
            color: Colors.black.withAlpha(128),
            child: Center(
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      if (message != null) ...[
                        const SizedBox(height: 16),
                        Text(
                          message!,
                          style: Theme.of(context).textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
