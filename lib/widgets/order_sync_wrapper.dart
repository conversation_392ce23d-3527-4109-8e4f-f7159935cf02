import 'package:flutter/material.dart';
import '../services/order_sync_service.dart';

/// Widget that initializes the OrderSyncService when the app starts
class OrderSyncWrapper extends StatefulWidget {
  final Widget child;

  const OrderSyncWrapper({super.key, required this.child});

  @override
  State<OrderSyncWrapper> createState() => _OrderSyncWrapperState();
}

class _OrderSyncWrapperState extends State<OrderSyncWrapper> {
  bool _initialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    if (!_initialized) {
      _initializeOrderSync();
      _initialized = true;
    }
  }

  Future<void> _initializeOrderSync() async {
    try {
      final orderSyncService = OrderSyncService();
      await orderSyncService.initialize(context);
      debugPrint('OrderSyncService initialized in wrapper');
    } catch (e) {
      debugPrint('Error initializing OrderSyncService in wrapper: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
