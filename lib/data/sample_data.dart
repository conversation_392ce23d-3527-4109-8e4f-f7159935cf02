// lib/data/sample_data.dart
import 'package:unieatsappv0/models/cafeteria.dart';
import 'package:unieatsappv0/models/menu_item.dart';

// Sample orders data
/*
List<Order> getSampleOrders() {
  return [
    Order(
      id: 'order1',
      userId: 'user1',
      orderTime: DateTime.now().subtract(const Duration(hours: 2)),
      pickupTime: DateTime.now().add(const Duration(minutes: 30)),
      items: [
        CartItem(
          id: 'item1',
          name: 'Avocado Toast',
          price: 8.99,
          image: 'assets/images/avocado-toast.png',
          cafeteriaName: 'Beanos',
          buildingName: 'Main Campus',
          quantity: 1,
          customizations: [
            Customization(name: 'No salt', price: 0.0),
            Customization(name: 'Extra pepper', price: 0.0),
          ],
        ),
        CartItem(
          id: 'item2',
          name: 'Cappuccino',
          price: 4.50,
          image: 'assets/images/cappuccino.png',
          cafeteriaName: 'Beanos',
          buildingName: 'Main Campus',
          quantity: 2,
          customizations: [
            Customization(name: 'Oat milk', price: 0.75),
          ],
        ),
      ],
      subtotal: 17.99,
      tax: 1.80,
      total: 19.79,
      status: OrderStatus.preparing,
      cafeteriaId: 'cafe1',
      cafeteriaName: 'Beanos',
    ),
    Order(
      id: 'order2',
      userId: 'user1',
      orderTime: DateTime.now().subtract(const Duration(days: 1)),
      pickupTime: DateTime.now().subtract(const Duration(days: 1)).add(const Duration(hours: 1)),
      items: [
        CartItem(
          id: 'item3',
          name: 'Cinnamon Roll',
          price: 5.99,
          image: 'assets/images/cinnamon-roll.png',
          cafeteriaName: 'Cinnamon Factory',
          buildingName: 'B04 Building',
          quantity: 2,
          customizations: [
            Customization(name: 'Extra icing', price: 0.50),
          ],
        ),
      ],
      subtotal: 11.98,
      tax: 1.20,
      total: 13.18,
      status: OrderStatus.completed,
      cafeteriaId: 'cafe2',
      cafeteriaName: 'Cinnamon Factory',
    ),
  ];
}*/

final List<Cafeteria> cafeterias = [
  const Cafeteria(
    id: 'cafe1',
    name: 'Beanos',
    image: 'assets/images/beanos.png',
    location: 'Main Campus',
    description: 'Coffee shop with fresh pastries and sandwiches.',
    openingHours: '7:00 AM - 7:00 PM',
    isOpen: true,
    phoneNumber: '+20 ************',
    email: '<EMAIL>',
    cuisineType: 'Coffee & Bakery',
    rating: 4.6,
    estimatedTime: 15,
  ),
  const Cafeteria(
    id: 'cafe2',
    name: 'Cinnamon Factory',
    image: 'assets/images/cinnamon-factory.png',
    location: 'B04 Building',
    description: 'Bakery specializing in cinnamon rolls and pastries.',
    openingHours: '8:00 AM - 6:00 PM',
    isOpen: true,
    phoneNumber: '+20 ************',
    email: '<EMAIL>',
    cuisineType: 'Bakery',
    rating: 4.7,
    estimatedTime: 10,
  ),
  const Cafeteria(
    id: 'cafe3',
    name: 'Nova',
    image: 'assets/images/nova.png',
    location: 'Science Building',
    description: 'Modern cafeteria with a variety of healthy options.',
    openingHours: '8:00 AM - 8:00 PM',
    isOpen: false,
    phoneNumber: '+20 ************',
    email: '<EMAIL>',
    cuisineType: 'Modern & Healthy',
    rating: 4.5,
    estimatedTime: 20,
  ),
  const Cafeteria(
    id: 'cafe4',
    name: 'Pizza Corner',
    image: 'assets/images/pizza-corner.png',
    location: 'Student Center',
    description: 'Freshly baked pizzas and Italian dishes.',
    openingHours: '11:00 AM - 9:00 PM',
    isOpen: true,
    phoneNumber: '+20 ************',
    email: '<EMAIL>',
    cuisineType: 'Italian',
    rating: 4.8,
    estimatedTime: 25,
  ),
  const Cafeteria(
    id: 'cafe5',
    name: 'Green Bites',
    image: 'assets/images/green-bites.png',
    location: 'Health Sciences Building',
    description: 'Healthy salads, wraps, and smoothies.',
    openingHours: '8:00 AM - 5:00 PM',
    isOpen: true,
    phoneNumber: '+20 ************',
    email: '<EMAIL>',
    cuisineType: 'Healthy & Vegan',
    rating: 4.4,
    estimatedTime: 15,
  ),
];

final List<MenuItem> menuItems = [
  // Beanos
  const MenuItem(
    id: 'item1',
    name: 'Cappuccino',
    description: 'Rich espresso with steamed milk and foam.',
    price: 4.50,
    image: 'assets/images/cappuccino.png',
    cafeteriaId: 'cafe1',
    rating: 4.7,
    category: 'Coffee',
  ),
  const MenuItem(
    id: 'item2',
    name: 'Americano',
    description: 'Espresso with hot water for a smooth taste.',
    price: 3.50,
    image: 'assets/images/americano.png',
    cafeteriaId: 'cafe1',
    rating: 4.5,
    category: 'Coffee',
  ),
  const MenuItem(
    id: 'item3',
    name: 'Croissant',
    description: 'Buttery, flaky French pastry.',
    price: 2.50,
    image: 'assets/images/croissant.png',
    cafeteriaId: 'cafe1',
    rating: 4.6,
    category: 'Pastries',
  ),
  const MenuItem(
    id: 'item4',
    name: 'Turkey Club Sandwich',
    description: 'Turkey, lettuce, tomato, and mayo on toasted bread.',
    price: 6.99,
    image: 'assets/images/turkey-club.png',
    cafeteriaId: 'cafe1',
    rating: 4.4,
    category: 'Sandwiches',
  ),
  const MenuItem(
    id: 'item5',
    name: 'Avocado Toast',
    description: 'Fresh avocado on sourdough bread.',
    price: 8.99,
    image: 'assets/images/avocado-toast.png',
    cafeteriaId: 'cafe1',
    rating: 4.8,
    category: 'Sandwiches',
  ),
  // Cinnamon Factory
  const MenuItem(
    id: 'item6',
    name: 'Classic Cinnamon Roll',
    description: 'Warm cinnamon roll with sweet icing.',
    price: 3.99,
    image: 'assets/images/cinnamon-roll.png',
    cafeteriaId: 'cafe2',
    rating: 4.9,
    category: 'Cinnamon Rolls',
  ),
  const MenuItem(
    id: 'item7',
    name: 'Caramel Pecan Roll',
    description: 'Cinnamon roll topped with caramel and pecans.',
    price: 4.99,
    image: 'assets/images/caramel-pecan-roll.png',
    cafeteriaId: 'cafe2',
    rating: 4.7,
    category: 'Cinnamon Rolls',
  ),
  const MenuItem(
    id: 'item8',
    name: 'Chocolate Cake',
    description: 'Rich chocolate cake with fudge icing.',
    price: 5.50,
    image: 'assets/images/chocolate-cake.png',
    cafeteriaId: 'cafe2',
    rating: 4.6,
    category: 'Cakes',
  ),
  const MenuItem(
    id: 'item9',
    name: 'Apple Danish',
    description: 'Pastry filled with apple and cinnamon.',
    price: 3.25,
    image: 'assets/images/apple-danish.png',
    cafeteriaId: 'cafe2',
    rating: 4.5,
    category: 'Pastries',
  ),
  // Nova
  const MenuItem(
    id: 'item10',
    name: 'Quinoa Bowl',
    description: 'Quinoa, roasted veggies, and tahini dressing.',
    price: 10.99,
    image: 'assets/images/quinoa-bowl.png',
    cafeteriaId: 'cafe3',
    rating: 4.7,
    category: 'Bowls',
  ),
  const MenuItem(
    id: 'item11',
    name: 'Vegan Burger',
    description: 'Plant-based patty, lettuce, tomato, vegan mayo.',
    price: 9.99,
    image: 'assets/images/vegan-burger.png',
    cafeteriaId: 'cafe3',
    rating: 4.6,
    category: 'Vegan',
  ),
  const MenuItem(
    id: 'item12',
    name: 'Grilled Chicken Salad',
    description: 'Grilled chicken, greens, cherry tomatoes, vinaigrette.',
    price: 8.99,
    image: 'assets/images/chicken-salad.png',
    cafeteriaId: 'cafe3',
    rating: 4.5,
    category: 'Salads',
  ),
  // Pizza Corner
  const MenuItem(
    id: 'item13',
    name: 'Margherita Pizza',
    description: 'Classic pizza with tomato, mozzarella, and basil.',
    price: 7.99,
    image: 'assets/images/margherita-pizza.png',
    cafeteriaId: 'cafe4',
    rating: 4.8,
    category: 'Pizza',
  ),
  const MenuItem(
    id: 'item14',
    name: 'Pepperoni Pizza',
    description: 'Pepperoni, mozzarella, and tomato sauce.',
    price: 8.99,
    image: 'assets/images/pepperoni-pizza.png',
    cafeteriaId: 'cafe4',
    rating: 4.7,
    category: 'Pizza',
  ),
  const MenuItem(
    id: 'item15',
    name: 'Garlic Bread',
    description: 'Toasted bread with garlic butter.',
    price: 3.50,
    image: 'assets/images/garlic-bread.png',
    cafeteriaId: 'cafe4',
    rating: 4.4,
    category: 'Sides',
  ),
  // Green Bites
  const MenuItem(
    id: 'item16',
    name: 'Caesar Salad',
    description: 'Romaine, parmesan, croutons, Caesar dressing.',
    price: 7.50,
    image: 'assets/images/caesar-salad.png',
    cafeteriaId: 'cafe5',
    rating: 4.6,
    category: 'Salads',
  ),
  const MenuItem(
    id: 'item17',
    name: 'Falafel Wrap',
    description: 'Falafel, hummus, veggies in a whole wheat wrap.',
    price: 6.99,
    image: 'assets/images/falafel-wrap.png',
    cafeteriaId: 'cafe5',
    rating: 4.7,
    category: 'Wraps',
  ),
  const MenuItem(
    id: 'item18',
    name: 'Mango Smoothie',
    description: 'Fresh mango blended with yogurt.',
    price: 4.99,
    image: 'assets/images/mango-smoothie.png',
    cafeteriaId: 'cafe5',
    rating: 4.8,
    category: 'Smoothies',
  ),
];
