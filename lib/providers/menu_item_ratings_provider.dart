import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/services/rating_service.dart';

class MenuItemRatingsProvider extends ChangeNotifier {
  static const String _storageKey = 'menu_item_ratings_cache';
  Map<String, List<Map<String, dynamic>>> _ratings = {};
  Map<String, Map<String, dynamic>> _averageRatings = {};
  bool _initialized = false;
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  // Get ratings for a specific menu item
  List<Map<String, dynamic>> getRatingsForMenuItem(String menuItemId) {
    return _ratings[menuItemId] ?? [];
  }

  // Get average rating for a menu item
  double getAverageRatingForMenuItem(String menuItemId) {
    // First check if we have the average rating in our cache
    if (_averageRatings.containsKey(menuItemId)) {
      return _averageRatings[menuItemId]!['rating'] ?? 0.0;
    }
    
    // Otherwise calculate from local ratings
    final menuItemRatings = getRatingsForMenuItem(menuItemId);
    if (menuItemRatings.isEmpty) return 0.0;

    final sum = menuItemRatings.fold(
        0.0, (double sum, rating) => sum + (rating['rating'] ?? 0.0));
    return sum / menuItemRatings.length;
  }

  // Get total ratings count for a menu item
  int getTotalRatingsForMenuItem(String menuItemId) {
    // First check if we have the count in our cache
    if (_averageRatings.containsKey(menuItemId)) {
      return _averageRatings[menuItemId]!['total_ratings'] ?? 0;
    }
    
    // Otherwise count local ratings
    return getRatingsForMenuItem(menuItemId).length;
  }

  // Add a new rating
  Future<void> addRating({
    required String menuItemId,
    required double rating,
    String? comment,
    String? orderId,
    required BuildContext context,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();
      
      // Add to Supabase
      await RatingService.submitMenuItemRating(
        menuItemId: menuItemId,
        rating: rating,
        comment: comment,
        orderId: orderId,
        context: context,
      );
      
      // Refresh ratings from server
      await fetchRatingsForMenuItem(menuItemId);
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error adding menu item rating: $e');
    }
  }

  // Fetch ratings for a specific menu item from Supabase
  Future<void> fetchRatingsForMenuItem(String menuItemId) async {
    try {
      _isLoading = true;
      notifyListeners();
      
      // Get ratings from Supabase
      final fetchedRatings = await RatingService.getMenuItemRatings(menuItemId);
      
      // Update local cache
      _ratings[menuItemId] = fetchedRatings;
      
      // Calculate average rating
      if (fetchedRatings.isNotEmpty) {
        final sum = fetchedRatings.fold(
            0.0, (double sum, rating) => sum + (rating['rating'] ?? 0.0));
        final avg = sum / fetchedRatings.length;
        
        _averageRatings[menuItemId] = {
          'rating': avg,
          'total_ratings': fetchedRatings.length,
        };
      } else {
        _averageRatings[menuItemId] = {
          'rating': 0.0,
          'total_ratings': 0,
        };
      }
      
      // Save to local storage
      await _saveRatings();
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error fetching menu item ratings: $e');
    }
  }

  // Initialize - load ratings from storage
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      _isLoading = true;
      notifyListeners();
      
      // Load from local storage first for quick display
      final prefs = await SharedPreferences.getInstance();
      final ratingsJson = prefs.getString(_storageKey);

      if (ratingsJson != null) {
        final Map<String, dynamic> decodedMap = jsonDecode(ratingsJson);
        _ratings = {};
        
        decodedMap.forEach((key, value) {
          _ratings[key] = List<Map<String, dynamic>>.from(value);
        });
      }
      
      _initialized = true;
      _isLoading = false;
      notifyListeners();
      
      // We don't fetch all ratings here since that could be expensive
      // Instead, we'll fetch ratings for specific menu items when needed
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error initializing menu item ratings: $e');
    }
  }

  // Save ratings to storage
  Future<void> _saveRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ratingsJson = jsonEncode(_ratings);
      await prefs.setString(_storageKey, ratingsJson);
    } catch (e) {
      debugPrint('Error saving menu item ratings: $e');
    }
  }

  // Clear all ratings (for testing)
  Future<void> clearAllRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
      _ratings = {};
      _averageRatings = {};
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing menu item ratings: $e');
    }
  }
}
