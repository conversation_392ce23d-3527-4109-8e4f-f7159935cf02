import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/user.dart';
import 'package:unieatsappv0/services/user_service.dart';
import 'package:unieatsappv0/services/local_storage_service.dart';

class AuthProvider extends ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  final UserService _userService = UserService();
  final LocalStorageService _storageService = LocalStorageService();

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;
  String? get error => _error;

  Future<void> init() async {
    await _storageService.init();
    await _userService.init();

    // Try to load saved credentials
    final savedCredentials = await _storageService.getLoginInfo();
    if (savedCredentials != null &&
        savedCredentials['email'] != null &&
        savedCredentials['password'] != null) {
      // Try to login with saved credentials
      await login(
        savedCredentials['email']!,
        savedCredentials['password']!,
      );
    }
  }

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (!email.endsWith('@students.eui.edu.eg')) {
        _error = 'Please use your student email (@students.eui.edu.eg)';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      // Get user profile from UserService
      final profile = await _userService.getUserProfile();

      if (profile.isEmpty) {
        _error = 'User not found';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      _currentUser = User.fromJson(profile);

      // Save credentials to local storage
      await _storageService.saveLoginInfo(email, password);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> register(
      String name, String email, String phone, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (!email.endsWith('@students.eui.edu.eg')) {
        _error = 'Please use your student email (@students.eui.edu.eg)';
        _isLoading = false;
        notifyListeners();
        return false;
      }

      // Save user profile to UserService
      await _userService.saveUserProfile(
        name: name,
        email: email,
        phone: phone,
      );

      _currentUser = User(
        id: '1',
        name: name,
        email: email,
        phone: phone,
      );

      // Save credentials to local storage
      await _storageService.saveLoginInfo(email, password);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Clear credentials from local storage
      await _storageService.clearLoginInfo();

      _currentUser = null;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    String? phone,
  }) async {
    if (_currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // Save to UserService
      await _userService.saveUserProfile(
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        phone: phone,
      );

      // Update current user
      _currentUser = _currentUser!.copyWith(
        name: name,
        email: email,
        phone: phone,
      );

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Check if user has penalties
  bool get hasPenalty => _currentUser?.hasPenalty ?? false;
  double get penaltyAmount => _currentUser?.penaltyAmount ?? 0.0;
}
