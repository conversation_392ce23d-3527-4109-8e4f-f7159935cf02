import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/services/supabase_order_service.dart';

import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/utils/order_status_utils.dart';

class OrderHistoryProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  static const String _storageKey = 'order_history';
  final SupabaseOrderService _orderService = SupabaseOrderService();

  List<Order> get orders => [..._orders];
  bool get isLoading => _isLoading;

  /// Clear all order history data
  void _clearOrderData() {
    _orders.clear();
    debugPrint('OrderHistoryProvider: Cleared all order data');
  }

  // Initialize the provider by loading orders from Supabase and local storage
  Future<void> loadOrders() async {
    _isLoading = true;
    notifyListeners();

    // Clear existing data first
    _clearOrderData();

    try {
      debugPrint('🔄 Loading order history from Supabase...');

      // Load orders from Supabase (don't fall back to local storage)
      final supabaseOrders = await _orderService.getUserOrders();
      debugPrint('✅ Loaded ${supabaseOrders.length} orders from Supabase for current user');

      if (supabaseOrders.isNotEmpty) {
        // Debug: Check if orders have items
        for (final order in supabaseOrders.take(3)) {
          debugPrint('🔍 Order ${order.id}: has ${order.items?.length ?? 0} items');
          if (order.items != null) {
            for (final item in order.items!.take(2)) {
              debugPrint('   - Item: ${item.menuItemId} (price: ${item.price}, qty: ${item.quantity})');
            }
          }
        }

        // Convert Supabase orders to local Order model
        _orders = await _convertSupabaseOrdersToLocal(supabaseOrders);

        // Sort orders by date (newest first)
        _orders.sort((a, b) => b.orderDate.compareTo(a.orderDate));

        // Save to local storage for offline access
        await _saveOrdersToStorage();
      } else {
        debugPrint('📱 No orders found in Supabase for current user - showing empty state');
        // Don't fall back to local storage - just show empty state
        _orders = [];
      }

      debugPrint('📋 Total orders loaded: ${_orders.length}');
    } catch (e) {
      debugPrint('❌ Error loading orders from Supabase: $e');
      // Don't fall back to local storage - just show empty state
      _orders = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add a new order to history
  Future<void> addOrder(Order order) async {
    _orders.insert(0, order); // Add to the beginning of the list
    notifyListeners();
    await _saveOrdersToStorage();
  }

  // Save orders to local storage
  Future<void> _saveOrdersToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String ordersJson =
          json.encode(_orders.map((order) => order.toJson()).toList());
      await prefs.setString(_storageKey, ordersJson);
    } catch (e) {
      debugPrint('Error saving orders: $e');
    }
  }

  // Get orders for a specific date
  List<Order> getOrdersForDate(DateTime date) {
    return _orders.where((order) {
      return order.dateTime.year == date.year &&
          order.dateTime.month == date.month &&
          order.dateTime.day == date.day;
    }).toList();
  }

  // Get orders by status
  List<Order> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  // Get recent orders (last 30 days)
  List<Order> getRecentOrders() {
    final DateTime thirtyDaysAgo =
        DateTime.now().subtract(const Duration(days: 30));
    return _orders
        .where((order) => order.dateTime.isAfter(thirtyDaysAgo))
        .toList();
  }

  // Update an existing order
  Future<void> updateOrder(Order updatedOrder) async {
    final index = _orders
        .indexWhere((order) => order.orderNumber == updatedOrder.orderNumber);
    if (index != -1) {
      _orders[index] = updatedOrder;
      notifyListeners();
      await _saveOrdersToStorage();
    }
  }

  // Clear all order history
  Future<void> clearOrderHistory() async {
    _orders = [];
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
    } catch (e) {
      debugPrint('Error clearing order history: $e');
    }
  }

  // Convert Supabase orders to local Order model
  Future<List<Order>> _convertSupabaseOrdersToLocal(List<SupabaseOrder> supabaseOrders) async {
    final List<Order> localOrders = [];

    for (final supabaseOrder in supabaseOrders) {
      try {
        // Convert order items
        final List<CartItem> orderItems = [];
        if (supabaseOrder.items != null) {
          debugPrint('🔄 Converting ${supabaseOrder.items!.length} items for order ${supabaseOrder.id}');
          for (final item in supabaseOrder.items!) {
            debugPrint('   📦 Converting item: ${item.menuItemId} (${item.quantity}x ${item.price})');
            final cartItem = CartItem(
              id: item.menuItemId,
              name: item.menuItemName ?? 'Menu Item', // Use actual name from database
              price: item.price,
              image: item.menuItemImageUrl ?? 'assets/images/placeholder.png', // Use actual image URL
              quantity: item.quantity,
              cafeteriaName: item.cafeteriaName ?? 'Unknown Cafeteria', // Use actual cafeteria name
              buildingName: item.cafeteriaLocation ?? 'Unknown Location', // Use actual location
              notes: item.notes ?? '',
              customizations: {},
            );
            orderItems.add(cartItem);
            debugPrint('   ✅ Added cart item: ${cartItem.name} from ${cartItem.cafeteriaName}');
          }
        } else {
          debugPrint('⚠️ Order ${supabaseOrder.id} has no items');
        }

        // Convert to local Order model
        final localOrder = Order(
          id: supabaseOrder.id,
          orderNumber: supabaseOrder.orderNumber ?? supabaseOrder.id.substring(0, 8).toUpperCase(),
          userId: supabaseOrder.userId,
          orderDate: supabaseOrder.createdAt,
          items: orderItems,
          totalPrice: supabaseOrder.totalAmount,
          pickupTime: supabaseOrder.pickupTime ?? 'ASAP',
          status: OrderStatusUtils.getDisplayStatus(supabaseOrder.status),
          paymentMethod: supabaseOrder.paymentMethod ?? 'card',
          rating: supabaseOrder.rating,
          comment: supabaseOrder.reviewComment,
        );

        localOrders.add(localOrder);
      } catch (e) {
        debugPrint('❌ Error converting order ${supabaseOrder.id}: $e');
      }
    }

    return localOrders;
  }
}
