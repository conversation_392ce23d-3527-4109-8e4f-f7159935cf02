import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:unieatsappv0/config/supabase_config.dart';

class AppNotification {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final bool isRead;
  final String? orderId;
  final String? type; // 'order', 'promo', 'system'

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    this.isRead = false,
    this.orderId,
    this.type = 'system',
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    String? orderId,
    String? type,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      orderId: orderId ?? this.orderId,
      type: type ?? this.type,
    );
  }
}

class NotificationProvider with ChangeNotifier {
  final List<AppNotification> _notifications = [];
  bool _isLoading = false;
  String? _error;
  bool _initialized = false;

  NotificationProvider() {
    // Initialize by fetching notifications from Supabase
    _initialize();
  }

  List<AppNotification> get notifications => List.unmodifiable(_notifications);
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  // Initialize by fetching notifications from Supabase
  Future<void> _initialize() async {
    if (_initialized) return;

    await fetchNotifications();
    _setupRealtimeSubscription();
    _initialized = true;
  }

  // Fetch notifications from Supabase
  Future<void> fetchNotifications() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Get current user
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) {
        _error = 'User not logged in';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Fetch notifications from Supabase
      final response = await SupabaseConfig.client
          .from('notifications')
          .select('*')
          .eq('user_id', currentUser.id)
          .order('created_at', ascending: false);

      // Clear existing notifications
      _notifications.clear();

      // Add fetched notifications
      for (final item in response as List) {
        _notifications.add(
          AppNotification(
            id: item['id'],
            title: item['title'],
            message: item['message'],
            timestamp: DateTime.parse(item['created_at']),
            isRead: item['is_read'] ?? false,
            orderId: item['related_order_id'],
            type: 'order', // Default to order type
          ),
        );
      }
    } catch (e) {
      _error = 'Error fetching notifications: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Set up real-time subscription for notifications
  void _setupRealtimeSubscription() {
    try {
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) return;

      // Subscribe to notifications table for this user
      SupabaseConfig.subscribeToTable(
        'notifications',
        (payload) {
          debugPrint('📧 Notification real-time update received: $payload');

          // Check if this notification is for the current user
          if (payload['user_id'] == currentUser.id) {
            final notificationId = payload['id'];

            // Check if notification already exists to prevent duplicates
            final existingIndex = _notifications.indexWhere((n) => n.id == notificationId);

            if (existingIndex == -1) {
              // Only add if it doesn't exist
              final notification = AppNotification(
                id: notificationId,
                title: payload['title'],
                message: payload['message'],
                timestamp: DateTime.parse(payload['created_at']),
                isRead: payload['is_read'] ?? false,
                orderId: payload['related_order_id'],
                type: payload['type'] ?? 'order',
              );

              // Add to the beginning of the list
              _notifications.insert(0, notification);
              notifyListeners();
              debugPrint('✅ Added new notification: ${notification.title}');
            } else {
              // Update existing notification if it changed
              final existingNotification = _notifications[existingIndex];
              final updatedNotification = AppNotification(
                id: notificationId,
                title: payload['title'],
                message: payload['message'],
                timestamp: DateTime.parse(payload['created_at']),
                isRead: payload['is_read'] ?? false,
                orderId: payload['related_order_id'],
                type: payload['type'] ?? 'order',
              );

              // Only update if something actually changed
              if (existingNotification.isRead != updatedNotification.isRead ||
                  existingNotification.title != updatedNotification.title ||
                  existingNotification.message != updatedNotification.message) {
                _notifications[existingIndex] = updatedNotification;
                notifyListeners();
                debugPrint('🔄 Updated existing notification: ${updatedNotification.title}');
              } else {
                debugPrint('⚠️ Notification already exists and unchanged, skipping: $notificationId');
              }
            }
          }
        },
      );
    } catch (e) {
      debugPrint('Error setting up real-time subscription: $e');
    }
  }

  // Add a notification locally and to Supabase
  Future<void> addNotification(AppNotification notification) async {
    try {
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) return;

      // Add to Supabase
      await SupabaseConfig.client.from('notifications').insert({
        'id': notification.id,
        'user_id': currentUser.id,
        'title': notification.title,
        'message': notification.message,
        'is_read': notification.isRead,
        'related_order_id': notification.orderId,
        'created_at': notification.timestamp.toIso8601String(),
      });

      // Add locally (will be added via real-time subscription)
    } catch (e) {
      debugPrint('Error adding notification: $e');
    }
  }

  // Add an order status notification
  Future<void> addOrderStatusNotification(String orderNumber, String status,
      {String? cafeteriaName, String? orderId}) async {
    String title;
    String message;

    switch (status) {
      case 'Ready for pickup':
        title = 'Order Ready for Pickup';
        message =
            'Your order #$orderNumber is ready for pickup${cafeteriaName != null ? ' at $cafeteriaName' : ''}.';
        break;
      case 'Preparing':
        title = 'Order Being Prepared';
        message =
            'Your order #$orderNumber is now being prepared${cafeteriaName != null ? ' at $cafeteriaName' : ''}.';
        break;
      case 'Completed':
        title = 'Order Completed';
        message =
            'Your order #$orderNumber has been completed. Thank you for using UniEats!';
        break;
      case 'Cancelled':
        title = 'Order Cancelled';
        message = 'Your order #$orderNumber has been cancelled.';
        break;
      case 'Order Received':
        title = 'Order Received';
        message = 'Your order has been received and is being processed.';
        break;
      default:
        title = 'Order Update';
        message = 'Your order #$orderNumber status: $status';
    }

    final notification = AppNotification(
      id: const Uuid().v4(),
      title: title,
      message: message,
      timestamp: DateTime.now(),
      orderId: orderId, // Use the actual order ID (UUID) instead of order number
      type: 'order',
    );

    await addNotification(notification);
  }

  // Mark a notification as read locally and in Supabase
  Future<void> markAsRead(String id) async {
    try {
      // Update in Supabase
      await SupabaseConfig.client
          .from('notifications')
          .update({'is_read': true}).eq('id', id);

      // Update locally
      final index = _notifications.indexWhere((n) => n.id == id);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) return;

      // Update in Supabase
      await SupabaseConfig.client
          .from('notifications')
          .update({'is_read': true}).eq('user_id', currentUser.id);

      // Update locally
      for (var i = 0; i < _notifications.length; i++) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    }
  }

  // Remove a notification
  Future<void> removeNotification(String id) async {
    try {
      // Delete from Supabase
      await SupabaseConfig.client.from('notifications').delete().eq('id', id);

      // Remove locally
      _notifications.removeWhere((n) => n.id == id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error removing notification: $e');
    }
  }

  // Clear all notifications
  Future<void> clearAll() async {
    try {
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) return;

      // Delete from Supabase
      await SupabaseConfig.client
          .from('notifications')
          .delete()
          .eq('user_id', currentUser.id);

      // Clear locally
      _notifications.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing all notifications: $e');
    }
  }

  // Mark a notification as unread
  Future<void> markAsUnread(String id) async {
    try {
      // Update in Supabase
      await SupabaseConfig.client
          .from('notifications')
          .update({'is_read': false}).eq('id', id);

      // Update locally
      final index = _notifications.indexWhere((n) => n.id == id);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: false);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error marking notification as unread: $e');
    }
  }

  // Delete a notification
  Future<void> deleteNotification(String id) async {
    await removeNotification(id);
  }

  // Clear old test notifications (older than 1 hour)
  Future<void> clearOldTestNotifications() async {
    try {
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) return;

      final oneHourAgo = DateTime.now().subtract(const Duration(hours: 1));

      // Delete old test notifications from Supabase
      await SupabaseConfig.client
          .from('notifications')
          .delete()
          .eq('user_id', currentUser.id)
          .or('title.eq.Test Notification,message.ilike.%test%')
          .lt('created_at', oneHourAgo.toIso8601String());

      // Refresh local notifications
      await fetchNotifications();
    } catch (e) {
      debugPrint('Error clearing old test notifications: $e');
    }
  }

  // Clear old notifications (older than 2 hours) to fix timestamp issues
  Future<void> clearOldNotifications() async {
    try {
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) return;

      final twoHoursAgo = DateTime.now().subtract(const Duration(hours: 2));

      debugPrint('🧹 Clearing old notifications older than: ${twoHoursAgo.toIso8601String()}');

      // Delete old notifications from Supabase
      final result = await SupabaseConfig.client
          .from('notifications')
          .delete()
          .eq('user_id', currentUser.id)
          .lt('created_at', twoHoursAgo.toIso8601String());

      debugPrint('🧹 Cleared old notifications result: $result');

      // Refresh local notifications
      await fetchNotifications();
    } catch (e) {
      debugPrint('Error clearing old notifications: $e');
    }
  }

  // Refresh notifications
  Future<void> refresh() async {
    await fetchNotifications();
  }
}
