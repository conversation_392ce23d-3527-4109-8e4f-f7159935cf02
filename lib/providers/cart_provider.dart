import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/cart_item.dart';

class CartProvider with ChangeNotifier {
  final Map<String, CartItem> _items = {};

  List<CartItem> get items {
    return _items.values.toList();
  }

  int get itemCount {
    return _items.length;
  }

  double get subtotal {
    double total = 0.0;
    _items.forEach((key, cartItem) {
      total += cartItem.calculateItemTotal();
    });
    return total;
  }

  double get serviceFee {
    final fee = subtotal * 0.04;
    return fee > 20.0 ? 20.0 : fee;
  }

  double get total {
    return subtotal + serviceFee;
  }

  // Formatted currency strings
  String get formattedSubtotal => '${subtotal.toStringAsFixed(2)} EGP';
  String get formattedServiceFee => '${serviceFee.toStringAsFixed(2)} EGP';
  String get formattedTotal => '${total.toStringAsFixed(2)} EGP';

  void addItem(CartItem cartItem) {
    if (_items.containsKey(cartItem.id)) {
      // Update existing item - preserve the existing notes if new item has no notes
      final existingItem = _items[cartItem.id]!;
      final notesToUse = cartItem.notes?.isNotEmpty == true ? cartItem.notes : existingItem.notes;

      _items.update(
        cartItem.id,
        (existingCartItem) => CartItem(
          id: existingCartItem.id,
          menuItem: existingCartItem.menuItem,
          quantity: existingCartItem.quantity + cartItem.quantity,
          customizations: cartItem.customizations,
          notes: notesToUse, // Use new notes if provided, otherwise keep existing
          name: existingCartItem.name,
          price: existingCartItem.price,
          image: existingCartItem.image,
          cafeteriaName: existingCartItem.cafeteriaName,
          buildingName: existingCartItem.buildingName,
        ),
      );
    } else {
      // Add new item
      _items.putIfAbsent(cartItem.id, () => cartItem);
    }
    notifyListeners();
  }

  void removeItem(String id) {
    _items.remove(id);
    notifyListeners();
  }

  void updateQuantity(String id, int quantity) {
    if (_items.containsKey(id)) {
      _items.update(
        id,
        (existingCartItem) => CartItem(
          id: existingCartItem.id,
          menuItem: existingCartItem.menuItem,
          quantity: quantity,
          customizations: existingCartItem.customizations,
          notes: existingCartItem.notes, // Preserve the notes
          name: existingCartItem.name,
          price: existingCartItem.price,
          image: existingCartItem.image,
          cafeteriaName: existingCartItem.cafeteriaName,
          buildingName: existingCartItem.buildingName,
        ),
      );
      notifyListeners();
    }
  }

  void clearCart() {
    _items.clear();
    notifyListeners();
  }
}
