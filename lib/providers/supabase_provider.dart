import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:unieatsappv0/services/simple_auth_service.dart';
import 'package:unieatsappv0/services/supabase_cafeteria_service.dart';
import 'package:unieatsappv0/services/supabase_menu_service.dart';
import 'package:unieatsappv0/services/supabase_order_service.dart';
import 'package:unieatsappv0/config/supabase_config.dart';

class SupabaseProvider extends ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();
  final SimpleAuthService _userService = SimpleAuthService();
  final SupabaseCafeteriaService _cafeteriaService = SupabaseCafeteriaService();
  final SupabaseMenuService _menuService = SupabaseMenuService();
  final SupabaseOrderService _orderService = SupabaseOrderService();

  // User state
  UserProfile? _currentUser;
  bool _isLoading = false;
  bool _isLoadingCafeterias = false;
  bool _isLoadingMenuItems = false;
  bool _isLoadingOrders = false;
  bool _isProcessingCheckout = false;
  String? _error;
  String? _loadingMessage;

  // Cafeteria state
  List<SupabaseCafeteria> _cafeterias = [];
  SupabaseCafeteria? _selectedCafeteria;

  // Menu state
  List<SupabaseMenuItem> _menuItems = [];
  Map<String, List<SupabaseMenuItem>> _menuItemsByCategory = {};

  // Order state
  List<SupabaseOrder> _userOrders = [];
  SupabaseOrder? _currentOrder;

  // Real-time subscriptions
  final Map<String, RealtimeChannel> _subscriptions = {};

  // Getters
  UserProfile? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isLoadingCafeterias => _isLoadingCafeterias;
  bool get isLoadingMenuItems => _isLoadingMenuItems;
  bool get isLoadingOrders => _isLoadingOrders;
  bool get isProcessingCheckout => _isProcessingCheckout;
  String? get error => _error;
  String? get loadingMessage => _loadingMessage;
  bool get isAuthenticated => _currentUser != null;

  List<SupabaseCafeteria> get cafeterias => _cafeterias;
  SupabaseCafeteria? get selectedCafeteria => _selectedCafeteria;

  List<SupabaseMenuItem> get menuItems => _menuItems;
  Map<String, List<SupabaseMenuItem>> get menuItemsByCategory =>
      _menuItemsByCategory;

  List<SupabaseOrder> get userOrders => _userOrders;
  SupabaseOrder? get currentOrder => _currentOrder;

  // Initialize the provider
  Future<void> initialize() async {
    debugPrint('SupabaseProvider: initialize() called');
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Initialize Supabase
      debugPrint('SupabaseProvider: Initializing Supabase service');
      await _supabaseService.init();
      debugPrint('SupabaseProvider: Supabase service initialized');

      // Test the connection
      try {
        final testResponse =
            await _supabaseService.client.from('cafeterias').select().limit(1);
        debugPrint(
            'SupabaseProvider: Test query successful: ${testResponse.length} cafeterias found');
      } catch (e) {
        debugPrint('SupabaseProvider: Test query failed: $e');
        // Try to reinitialize with direct config
        await Supabase.initialize(
          url: 'https://lqtnaxvqkoynaziiinqh.supabase.co',
          anonKey:
              'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA',
          debug: true,
        );
        debugPrint(
            'SupabaseProvider: Reinitialized Supabase with direct config');
      }

      // Set up real-time subscriptions
      debugPrint('SupabaseProvider: Setting up real-time subscriptions');
      _setupRealtimeSubscriptions();
      debugPrint('SupabaseProvider: Real-time subscriptions set up');

      // Check if user is already logged in
      debugPrint('SupabaseProvider: Checking current user');
      await _checkCurrentUser();
      debugPrint('SupabaseProvider: Current user checked');

      // Load cafeterias
      debugPrint('SupabaseProvider: Loading cafeterias');
      await loadCafeterias();
      debugPrint('SupabaseProvider: Cafeterias loaded');
    } catch (e) {
      _error = 'Failed to initialize Supabase: $e';
      debugPrint('SupabaseProvider: Error initializing: $_error');
    } finally {
      _isLoading = false;
      notifyListeners();
      debugPrint('SupabaseProvider: initialize() completed');
    }
  }

  // Set up real-time subscriptions for key tables
  void _setupRealtimeSubscriptions() {
    try {
      debugPrint('Setting up real-time subscriptions...');

      // Subscribe to cafeterias table
      _subscriptions['cafeterias'] = SupabaseConfig.subscribeToTable(
        'cafeterias',
        (payload) {
          debugPrint('Cafeteria update received: ${payload.toString()}');
          loadCafeterias(); // Reload cafeterias when data changes
        },
      );
      debugPrint('Subscribed to cafeterias table');

      // Subscribe to menu_items table
      _subscriptions['menu_items'] = SupabaseConfig.subscribeToTable(
        'menu_items',
        (payload) {
          debugPrint('Menu item update received: ${payload.toString()}');
          if (_selectedCafeteria != null) {
            loadMenuItems(
                _selectedCafeteria!.id); // Reload menu items when data changes
          }
        },
      );
      debugPrint('Subscribed to menu_items table');

      // Subscribe to orders table
      _subscriptions['orders'] = SupabaseConfig.subscribeToTable(
        'orders',
        (payload) {
          debugPrint('Order update received: ${payload.toString()}');
          if (_currentUser != null) {
            loadUserOrders(); // Reload orders when data changes
          }
        },
      );
      debugPrint('Subscribed to orders table');

      debugPrint('Real-time subscriptions set up successfully');
    } catch (e) {
      debugPrint('Error setting up real-time subscriptions: $e');
    }
  }

  // Check if user is already logged in
  Future<void> _checkCurrentUser() async {
    try {
      final currentUser = _userService.currentUser;
      if (currentUser != null && currentUser.email != null) {
        // Try to get the user profile by logging in with empty password (session check)
        final profile = await _userService.login(
          email: currentUser.email!,
          password: '', // Empty password for session check
        );
        if (profile != null) {
          _currentUser = profile;
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error checking current user: $e');
    }
  }

  // User authentication methods
  Future<bool> register({
    required String email,
    required String password,
    required String fullName,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('SupabaseProvider: Registering user with email: $email');

      // Try to register the user
      final user = await _userService.register(
        email: email,
        password: password,
        fullName: fullName,
      );

      if (user != null) {
        debugPrint('SupabaseProvider: User registered successfully');
        _currentUser = user;

        // Load cafeterias after successful registration
        await loadCafeterias();

        notifyListeners();
        return true;
      } else {
        debugPrint('SupabaseProvider: Failed to register user (null returned)');
        _error = 'Failed to register user. Please try again.';
        notifyListeners();
        return false;
      }
    } catch (e) {
      // Handle specific error cases
      String errorMessage = 'Registration failed';

      if (e.toString().contains('already registered')) {
        errorMessage =
            'This email is already registered. Please try logging in instead.';
      } else if (e.toString().contains('invalid email')) {
        errorMessage = 'Please enter a valid email address.';
      } else if (e.toString().contains('password')) {
        errorMessage = 'Password must be at least 6 characters long.';
      } else {
        errorMessage = 'Registration error: ${e.toString().split(':').last}';
      }

      debugPrint('SupabaseProvider: Registration error: $e');
      _error = errorMessage;
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Clear all user-specific data
  void _clearUserData() {
    _userOrders = [];
    _currentOrder = null;
    _selectedCafeteria = null;
    _menuItems = [];
    _menuItemsByCategory = {};
    debugPrint('SupabaseProvider: Cleared all user-specific data');
  }

  Future<bool> login({
    required String email,
    required String password,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('SupabaseProvider: Logging in user with email: $email');

      // Clear any existing user data first
      _clearUserData();

      final user = await _userService.login(
        email: email,
        password: password,
      );

      if (user != null) {
        debugPrint('SupabaseProvider: User logged in successfully');
        _currentUser = user;

        // Load user orders after login
        await loadUserOrders();

        // Load cafeterias after login
        await loadCafeterias();

        notifyListeners();
        return true;
      } else {
        debugPrint('SupabaseProvider: Login failed (null user returned)');
        _error = 'Invalid email or password';
        notifyListeners();
        return false;
      }
    } catch (e) {
      // Handle specific error cases
      String errorMessage = 'Login failed';

      if (e.toString().contains('Invalid login credentials')) {
        errorMessage = 'Invalid email or password. Please try again.';
      } else if (e.toString().contains('Email not confirmed')) {
        errorMessage = 'Please confirm your email before logging in.';
      } else {
        errorMessage = 'Login error: ${e.toString().split(':').last}';
      }

      debugPrint('SupabaseProvider: Login error: $e');
      _error = errorMessage;
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _userService.logout();
      _currentUser = null;
      _clearUserData();
    } catch (e) {
      _error = 'Logout error: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Cafeteria methods
  Future<void> loadCafeterias() async {
    _isLoadingCafeterias = true;
    _loadingMessage = 'Loading cafeterias...';
    notifyListeners();

    try {
      debugPrint('Loading cafeterias from service...');
      _cafeterias = await _cafeteriaService.getAllCafeterias();
      debugPrint('Loaded ${_cafeterias.length} cafeterias');

      // Debug log each cafeteria
      for (var cafeteria in _cafeterias) {
        debugPrint(
            'Cafeteria: ${cafeteria.name}, isActive: ${cafeteria.isActive}, isOpen: ${cafeteria.isOpen}');
      }
    } catch (e) {
      _error = 'Error loading cafeterias: $e';
      debugPrint('Error loading cafeterias: $e');
    } finally {
      _isLoadingCafeterias = false;
      _loadingMessage = null;
      notifyListeners();
    }
  }

  Future<void> selectCafeteria(String cafeteriaId) async {
    // Check if we're already selecting this cafeteria
    if (_selectedCafeteria?.id == cafeteriaId) {
      debugPrint('Cafeteria $cafeteriaId already selected, skipping');
      return;
    }

    debugPrint('Selecting cafeteria: $cafeteriaId');
    _isLoadingCafeterias = true;
    _loadingMessage = 'Loading cafeteria details...';
    notifyListeners();

    try {
      // First check if we already have this cafeteria in our list
      final cafeteriaIndex = _cafeterias.indexWhere((c) => c.id == cafeteriaId);

      if (cafeteriaIndex >= 0) {
        final existingCafeteria = _cafeterias[cafeteriaIndex];
        debugPrint('Using existing cafeteria: ${existingCafeteria.name}');
        _selectedCafeteria = existingCafeteria;
      } else {
        // If not, fetch it from the service
        debugPrint('Fetching cafeteria from service');
        final cafeteria = await _cafeteriaService.getCafeteriaById(cafeteriaId);
        if (cafeteria != null) {
          _selectedCafeteria = cafeteria;
        }
      }

      // Load menu items for this cafeteria if we have a selected cafeteria
      if (_selectedCafeteria != null) {
        await loadMenuItems(cafeteriaId);
      }
    } catch (e) {
      _error = 'Error selecting cafeteria: $e';
      debugPrint('Error selecting cafeteria: $e');
    } finally {
      _isLoadingCafeterias = false;
      _loadingMessage = null;
      notifyListeners();
    }
  }

  // Menu methods
  Future<void> loadMenuItems(String cafeteriaId) async {
    // Check if we already have menu items for this cafeteria
    final hasMenuItems =
        _menuItems.any((item) => item.cafeteriaId == cafeteriaId);

    if (hasMenuItems) {
      debugPrint(
          'Already have menu items for cafeteria $cafeteriaId, skipping load');
      return;
    }

    debugPrint('Loading menu items for cafeteria: $cafeteriaId');
    _isLoadingMenuItems = true;
    _loadingMessage = 'Loading menu items...';
    notifyListeners();

    try {
      // Load menu items for this specific cafeteria
      final cafeteriaMenuItems = await _menuService.getMenuItemsByCafeteria(cafeteriaId);
      debugPrint('Loaded ${cafeteriaMenuItems.length} menu items for cafeteria $cafeteriaId');

      // Remove any existing items for this cafeteria and add the new ones
      _menuItems.removeWhere((item) => item.cafeteriaId == cafeteriaId);
      _menuItems.addAll(cafeteriaMenuItems);

      // Group menu items by category for this cafeteria
      final categories =
          await _menuService.getCategoriesByCafeteria(cafeteriaId);
      debugPrint('Found ${categories.length} categories for cafeteria $cafeteriaId');

      for (var category in categories) {
        final items =
            await _menuService.getMenuItemsByCategory(cafeteriaId, category);
        _menuItemsByCategory[category] = items;
        debugPrint('Category $category has ${items.length} items');
      }
    } catch (e) {
      _error = 'Error loading menu items: $e';
      debugPrint('Error loading menu items: $e');
    } finally {
      _isLoadingMenuItems = false;
      _loadingMessage = null;
      notifyListeners();
    }
  }

  /// Refresh menu items for current cafeteria
  Future<void> refreshMenuItems() async {
    if (_selectedCafeteria != null) {
      await loadMenuItems(_selectedCafeteria!.id);
    }
  }

  /// Refresh menu items after rating to update ratings display
  Future<void> refreshMenuItemsAfterRating() async {
    if (_selectedCafeteria != null) {
      debugPrint('🔄 Refreshing menu items after rating...');
      await loadMenuItems(_selectedCafeteria!.id);
      notifyListeners();
    }
  }

  // Order methods
  Future<void> loadUserOrders() async {
    if (_currentUser == null) return;

    _isLoadingOrders = true;
    _loadingMessage = 'Loading your orders...';
    notifyListeners();

    try {
      _userOrders = await _orderService.getUserOrders();
      debugPrint('Loaded ${_userOrders.length} orders');
    } catch (e) {
      _error = 'Error loading orders: $e';
      debugPrint('Error loading orders: $e');
    } finally {
      _isLoadingOrders = false;
      _loadingMessage = null;
      notifyListeners();
    }
  }

  Future<bool> createOrder({
    required String cafeteriaId,
    required double totalAmount,
    required List<SupabaseOrderItem> items,
  }) async {
    if (_currentUser == null) {
      debugPrint('Cannot create order: User not authenticated');
      return false;
    }

    _isProcessingCheckout = true;
    _loadingMessage = 'Processing your order...';
    notifyListeners();

    try {
      debugPrint(
          'Creating order for cafeteria: $cafeteriaId with ${items.length} items');
      debugPrint('User ID: ${_currentUser!.id}');
      debugPrint('Total amount: $totalAmount');

      final order = await _orderService.createOrder(
        cafeteriaId: cafeteriaId,
        totalAmount: totalAmount,
        items: items,
      );

      if (order != null) {
        debugPrint('Order created successfully with ID: ${order.id}');
        debugPrint('Order status: ${order.status}');
        debugPrint('Order will appear in cafeteria portal for processing');

        _currentOrder = order;
        _userOrders.insert(0, order); // Add to the beginning of the list
        notifyListeners();
        return true;
      } else {
        _error = 'Failed to create order';
        debugPrint('Failed to create order: null returned from service');
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Error creating order: $e';
      debugPrint('Error creating order: $e');
      notifyListeners();
      return false;
    } finally {
      _isProcessingCheckout = false;
      _loadingMessage = null;
      notifyListeners();
    }
  }

  Future<void> updateOrderStatus(String orderId, String status) async {
    _isLoadingOrders = true;
    _loadingMessage = 'Updating order status...';
    notifyListeners();

    try {
      debugPrint('Updating order status: $orderId to $status');
      final updatedOrder =
          await _orderService.updateOrderStatus(orderId, status);

      if (updatedOrder != null) {
        debugPrint('Order status updated successfully');
        // Update the order in the list
        final index = _userOrders.indexWhere((order) => order.id == orderId);
        if (index != -1) {
          _userOrders[index] = updatedOrder;
        }

        // Update current order if it's the same
        if (_currentOrder?.id == orderId) {
          _currentOrder = updatedOrder;
        }
      }
    } catch (e) {
      _error = 'Error updating order status: $e';
      debugPrint('Error updating order status: $e');
    } finally {
      _isLoadingOrders = false;
      _loadingMessage = null;
      notifyListeners();
    }
  }

  // Getter for menu service
  SupabaseMenuService get menuService => _menuService;

  @override
  void dispose() {
    // Clean up real-time subscriptions when provider is disposed
    for (final subscription in _subscriptions.values) {
      try {
        SupabaseConfig.unsubscribe(subscription);
      } catch (e) {
        debugPrint('Error unsubscribing from real-time channel: $e');
      }
    }
    super.dispose();
  }
}
