import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/models/menu_item.dart';
import 'package:unieatsappv0/models/cafeteria.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/favorites_service.dart';
import 'package:unieatsappv0/services/simple_auth_service.dart';

class FavoritesProvider with ChangeNotifier {
  final Set<String> _favoriteMenuItemIds = {};
  final Set<String> _favoriteCafeteriaIds = {};
  final FavoritesService _favoritesService = FavoritesService();
  final SimpleAuthService _authService = SimpleAuthService();

  bool _isLoading = false;
  String? _error;

  // Storage keys for local persistence
  static const String _cafeteriaFavoritesKey = 'favorite_cafeterias';

  List<String> get favoriteMenuItemIds => _favoriteMenuItemIds.toList();
  List<String> get favoriteCafeteriaIds => _favoriteCafeteriaIds.toList();
  bool get isLoading => _isLoading;
  String? get error => _error;

  bool isMenuItemFavorite(String id) => _favoriteMenuItemIds.contains(id);
  bool isCafeteriaFavorite(String id) => _favoriteCafeteriaIds.contains(id);

  /// Clear all favorites data
  void _clearFavoritesData() {
    _favoriteMenuItemIds.clear();
    _favoriteCafeteriaIds.clear();
    debugPrint('FavoritesProvider: Cleared all favorites data');
  }

  /// Initialize favorites from Supabase and local storage
  Future<void> initialize() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Clear existing data first
      _clearFavoritesData();

      // Load menu item favorites from Supabase
      await loadUserFavorites();

      // Load cafeteria favorites from local storage
      await _loadCafeteriaFavorites();

      // Subscribe to real-time updates for menu items
      final user = _authService.currentUser;
      if (user != null) {
        _favoritesService.subscribeToUserFavorites(user.id, _onFavoriteChange);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to initialize favorites: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error initializing favorites: $e');
    }
  }

  /// Load user favorites from Supabase
  Future<void> loadUserFavorites() async {
    try {
      final favoriteIds = await _favoritesService.getUserFavoriteIds();
      _favoriteMenuItemIds.clear();
      _favoriteMenuItemIds.addAll(favoriteIds);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user favorites: $e');
    }
  }

  /// Toggle menu item favorite status
  Future<void> toggleMenuItemFavorite(String menuItemId) async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await _favoritesService.toggleFavorite(menuItemId);

      if (success) {
        // Update local state
        if (_favoriteMenuItemIds.contains(menuItemId)) {
          _favoriteMenuItemIds.remove(menuItemId);
        } else {
          _favoriteMenuItemIds.add(menuItemId);
        }
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to update favorite: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error toggling favorite: $e');
    }
  }

  /// Legacy method for MenuItem objects
  void toggleMenuItemFavoriteLegacy(MenuItem item) {
    toggleMenuItemFavorite(item.id);
  }

  /// Legacy method for SupabaseMenuItem objects
  void toggleSupabaseMenuItemFavorite(SupabaseMenuItem item) {
    toggleMenuItemFavorite(item.id);
  }

  /// Toggle cafeteria favorite status (for Supabase cafeterias)
  Future<void> toggleSupabaseCafeteriaFavorite(SupabaseCafeteria cafeteria) async {
    try {
      debugPrint('🔄 Toggling cafeteria favorite: ${cafeteria.id}');

      _isLoading = true;
      notifyListeners();

      if (_favoriteCafeteriaIds.contains(cafeteria.id)) {
        _favoriteCafeteriaIds.remove(cafeteria.id);
        debugPrint('❌ Removed cafeteria from favorites: ${cafeteria.name}');
      } else {
        _favoriteCafeteriaIds.add(cafeteria.id);
        debugPrint('✅ Added cafeteria to favorites: ${cafeteria.name}');
      }

      // Save to local storage
      await _saveCafeteriaFavorites();

      _isLoading = false;
      notifyListeners();
      debugPrint('📋 Total cafeteria favorites: ${_favoriteCafeteriaIds.length}');
    } catch (e) {
      _error = 'Failed to update cafeteria favorite: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('❌ Error toggling cafeteria favorite: $e');
    }
  }

  /// Legacy method for Cafeteria objects (kept for compatibility)
  void toggleCafeteriaFavorite(Cafeteria cafeteria) {
    if (_favoriteCafeteriaIds.contains(cafeteria.id)) {
      _favoriteCafeteriaIds.remove(cafeteria.id);
    } else {
      _favoriteCafeteriaIds.add(cafeteria.id);
    }
    notifyListeners();
  }

  /// Handle real-time favorite changes
  void _onFavoriteChange(Map<String, dynamic> payload) {
    // Reload favorites when changes occur
    loadUserFavorites();
  }

  /// Clear all favorites
  Future<void> clearAllFavorites() async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await _favoritesService.clearAllFavorites();

      if (success) {
        _favoriteMenuItemIds.clear();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to clear favorites: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error clearing favorites: $e');
    }
  }

  /// Load cafeteria favorites from local storage
  Future<void> _loadCafeteriaFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? favoriteIds = prefs.getStringList(_cafeteriaFavoritesKey);

      if (favoriteIds != null) {
        _favoriteCafeteriaIds.clear();
        _favoriteCafeteriaIds.addAll(favoriteIds);
        debugPrint('✅ Loaded ${favoriteIds.length} cafeteria favorites from local storage');
      }
    } catch (e) {
      debugPrint('❌ Error loading cafeteria favorites: $e');
    }
  }

  /// Save cafeteria favorites to local storage
  Future<void> _saveCafeteriaFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_cafeteriaFavoritesKey, _favoriteCafeteriaIds.toList());
      debugPrint('✅ Saved ${_favoriteCafeteriaIds.length} cafeteria favorites to local storage');
    } catch (e) {
      debugPrint('❌ Error saving cafeteria favorites: $e');
    }
  }

  /// Dispose method to clean up subscriptions
  @override
  void dispose() {
    final user = _authService.currentUser;
    if (user != null) {
      _favoritesService.unsubscribeFromUserFavorites(user.id);
    }
    super.dispose();
  }
}