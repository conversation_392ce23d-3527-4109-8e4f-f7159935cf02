import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/models/cafeteria_rating.dart';
import 'package:unieatsappv0/services/rating_service.dart';

class CafeteriaRatingsProvider extends ChangeNotifier {
  static const String _storageKey = 'cafeteria_ratings_cache';
  List<CafeteriaRating> _ratings = [];
  Map<String, Map<String, dynamic>> _averageRatings = {};
  bool _initialized = false;
  bool _isLoading = false;

  List<CafeteriaRating> get ratings => _ratings;
  bool get isLoading => _isLoading;

  // Get ratings for a specific cafeteria
  List<CafeteriaRating> getRatingsForCafeteria(String cafeteriaId) {
    return _ratings
        .where((rating) => rating.cafeteriaId == cafeteriaId)
        .toList();
  }

  // Calculate average rating for a cafeteria
  double getAverageRatingForCafeteria(String cafeteriaId) {
    debugPrint('🔍 Getting average rating for cafeteria: $cafeteriaId');

    // First check if we have the average rating in our cache
    if (_averageRatings.containsKey(cafeteriaId)) {
      final rating = _averageRatings[cafeteriaId]!['overall_rating'] ?? 0.0;
      debugPrint('📊 Found cached average rating: $rating');
      return rating;
    }

    // Otherwise calculate from local ratings
    final cafeteriaRatings = getRatingsForCafeteria(cafeteriaId);
    debugPrint('📝 Local ratings count: ${cafeteriaRatings.length}');
    if (cafeteriaRatings.isEmpty) {
      debugPrint('⚠️ No ratings found, returning 0.0');
      return 0.0;
    }

    final sum =
        cafeteriaRatings.fold(0.0, (double sum, rating) => sum + rating.rating);
    final average = sum / cafeteriaRatings.length;
    debugPrint('🧮 Calculated average from local ratings: $average');
    return average;
  }

  // Get total ratings count for a cafeteria
  int getTotalRatingsForCafeteria(String cafeteriaId) {
    // First check if we have the count in our cache
    if (_averageRatings.containsKey(cafeteriaId)) {
      return _averageRatings[cafeteriaId]!['total_ratings'] ?? 0;
    }

    // Otherwise count local ratings
    return getRatingsForCafeteria(cafeteriaId).length;
  }

  // Add a new rating
  Future<void> addRating(CafeteriaRating rating, {BuildContext? context}) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Add to Supabase
      if (context != null) {
        await RatingService.submitCafeteriaRating(
          cafeteriaId: rating.cafeteriaId,
          rating: rating.rating,
          comment: rating.comment,
          orderId: rating.orderId,
          context: context,
        );
      }

      // Add to local cache
      _ratings.add(rating);
      await _saveRatings();

      // Refresh ratings from server
      await fetchRatingsForCafeteria(rating.cafeteriaId);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error adding cafeteria rating: $e');
    }
  }

  // Fetch ratings for a specific cafeteria from Supabase
  Future<void> fetchRatingsForCafeteria(String cafeteriaId) async {
    try {
      debugPrint('🔄 Fetching ratings for cafeteria: $cafeteriaId');
      _isLoading = true;
      notifyListeners();

      // Get ratings from Supabase
      final fetchedRatings = await RatingService.getCafeteriaRatings(cafeteriaId);
      debugPrint('📊 Fetched ${fetchedRatings.length} individual ratings');

      // Get average rating
      final avgRating = await RatingService.getCafeteriaAverageRating(cafeteriaId);
      debugPrint('⭐ Average rating data: $avgRating');

      // Update local cache
      _averageRatings[cafeteriaId] = avgRating;
      debugPrint('💾 Stored average rating: ${avgRating['overall_rating']} for cafeteria: $cafeteriaId');

      // Remove old ratings for this cafeteria
      _ratings.removeWhere((rating) => rating.cafeteriaId == cafeteriaId);

      // Add new ratings
      _ratings.addAll(fetchedRatings);
      debugPrint('📝 Total ratings in cache: ${_ratings.length}');
      debugPrint('📝 Ratings for this cafeteria: ${_ratings.where((r) => r.cafeteriaId == cafeteriaId).length}');

      // Save to local storage
      await _saveRatings();

      _isLoading = false;
      notifyListeners();
      debugPrint('✅ Finished fetching ratings for cafeteria: $cafeteriaId');
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('❌ Error fetching cafeteria ratings: $e');
    }
  }

  // Initialize - load ratings from storage and then from Supabase
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      _isLoading = true;
      notifyListeners();

      // Load from local storage first for quick display
      final prefs = await SharedPreferences.getInstance();
      final ratingsJson = prefs.getString(_storageKey);

      if (ratingsJson != null) {
        final List<dynamic> decodedList = jsonDecode(ratingsJson);
        _ratings =
            decodedList.map((item) => CafeteriaRating.fromJson(item)).toList();
      }

      _initialized = true;
      _isLoading = false;
      notifyListeners();

      // We don't fetch all ratings here since that could be expensive
      // Instead, we'll fetch ratings for specific cafeterias when needed
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      debugPrint('Error initializing cafeteria ratings: $e');
    }
  }

  // Save ratings to storage
  Future<void> _saveRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ratingsJson = jsonEncode(_ratings.map((r) => r.toJson()).toList());
      await prefs.setString(_storageKey, ratingsJson);
    } catch (e) {
      debugPrint('Error saving cafeteria ratings: $e');
    }
  }

  // Clear all ratings (for testing)
  Future<void> clearAllRatings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
      _ratings = [];
      _averageRatings = {};
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing cafeteria ratings: $e');
    }
  }
}
