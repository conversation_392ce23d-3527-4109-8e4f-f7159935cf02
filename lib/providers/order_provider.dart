import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/services/notification_service.dart';

class OrderProvider with ChangeNotifier {
  final List<Order> _orders = [];

  List<Order> get orders => List.unmodifiable(_orders);

  int get orderCount {
    return _orders.length;
  }

  void addOrder(Order order) {
    _orders.insert(0, order); // newest first
    notifyListeners();
  }

  Order? getOrderByNumber(String orderNumber) {
    try {
      return _orders.firstWhere((order) => order.orderNumber == orderNumber);
    } catch (e) {
      return null;
    }
  }

  void updateOrder(Order updatedOrder,
      {bool sendNotification = true, BuildContext? context}) {
    debugPrint('🔄 OrderProvider.updateOrder called for order: ${updatedOrder.orderNumber}');
    debugPrint('📋 OrderProvider has ${_orders.length} orders:');
    for (final order in _orders) {
      debugPrint('   - ${order.orderNumber} (Status: ${order.status})');
    }

    final index = _orders
        .indexWhere((order) => order.orderNumber == updatedOrder.orderNumber);
    debugPrint('🔍 Found order at index: $index');

    if (index != -1) {
      final oldOrder = _orders[index];
      debugPrint('📝 Updating order ${updatedOrder.orderNumber}: ${oldOrder.status} → ${updatedOrder.status}');
      _orders[index] = updatedOrder;
      notifyListeners();
      debugPrint('✅ OrderProvider updated and notified listeners');

      // Send notification if status has changed and notification is requested
      if (sendNotification &&
          oldOrder.status != updatedOrder.status &&
          context != null) {
        // Send in-app notification
        final notificationProvider =
            Provider.of<NotificationProvider>(context, listen: false);

        // Get cafeteria name if available
        String? cafeteriaName;
        if (updatedOrder.items.isNotEmpty) {
          cafeteriaName = updatedOrder.items.first.cafeteriaName;
        }

        // Add in-app notification for status change
        notificationProvider.addOrderStatusNotification(
            updatedOrder.orderNumber, updatedOrder.status,
            cafeteriaName: cafeteriaName);

        // Send notification based on status
        final notificationService = NotificationService();

        if (updatedOrder.status == 'Ready for pickup') {
          notificationService.showOrderReadyNotification(
            updatedOrder,
            onTap: () {
              Navigator.of(context).pushNamed(
                '/order_tracking',
                arguments: updatedOrder,
              );
            },
          );
        } else if (updatedOrder.status == 'Completed') {
          notificationService.showOrderCompletedNotification(
            updatedOrder,
            onTap: () {
              Navigator.of(context).pushNamed(
                '/order_tracking',
                arguments: updatedOrder,
              );
            },
          );
        } else if (updatedOrder.status == 'Cancelled') {
          notificationService.showOrderCancelledNotification(
            updatedOrder,
            onTap: () {
              Navigator.of(context).pushNamed(
                '/order_tracking',
                arguments: updatedOrder,
              );
            },
          );
        }
      }
    } else {
      debugPrint('❌ OrderProvider: Order ${updatedOrder.orderNumber} not found in provider!');
      debugPrint('💡 Adding order to provider instead...');
      addOrder(updatedOrder);
    }
  }
}
