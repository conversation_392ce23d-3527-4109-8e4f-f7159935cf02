import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/services/supabase_cart_service.dart';

/// Modern cart provider with Supabase integration and animations
class ModernCartProvider with ChangeNotifier {
  final SupabaseCartService _cartService = SupabaseCartService();
  
  List<CartItem> _items = [];
  bool _isLoading = false;
  String? _error;
  
  // Animation states
  bool _isAnimating = false;
  String? _lastAddedItemId;
  String? _lastRemovedItemId;

  // Getters
  List<CartItem> get items => _items;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAnimating => _isAnimating;
  String? get lastAddedItemId => _lastAddedItemId;
  String? get lastRemovedItemId => _lastRemovedItemId;

  int get itemCount => _items.length;
  
  double get subtotal {
    return _items.fold(0.0, (total, item) => total + item.calculateItemTotal());
  }

  double get serviceFee {
    final fee = subtotal * 0.04;
    return fee > 20.0 ? 20.0 : fee;
  }

  double get total => subtotal + serviceFee;

  // Formatted currency strings
  String get formattedSubtotal => '${subtotal.toStringAsFixed(2)} EGP';
  String get formattedServiceFee => '${serviceFee.toStringAsFixed(2)} EGP';
  String get formattedTotal => '${total.toStringAsFixed(2)} EGP';

  /// Initialize cart provider
  Future<void> initialize() async {
    await _cartService.initialize();
    await loadCart();
    
    // Subscribe to real-time cart changes
    _cartService.subscribeToCartChanges((updatedItems) {
      _items = updatedItems;
      notifyListeners();
    });
  }

  /// Load cart from Supabase
  Future<void> loadCart() async {
    _setLoading(true);
    _error = null;

    try {
      _items = await _cartService.getCartItems();
    } catch (e) {
      _error = e.toString();
      debugPrint('Error loading cart: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add item to cart with animation
  Future<bool> addItem(CartItem cartItem, {bool animate = true}) async {
    if (animate) {
      _setAnimating(true);
      _lastAddedItemId = cartItem.id;
    }

    try {
      final success = await _cartService.addToCart(cartItem);
      
      if (success) {
        // Update local state optimistically
        final existingIndex = _items.indexWhere((item) => 
            item.menuItem.id == cartItem.menuItem.id);
        
        if (existingIndex >= 0) {
          // Update existing item
          final existingItem = _items[existingIndex];
          _items[existingIndex] = CartItem(
            id: existingItem.id,
            menuItem: existingItem.menuItem,
            quantity: existingItem.quantity + cartItem.quantity,
            customizations: cartItem.customizations,
            notes: cartItem.notes ?? existingItem.notes,
            name: existingItem.name,
            price: existingItem.price,
            image: existingItem.image,
            cafeteriaName: existingItem.cafeteriaName,
            buildingName: existingItem.buildingName,
          );
        } else {
          // Add new item
          _items.add(cartItem);
        }
        
        notifyListeners();
        
        // Reset animation state after delay
        if (animate) {
          Future.delayed(const Duration(milliseconds: 500), () {
            _setAnimating(false);
            _lastAddedItemId = null;
          });
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error adding item to cart: $e');
      
      if (animate) {
        _setAnimating(false);
        _lastAddedItemId = null;
      }
      
      return false;
    }
  }

  /// Remove item from cart with animation
  Future<bool> removeItem(String menuItemId, {bool animate = true}) async {
    if (animate) {
      _setAnimating(true);
      _lastRemovedItemId = menuItemId;
    }

    try {
      final success = await _cartService.removeFromCart(menuItemId);
      
      if (success) {
        // Update local state optimistically
        _items.removeWhere((item) => item.menuItem.id == menuItemId);
        notifyListeners();
        
        // Reset animation state after delay
        if (animate) {
          Future.delayed(const Duration(milliseconds: 300), () {
            _setAnimating(false);
            _lastRemovedItemId = null;
          });
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error removing item from cart: $e');
      
      if (animate) {
        _setAnimating(false);
        _lastRemovedItemId = null;
      }
      
      return false;
    }
  }

  /// Update item quantity with animation
  Future<bool> updateQuantity(String menuItemId, int quantity, {bool animate = true}) async {
    if (quantity <= 0) {
      return await removeItem(menuItemId, animate: animate);
    }

    if (animate) {
      _setAnimating(true);
    }

    try {
      final success = await _cartService.updateCartItemQuantity(menuItemId, quantity);
      
      if (success) {
        // Update local state optimistically
        final itemIndex = _items.indexWhere((item) => item.menuItem.id == menuItemId);
        if (itemIndex >= 0) {
          final existingItem = _items[itemIndex];
          _items[itemIndex] = CartItem(
            id: existingItem.id,
            menuItem: existingItem.menuItem,
            quantity: quantity,
            customizations: existingItem.customizations,
            notes: existingItem.notes,
            name: existingItem.name,
            price: existingItem.price,
            image: existingItem.image,
            cafeteriaName: existingItem.cafeteriaName,
            buildingName: existingItem.buildingName,
          );
        }
        
        notifyListeners();
        
        // Reset animation state after delay
        if (animate) {
          Future.delayed(const Duration(milliseconds: 200), () {
            _setAnimating(false);
          });
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error updating cart quantity: $e');
      
      if (animate) {
        _setAnimating(false);
      }
      
      return false;
    }
  }

  /// Clear entire cart with animation
  Future<bool> clearCart({bool animate = true}) async {
    if (animate) {
      _setAnimating(true);
    }

    try {
      final success = await _cartService.clearCart();
      
      if (success) {
        _items.clear();
        notifyListeners();
        
        // Reset animation state after delay
        if (animate) {
          Future.delayed(const Duration(milliseconds: 400), () {
            _setAnimating(false);
          });
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error clearing cart: $e');
      
      if (animate) {
        _setAnimating(false);
      }
      
      return false;
    }
  }

  /// Get cart item by menu item ID
  CartItem? getCartItem(String menuItemId) {
    try {
      return _items.firstWhere((item) => item.menuItem.id == menuItemId);
    } catch (e) {
      return null;
    }
  }

  /// Check if item is in cart
  bool isInCart(String menuItemId) {
    return _items.any((item) => item.menuItem.id == menuItemId);
  }

  /// Get quantity of specific item in cart
  int getItemQuantity(String menuItemId) {
    final item = getCartItem(menuItemId);
    return item?.quantity ?? 0;
  }

  /// Get order items for checkout
  Future<List<Map<String, dynamic>>> getOrderItems() async {
    return await _cartService.getOrderItems();
  }

  /// Refresh cart from server
  Future<void> refresh() async {
    await loadCart();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Set animating state
  void _setAnimating(bool animating) {
    if (_isAnimating != animating) {
      _isAnimating = animating;
      notifyListeners();
    }
  }

  /// Clear error
  void clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _cartService.dispose();
    super.dispose();
  }
}
