class PenaltyHistory {
  final String id;
  final String userId;
  final String orderId;
  final String penaltyType;
  final double amount;
  final DateTime penaltyDate;
  final String penaltyStatus;
  final bool isPaid;
  final DateTime? paidDate;

  const PenaltyHistory({
    required this.id,
    required this.userId,
    required this.orderId,
    required this.penaltyType,
    required this.amount,
    required this.penaltyDate,
    required this.penaltyStatus,
    this.isPaid = false,
    this.paidDate,
  });

  // Create a PenaltyHistory from a JSON object
  factory PenaltyHistory.fromJson(Map<String, dynamic> json) {
    return PenaltyHistory(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      orderId: json['orderId'] ?? '',
      penaltyType: json['penaltyType'] ?? '',
      amount: (json['amount'] ?? 0.0).toDouble(),
      penaltyDate: json['penaltyDate'] != null
          ? DateTime.parse(json['penaltyDate'].toString())
          : DateTime.now(),
      penaltyStatus: json['penaltyStatus'] ?? 'Pending',
      isPaid: json['isPaid'] ?? false,
      paidDate: json['paidDate'] != null
          ? DateTime.parse(json['paidDate'].toString())
          : null,
    );
  }

  // Create a PenaltyHistory from a Supabase row
  factory PenaltyHistory.fromSupabase(Map<String, dynamic> data) {
    return PenaltyHistory.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert a PenaltyHistory to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'orderId': orderId,
      'penaltyType': penaltyType,
      'amount': amount,
      'penaltyDate': penaltyDate.toIso8601String(),
      'penaltyStatus': penaltyStatus,
      'isPaid': isPaid,
      'paidDate': paidDate?.toIso8601String(),
    };
  }

  // Create a copy of this PenaltyHistory with the given fields replaced
  PenaltyHistory copyWith({
    String? id,
    String? userId,
    String? orderId,
    String? penaltyType,
    double? amount,
    DateTime? penaltyDate,
    String? penaltyStatus,
    bool? isPaid,
    DateTime? paidDate,
  }) {
    return PenaltyHistory(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      orderId: orderId ?? this.orderId,
      penaltyType: penaltyType ?? this.penaltyType,
      amount: amount ?? this.amount,
      penaltyDate: penaltyDate ?? this.penaltyDate,
      penaltyStatus: penaltyStatus ?? this.penaltyStatus,
      isPaid: isPaid ?? this.isPaid,
      paidDate: paidDate ?? this.paidDate,
    );
  }
}
