class Review {
  final String id;
  final String orderId;
  final String userId;
  final String cafeteriaId;
  final String? itemId;
  final int rating;
  final String? comment;
  final bool isAnonymous;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ReviewUser? user;
  final ReviewItem? item;

  Review({
    required this.id,
    required this.orderId,
    required this.userId,
    required this.cafeteriaId,
    this.itemId,
    required this.rating,
    this.comment,
    required this.isAnonymous,
    required this.createdAt,
    required this.updatedAt,
    this.user,
    this.item,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'],
      orderId: json['order_id'],
      userId: json['user_id'],
      cafeteriaId: json['cafeteria_id'],
      itemId: json['item_id'],
      rating: json['rating'],
      comment: json['comment'],
      isAnonymous: json['is_anonymous'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      user: json['user'] != null ? ReviewUser.fromJson(json['user']) : null,
      item: json['item'] != null ? ReviewItem.fromJson(json['item']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'user_id': userId,
      'cafeteria_id': cafeteriaId,
      'item_id': itemId,
      'rating': rating,
      'comment': comment,
      'is_anonymous': isAnonymous,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'user': user?.toJson(),
      'item': item?.toJson(),
    };
  }

  /// Get formatted rating as stars
  String get ratingStars {
    return '★' * rating + '☆' * (5 - rating);
  }

  /// Get relative time string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  /// Get display name (considering anonymous reviews)
  String get displayName {
    if (isAnonymous) {
      return 'Anonymous';
    }
    return user?.fullName ?? 'Unknown User';
  }

  /// Check if review has comment
  bool get hasComment => comment != null && comment!.isNotEmpty;

  /// Get short comment (truncated if too long)
  String? get shortComment {
    if (!hasComment) return null;
    if (comment!.length <= 100) return comment;
    return '${comment!.substring(0, 97)}...';
  }
}

class ReviewUser {
  final String fullName;
  final String? avatarUrl;

  ReviewUser({
    required this.fullName,
    this.avatarUrl,
  });

  factory ReviewUser.fromJson(Map<String, dynamic> json) {
    return ReviewUser(
      fullName: json['full_name'] ?? '',
      avatarUrl: json['avatar_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'full_name': fullName,
      'avatar_url': avatarUrl,
    };
  }
}

class ReviewItem {
  final String name;

  ReviewItem({
    required this.name,
  });

  factory ReviewItem.fromJson(Map<String, dynamic> json) {
    return ReviewItem(
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
    };
  }
}
