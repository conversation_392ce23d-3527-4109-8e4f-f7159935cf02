import 'package:unieatsappv0/models/menu_item.dart';

class CartItem {
  final String id;
  final String name;
  final double price;
  final String image;
  final String cafeteriaName;
  final String buildingName;
  final int quantity;
  final Map<String, dynamic> customizations;
  final String? notes;
  final MenuItem? menuItem;

  CartItem({
    required this.id,
    required this.name,
    required this.price,
    required this.image,
    required this.cafeteriaName,
    required this.buildingName,
    required this.quantity,
    required this.customizations,
    this.notes,
    this.menuItem,
  });

  // Calculate the total price for this cart item
  double get totalPrice => price * quantity;

  // Method for backward compatibility
  double calculateItemTotal() {
    return totalPrice;
  }

  // Formatted currency strings
  String get formattedPrice => '${price.toStringAsFixed(2)} EGP';
  String get formattedTotalPrice => '${totalPrice.toStringAsFixed(2)} EGP';

  // Create a copy of this CartItem with the given fields replaced
  CartItem copyWith({
    String? id,
    String? name,
    double? price,
    String? image,
    String? cafeteriaName,
    String? buildingName,
    int? quantity,
    Map<String, dynamic>? customizations,
    String? notes,
    MenuItem? menuItem,
  }) {
    return CartItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      image: image ?? this.image,
      cafeteriaName: cafeteriaName ?? this.cafeteriaName,
      buildingName: buildingName ?? this.buildingName,
      quantity: quantity ?? this.quantity,
      customizations: customizations ?? this.customizations,
      notes: notes ?? this.notes,
      menuItem: menuItem ?? this.menuItem,
    );
  }

  // Convert a CartItem to a Map for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'image': image,
      'cafeteriaName': cafeteriaName,
      'buildingName': buildingName,
      'quantity': quantity,
      'customizations': customizations,
      'notes': notes,
    };
  }

  // Create a CartItem from a Map
  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      image: json['image'] ?? 'assets/images/placeholder.png',
      cafeteriaName: json['cafeteriaName'] ?? '',
      buildingName: json['buildingName'] ?? '',
      quantity: json['quantity'] ?? 1,
      customizations: json['customizations'] ?? {},
      notes: json['notes'],
      menuItem: null, // MenuItem can't be serialized easily
    );
  }
}
