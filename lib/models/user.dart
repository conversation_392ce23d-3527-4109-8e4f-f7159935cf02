class User {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String? address;
  final String? profileImage;
  final double balance;
  final int warningsCount;
  final int penalizedMisses;
  final bool suspensionStatus;
  final bool hasPenalty;
  final double penaltyAmount;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.address,
    this.profileImage,
    this.balance = 0.0,
    this.warningsCount = 0,
    this.penalizedMisses = 0,
    this.suspensionStatus = false,
    this.hasPenalty = false,
    this.penaltyAmount = 0.0,
  });

  // Create a User from a Map
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      address: json['address'],
      profileImage: json['profileImage'],
      balance: (json['balance'] ?? 0.0).toDouble(),
      warningsCount: json['warningsCount'] ?? 0,
      penalizedMisses: json['penalizedMisses'] ?? 0,
      suspensionStatus: json['suspensionStatus'] ?? false,
      hasPenalty: json['hasPenalty'] ?? false,
      penaltyAmount: (json['penaltyAmount'] ?? 0.0).toDouble(),
    );
  }

  // Create a User from a Supabase row
  factory User.fromSupabase(Map<String, dynamic> data) {
    return User.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert a User to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'profileImage': profileImage,
      'balance': balance,
      'warningsCount': warningsCount,
      'penalizedMisses': penalizedMisses,
      'suspensionStatus': suspensionStatus,
      'hasPenalty': hasPenalty,
      'penaltyAmount': penaltyAmount,
    };
  }

  // Create a copy of this User with the given fields replaced
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? profileImage,
    double? balance,
    int? warningsCount,
    int? penalizedMisses,
    bool? suspensionStatus,
    bool? hasPenalty,
    double? penaltyAmount,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      profileImage: profileImage ?? this.profileImage,
      balance: balance ?? this.balance,
      warningsCount: warningsCount ?? this.warningsCount,
      penalizedMisses: penalizedMisses ?? this.penalizedMisses,
      suspensionStatus: suspensionStatus ?? this.suspensionStatus,
      hasPenalty: hasPenalty ?? this.hasPenalty,
      penaltyAmount: penaltyAmount ?? this.penaltyAmount,
    );
  }
}
