class OrderDetails {
  final String id;
  final String orderId;
  final String itemId;
  final int quantity;
  final double priceAtOrder;
  final String? customizations;
  final String? notes;

  const OrderDetails({
    required this.id,
    required this.orderId,
    required this.itemId,
    required this.quantity,
    required this.priceAtOrder,
    this.customizations,
    this.notes,
  });

  // Create an OrderDetails from a JSON object
  factory OrderDetails.fromJson(Map<String, dynamic> json) {
    return OrderDetails(
      id: json['id'] ?? '',
      orderId: json['orderId'] ?? '',
      itemId: json['itemId'] ?? '',
      quantity: json['quantity'] ?? 1,
      priceAtOrder: (json['priceAtOrder'] ?? 0.0).toDouble(),
      customizations: json['customizations'],
      notes: json['notes'],
    );
  }

  // Create an OrderDetails from a Supabase row
  factory OrderDetails.fromSupabase(Map<String, dynamic> data) {
    return OrderDetails.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert an OrderDetails to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'itemId': itemId,
      'quantity': quantity,
      'priceAtOrder': priceAtOrder,
      'customizations': customizations,
      'notes': notes,
    };
  }

  // Create a copy of this OrderDetails with the given fields replaced
  OrderDetails copyWith({
    String? id,
    String? orderId,
    String? itemId,
    int? quantity,
    double? priceAtOrder,
    String? customizations,
    String? notes,
  }) {
    return OrderDetails(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      itemId: itemId ?? this.itemId,
      quantity: quantity ?? this.quantity,
      priceAtOrder: priceAtOrder ?? this.priceAtOrder,
      customizations: customizations ?? this.customizations,
      notes: notes ?? this.notes,
    );
  }
}
