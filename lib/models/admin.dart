class Admin {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String? password;
  final String role;
  final List<String> permissions;
  final DateTime? lastLogin;
  final String? activityLog;

  const Admin({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.password,
    required this.role,
    required this.permissions,
    this.lastLogin,
    this.activityLog,
  });

  // Full name getter
  String get name => '$firstName $lastName';

  // Create an Admin from a JSON object
  factory Admin.fromJson(Map<String, dynamic> json) {
    return Admin(
      id: json['id'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      email: json['email'] ?? '',
      password: json['password'],
      role: json['role'] ?? 'admin',
      permissions: json['permissions'] != null
          ? List<String>.from(json['permissions'])
          : [],
      lastLogin: json['lastLogin'] != null
          ? DateTime.parse(json['lastLogin'].toString())
          : null,
      activityLog: json['activityLog'],
    );
  }

  // Create an Admin from a Supabase row
  factory Admin.fromSupabase(Map<String, dynamic> data) {
    return Admin.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert an Admin to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'password': password,
      'role': role,
      'permissions': permissions,
      'lastLogin': lastLogin?.toIso8601String(),
      'activityLog': activityLog,
    };
  }

  // Create a copy of this Admin with the given fields replaced
  Admin copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? password,
    String? role,
    List<String>? permissions,
    DateTime? lastLogin,
    String? activityLog,
  }) {
    return Admin(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      password: password ?? this.password,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      lastLogin: lastLogin ?? this.lastLogin,
      activityLog: activityLog ?? this.activityLog,
    );
  }
}
