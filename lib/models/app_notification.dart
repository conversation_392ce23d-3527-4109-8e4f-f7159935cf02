/// Model class for app notifications
class AppNotification {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final bool isRead;
  final String? orderId;
  final String type;

  /// Constructor for AppNotification
  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    this.isRead = false,
    this.orderId,
    this.type = 'general',
  });

  /// Create a copy of this notification with some fields replaced
  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    String? orderId,
    String? type,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      orderId: orderId ?? this.orderId,
      type: type ?? this.type,
    );
  }

  /// Convert notification to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'orderId': orderId,
      'type': type,
    };
  }

  /// Create a notification from a map
  factory AppNotification.fromMap(Map<String, dynamic> map) {
    return AppNotification(
      id: map['id'],
      title: map['title'],
      message: map['message'],
      timestamp: DateTime.parse(map['created_at']),
      isRead: map['is_read'] ?? false,
      orderId: map['related_order_id'],
      type: map['type'] ?? 'general',
    );
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, message: $message, timestamp: $timestamp, isRead: $isRead, orderId: $orderId, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AppNotification &&
      other.id == id &&
      other.title == title &&
      other.message == message &&
      other.timestamp == timestamp &&
      other.isRead == isRead &&
      other.orderId == orderId &&
      other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      title.hashCode ^
      message.hashCode ^
      timestamp.hashCode ^
      isRead.hashCode ^
      orderId.hashCode ^
      type.hashCode;
  }
}
