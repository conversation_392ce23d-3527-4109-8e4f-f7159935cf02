class Report {
  final String id;
  final String userId;
  final String cafeteriaId;
  final String title;
  final String description;
  final DateTime date;
  final String status;

  const Report({
    required this.id,
    required this.userId,
    required this.cafeteriaId,
    required this.title,
    required this.description,
    required this.date,
    required this.status,
  });

  // Create a Report from a JSON object
  factory Report.fromJson(Map<String, dynamic> json) {
    return Report(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      cafeteriaId: json['cafeteriaId'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      date: json['date'] != null
          ? DateTime.parse(json['date'].toString())
          : DateTime.now(),
      status: json['status'] ?? 'Pending',
    );
  }

  // Create a Report from a Supabase row
  factory Report.fromSupabase(Map<String, dynamic> data) {
    return Report.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert a Report to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'cafeteriaId': cafeteriaId,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'status': status,
    };
  }

  // Create a copy of this Report with the given fields replaced
  Report copyWith({
    String? id,
    String? userId,
    String? cafeteriaId,
    String? title,
    String? description,
    DateTime? date,
    String? status,
  }) {
    return Report(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      cafeteriaId: cafeteriaId ?? this.cafeteriaId,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      status: status ?? this.status,
    );
  }
}
