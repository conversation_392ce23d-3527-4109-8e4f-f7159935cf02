import 'package:unieatsappv0/models/cart_item.dart';

class Order {
  final String id;
  final String orderNumber;
  final String userId;
  final DateTime orderDate;
  // Alias for backward compatibility
  DateTime get dateTime => orderDate;
  final List<CartItem> items;
  final double totalPrice;
  final String status;
  final String pickupTime;
  final String? paymentMethod;
  final bool penaltyApplied;
  final DateTime? completedDate;
  final int? rating;
  final String? comment;

  Order({
    required this.id,
    required this.orderNumber,
    required this.userId,
    required this.orderDate,
    required this.items,
    required this.totalPrice,
    required this.status,
    required this.pickupTime,
    this.paymentMethod,
    this.penaltyApplied = false,
    this.completedDate,
    this.rating,
    this.comment,
  });

  // Create an Order from a JSON object
  factory Order.fromJson(Map<String, dynamic> json) {
    // Parse items from JSON
    List<CartItem> itemsList = [];
    if (json['items'] != null) {
      itemsList = (json['items'] as List)
          .map((item) => CartItem.fromJson(item as Map<String, dynamic>))
          .toList();
    }

    return Order(
      id: json['id'] ?? '',
      orderNumber: json['orderNumber'] ?? '',
      userId: json['userId'] ?? '',
      orderDate: json['orderDate'] != null
          ? DateTime.parse(json['orderDate'].toString())
          : DateTime.now(),
      items: itemsList,
      totalPrice: (json['totalPrice'] ?? 0.0).toDouble(),
      status: json['status'] ?? 'Pending',
      pickupTime: json['pickupTime'] ?? '',
      paymentMethod: json['paymentMethod'],
      penaltyApplied: json['penaltyApplied'] ?? false,
      completedDate: json['completedDate'] != null
          ? DateTime.parse(json['completedDate'].toString())
          : null,
      rating: json['rating'],
      comment: json['comment'],
    );
  }

  // Create an Order from a Supabase row
  factory Order.fromSupabase(Map<String, dynamic> data) {
    return Order.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert an Order to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'orderNumber': orderNumber,
      'userId': userId,
      'orderDate': orderDate.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'totalPrice': totalPrice,
      'status': status,
      'pickupTime': pickupTime,
      'paymentMethod': paymentMethod,
      'penaltyApplied': penaltyApplied,
      'completedDate': completedDate?.toIso8601String(),
      'rating': rating,
      'comment': comment,
    };
  }

  // Create a copy of this Order with the given fields replaced
  Order copyWith({
    String? id,
    String? orderNumber,
    String? userId,
    DateTime? orderDate,
    List<CartItem>? items,
    double? totalPrice,
    String? status,
    String? pickupTime,
    String? paymentMethod,
    bool? penaltyApplied,
    DateTime? completedDate,
    int? rating,
    String? comment,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      userId: userId ?? this.userId,
      orderDate: orderDate ?? this.orderDate,
      items: items ?? this.items,
      totalPrice: totalPrice ?? this.totalPrice,
      status: status ?? this.status,
      pickupTime: pickupTime ?? this.pickupTime,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      penaltyApplied: penaltyApplied ?? this.penaltyApplied,
      completedDate: completedDate ?? this.completedDate,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
    );
  }
}
