class UserFeedback {
  final String id;
  final String userId;
  final String cafeteriaId;
  final String orderId;
  final String feedbackType;
  final String feedbackCategory;
  final String feedbackComment;
  final DateTime feedbackDate;

  UserFeedback({
    this.id = '',
    required this.userId,
    required this.cafeteriaId,
    required this.orderId,
    required this.feedbackType,
    required this.feedbackCategory,
    required this.feedbackComment,
    required this.feedbackDate,
  });

  // Create a UserFeedback from a JSON object
  factory UserFeedback.fromJson(Map<String, dynamic> json) {
    return UserFeedback(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      cafeteriaId: json['cafeteriaId'] ?? '',
      orderId: json['orderId'] ?? '',
      feedbackType: json['feedbackType'] ?? '',
      feedbackCategory: json['feedbackCategory'] ?? '',
      feedbackComment: json['feedbackComment'] ?? '',
      feedbackDate: json['feedbackDate'] != null
          ? DateTime.parse(json['feedbackDate'].toString())
          : DateTime.now(),
    );
  }

  // Create a UserFeedback from a Supabase row
  factory UserFeedback.fromSupabase(Map<String, dynamic> data) {
    return UserFeedback.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert a UserFeedback to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'cafeteriaId': cafeteriaId,
      'orderId': orderId,
      'feedbackType': feedbackType,
      'feedbackCategory': feedbackCategory,
      'feedbackComment': feedbackComment,
      'feedbackDate': feedbackDate.toIso8601String(),
    };
  }
}
