class MenuItem {
  final String id;
  final String name;
  final double price;
  final String description;
  final String image;
  final String cafeteriaId;
  final String category;
  final double rating;
  final bool isAvailable;
  final double discount;
  final Map<String, dynamic>? customizationOptions;
  final Map<String, dynamic>? nutritionInfo;
  final List<String>? ingredients;

  const MenuItem({
    required this.id,
    required this.name,
    required this.price,
    required this.description,
    required this.image,
    required this.cafeteriaId,
    required this.category,
    required this.rating,
    this.isAvailable = true,
    this.discount = 0.0,
    this.customizationOptions,
    this.nutritionInfo,
    this.ingredients,
  });

  // Formatted currency strings
  String get formattedPrice => '${price.toStringAsFixed(2)} EGP';
  String get formattedDiscountedPrice => '${(price - discount).toStringAsFixed(2)} EGP';

  // Create a MenuItem from a JSON object
  factory MenuItem.fromJson(Map<String, dynamic> json) {
    // Calculate average rating from rating array
    double calculatedRating = 0.0;
    try {
      final ratingData = json['rating'];
      if (ratingData is List && ratingData.isNotEmpty) {
        // Handle array of rating objects: [{rating: 5}, {rating: 4}, ...]
        double sum = 0.0;
        int count = 0;
        for (var item in ratingData) {
          if (item is Map<String, dynamic> && item['rating'] != null) {
            sum += (item['rating'] as num).toDouble();
            count++;
          }
        }
        if (count > 0) {
          calculatedRating = sum / count;
          print('✅ Calculated rating for ${json['name']}: $calculatedRating (from $count ratings)');
        }
      } else if (ratingData is num) {
        // Handle direct numeric rating
        calculatedRating = ratingData.toDouble();
      }
    } catch (e) {
      print('Error calculating rating for ${json['name']}: $e');
      calculatedRating = 0.0;
    }

    return MenuItem(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      description: json['description'] ?? '',
      image: json['image'] ?? 'assets/images/placeholder.png',
      cafeteriaId: json['cafeteriaId'] ?? '',
      category: json['category'] ?? 'Other',
      rating: calculatedRating,
      isAvailable: json['isAvailable'] ?? true,
      discount: (json['discount'] ?? 0.0).toDouble(),
      customizationOptions: json['customizationOptions'],
      nutritionInfo: json['nutritionInfo'],
      ingredients: json['ingredients'] != null
          ? List<String>.from(json['ingredients'])
          : null,
    );
  }

  // Create a MenuItem from a Supabase row
  factory MenuItem.fromSupabase(Map<String, dynamic> data) {
    return MenuItem.fromJson({
      'id': data['id'],
      'name': data['name'],
      'price': data['price'],
      'description': data['description'],
      'image': data['image_url'] ?? data['image'], // Handle both field names
      'cafeteriaId': data['cafeteria_id'] ?? data['cafeteriaId'],
      'category': data['category'],
      'rating': data['rating'], // This will be processed by fromJson
      'isAvailable': data['is_available'] ?? data['isAvailable'] ?? true,
      'discount': data['discount'] ?? 0.0,
      'customizationOptions': data['customization_options'] ?? data['customizationOptions'],
      'nutritionInfo': data['nutrition_info'] ?? data['nutritionInfo'],
      'ingredients': data['ingredients'],
    });
  }

  // Convert a MenuItem to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'price': price,
      'description': description,
      'image': image,
      'cafeteriaId': cafeteriaId,
      'category': category,
      'rating': rating,
      'isAvailable': isAvailable,
      'discount': discount,
      'customizationOptions': customizationOptions,
      'nutritionInfo': nutritionInfo,
      'ingredients': ingredients,
    };
  }

  // Create a copy of this MenuItem with the given fields replaced
  MenuItem copyWith({
    String? id,
    String? name,
    double? price,
    String? description,
    String? image,
    String? cafeteriaId,
    String? category,
    double? rating,
    bool? isAvailable,
    double? discount,
    Map<String, dynamic>? customizationOptions,
    Map<String, dynamic>? nutritionInfo,
    List<String>? ingredients,
  }) {
    return MenuItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      description: description ?? this.description,
      image: image ?? this.image,
      cafeteriaId: cafeteriaId ?? this.cafeteriaId,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      isAvailable: isAvailable ?? this.isAvailable,
      discount: discount ?? this.discount,
      customizationOptions: customizationOptions ?? this.customizationOptions,
      nutritionInfo: nutritionInfo ?? this.nutritionInfo,
      ingredients: ingredients ?? this.ingredients,
    );
  }
}
