class ChatConversation {
  final String id;
  final String userId;
  final String? supportAgentId;
  final String subject;
  final String status;
  final String priority;
  final String category;
  final String? orderId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? closedAt;
  final int? rating;
  final String? feedback;

  ChatConversation({
    required this.id,
    required this.userId,
    this.supportAgentId,
    required this.subject,
    required this.status,
    required this.priority,
    required this.category,
    this.orderId,
    required this.createdAt,
    required this.updatedAt,
    this.closedAt,
    this.rating,
    this.feedback,
  });

  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    return ChatConversation(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      supportAgentId: json['support_agent_id'] as String?,
      subject: json['subject'] as String,
      status: json['status'] as String? ?? 'open',
      priority: json['priority'] as String? ?? 'medium',
      category: json['category'] as String? ?? 'general_inquiry',
      orderId: json['order_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      closedAt: json['closed_at'] != null ? DateTime.parse(json['closed_at'] as String) : null,
      rating: json['rating'] as int?,
      feedback: json['feedback'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'support_agent_id': supportAgentId,
      'subject': subject,
      'status': status,
      'priority': priority,
      'category': category,
      'order_id': orderId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'closed_at': closedAt?.toIso8601String(),
      'rating': rating,
      'feedback': feedback,
    };
  }

  bool get isOpen => status == 'open';
  bool get isClosed => status == 'closed';
  bool get isAssigned => supportAgentId != null;
}

class ChatMessage {
  final String id;
  final String conversationId;
  final String senderId;
  final String messageType;
  final String content;
  final String? fileUrl;
  final String? fileName;
  final int? fileSize;
  final bool isRead;
  final DateTime createdAt;

  ChatMessage({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.messageType,
    required this.content,
    this.fileUrl,
    this.fileName,
    this.fileSize,
    required this.isRead,
    required this.createdAt,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] as String,
      conversationId: json['conversation_id'] as String,
      senderId: json['sender_id'] as String,
      messageType: json['message_type'] as String? ?? 'text',
      content: json['content'] as String,
      fileUrl: json['file_url'] as String?,
      fileName: json['file_name'] as String?,
      fileSize: json['file_size'] as int?,
      isRead: json['is_read'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversation_id': conversationId,
      'sender_id': senderId,
      'message_type': messageType,
      'content': content,
      'file_url': fileUrl,
      'file_name': fileName,
      'file_size': fileSize,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isTextMessage => messageType == 'text';
  bool get isFileMessage => messageType == 'file';
  bool get hasFile => fileUrl != null;
}

class SupportTicket {
  final String id;
  final String ticketNumber;
  final String userId;
  final String? assignedTo;
  final String title;
  final String description;
  final String category;
  final String priority;
  final String status;
  final String? orderId;
  final List<String> attachments;
  final List<String> tags;
  final String? resolution;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? resolvedAt;
  final DateTime? closedAt;
  final int? customerRating;
  final String? customerFeedback;

  SupportTicket({
    required this.id,
    required this.ticketNumber,
    required this.userId,
    this.assignedTo,
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.status,
    this.orderId,
    required this.attachments,
    required this.tags,
    this.resolution,
    required this.createdAt,
    required this.updatedAt,
    this.resolvedAt,
    this.closedAt,
    this.customerRating,
    this.customerFeedback,
  });

  factory SupportTicket.fromJson(Map<String, dynamic> json) {
    return SupportTicket(
      id: json['id'] as String,
      ticketNumber: json['ticket_number'] as String,
      userId: json['user_id'] as String,
      assignedTo: json['assigned_to'] as String?,
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      priority: json['priority'] as String? ?? 'medium',
      status: json['status'] as String? ?? 'open',
      orderId: json['order_id'] as String?,
      attachments: List<String>.from(json['attachments'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      resolution: json['resolution'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      resolvedAt: json['resolved_at'] != null ? DateTime.parse(json['resolved_at'] as String) : null,
      closedAt: json['closed_at'] != null ? DateTime.parse(json['closed_at'] as String) : null,
      customerRating: json['customer_rating'] as int?,
      customerFeedback: json['customer_feedback'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticket_number': ticketNumber,
      'user_id': userId,
      'assigned_to': assignedTo,
      'title': title,
      'description': description,
      'category': category,
      'priority': priority,
      'status': status,
      'order_id': orderId,
      'attachments': attachments,
      'tags': tags,
      'resolution': resolution,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'resolved_at': resolvedAt?.toIso8601String(),
      'closed_at': closedAt?.toIso8601String(),
      'customer_rating': customerRating,
      'customer_feedback': customerFeedback,
    };
  }

  bool get isOpen => status == 'open';
  bool get isClosed => status == 'closed';
  bool get isResolved => status == 'resolved';
  bool get isAssigned => assignedTo != null;
}
