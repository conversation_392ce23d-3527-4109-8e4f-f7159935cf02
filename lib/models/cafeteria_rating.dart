class CafeteriaRating {
  final String id;
  final String cafeteriaId;
  final double rating;
  final String? comment;
  final DateTime date;
  final String? orderId;
  final String? userId;
  final String? username;

  const CafeteriaRating({
    required this.id,
    required this.cafeteriaId,
    required this.rating,
    this.comment,
    required this.date,
    this.orderId,
    this.userId,
    this.username,
  });

  // Create a CafeteriaRating from a JSON object
  factory CafeteriaRating.fromJson(Map<String, dynamic> json) {
    return CafeteriaRating(
      id: json['id'] ?? '',
      cafeteriaId: json['cafeteriaId'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      comment: json['comment'],
      date: json['date'] != null
          ? DateTime.parse(json['date'].toString())
          : DateTime.now(),
      orderId: json['orderId'],
      userId: json['userId'],
      username: json['username'],
    );
  }

  // Create a CafeteriaRating from a Supabase row
  factory CafeteriaRating.fromSupabase(Map<String, dynamic> data) {
    return CafeteriaRating.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert a CafeteriaRating to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'cafeteriaId': cafeteriaId,
      'rating': rating,
      'comment': comment,
      'date': date.toIso8601String(),
      'orderId': orderId,
      'userId': userId,
      'username': username,
    };
  }

  // Create a copy of this CafeteriaRating with the given fields replaced
  CafeteriaRating copyWith({
    String? id,
    String? cafeteriaId,
    double? rating,
    String? comment,
    DateTime? date,
    String? orderId,
    String? userId,
    String? username,
  }) {
    return CafeteriaRating(
      id: id ?? this.id,
      cafeteriaId: cafeteriaId ?? this.cafeteriaId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      date: date ?? this.date,
      orderId: orderId ?? this.orderId,
      userId: userId ?? this.userId,
      username: username ?? this.username,
    );
  }
}
