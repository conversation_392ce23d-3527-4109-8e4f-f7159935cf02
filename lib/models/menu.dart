class Menu {
  final String cafeteriaId;
  final String name;
  final String description;
  final double price;
  final String category;
  final bool availability;
  final String menuType;

  Menu({
    required this.cafeteriaId,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    required this.availability,
    required this.menuType,
  });

  Map<String, dynamic> toJson() {
    return {
      'cafeteriaId': cafeteriaId,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'availability': availability,
      'menuType': menuType,
    };
  }
}