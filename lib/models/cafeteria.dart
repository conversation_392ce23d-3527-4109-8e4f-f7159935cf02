class Cafeteria {
  final String id;
  final String name;
  final String location;
  final String phoneNumber;
  final String email;
  final String cuisineType;
  final String description;
  final String image;
  final bool isOpen;
  final String openingHours;
  final double rating;
  final int estimatedTime;

  const Cafeteria({
    required this.id,
    required this.name,
    required this.location,
    required this.phoneNumber,
    required this.email,
    required this.cuisineType,
    required this.description,
    required this.image,
    required this.isOpen,
    required this.openingHours,
    required this.rating,
    required this.estimatedTime,
  });

  // Create a Cafeteria from a Firestore document
  factory Cafeteria.fromJson(Map<String, dynamic> json) {
    return Cafeteria(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      location: json['location'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] ?? '',
      cuisineType: json['cuisineType'] ?? '',
      description: json['description'] ?? '',
      image: json['image'] ?? 'assets/images/placeholder.png',
      isOpen: json['isOpen'] ?? false,
      openingHours: json['openingHours'] ?? '9:00 AM - 5:00 PM',
      rating: (json['rating'] ?? 0.0).toDouble(),
      estimatedTime: json['estimatedTime'] ?? 15,
    );
  }

  // Create a Cafeteria from a Supabase row
  factory Cafeteria.fromSupabase(Map<String, dynamic> data) {
    return Cafeteria.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert a Cafeteria to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'location': location,
      'phoneNumber': phoneNumber,
      'email': email,
      'cuisineType': cuisineType,
      'description': description,
      'image': image,
      'isOpen': isOpen,
      'openingHours': openingHours,
      'rating': rating,
      'estimatedTime': estimatedTime,
    };
  }

  // Create a copy of this Cafeteria with the given fields replaced
  Cafeteria copyWith({
    String? id,
    String? name,
    String? location,
    String? phoneNumber,
    String? email,
    String? cuisineType,
    String? description,
    String? image,
    bool? isOpen,
    String? openingHours,
    double? rating,
    int? estimatedTime,
  }) {
    return Cafeteria(
      id: id ?? this.id,
      name: name ?? this.name,
      location: location ?? this.location,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      cuisineType: cuisineType ?? this.cuisineType,
      description: description ?? this.description,
      image: image ?? this.image,
      isOpen: isOpen ?? this.isOpen,
      openingHours: openingHours ?? this.openingHours,
      rating: rating ?? this.rating,
      estimatedTime: estimatedTime ?? this.estimatedTime,
    );
  }
}
