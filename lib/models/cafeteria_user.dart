class CafeteriaUser {
  final String id;
  final String cafeteriaId;
  final String firstName;
  final String lastName;
  final String email;
  final String? password;
  final String role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;

  const CafeteriaUser({
    required this.id,
    required this.cafeteriaId,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.password,
    required this.role,
    this.isActive = true,
    required this.createdAt,
    this.lastLogin,
  });

  // Full name getter
  String get name => '$firstName $lastName';

  // Create a CafeteriaUser from a JSON object
  factory CafeteriaUser.fromJson(Map<String, dynamic> json) {
    return CafeteriaUser(
      id: json['id'] ?? '',
      cafeteriaId: json['cafeteriaId'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      email: json['email'] ?? '',
      password: json['password'],
      role: json['role'] ?? 'staff',
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'].toString())
          : DateTime.now(),
      lastLogin: json['lastLogin'] != null
          ? DateTime.parse(json['lastLogin'].toString())
          : null,
    );
  }

  // Create a CafeteriaUser from a Supabase row
  factory CafeteriaUser.fromSupabase(Map<String, dynamic> data) {
    return CafeteriaUser.fromJson({
      'id': data['id'],
      ...data,
    });
  }

  // Convert a CafeteriaUser to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'cafeteriaId': cafeteriaId,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'password': password,
      'role': role,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
    };
  }

  // Create a copy of this CafeteriaUser with the given fields replaced
  CafeteriaUser copyWith({
    String? id,
    String? cafeteriaId,
    String? firstName,
    String? lastName,
    String? email,
    String? password,
    String? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return CafeteriaUser(
      id: id ?? this.id,
      cafeteriaId: cafeteriaId ?? this.cafeteriaId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      password: password ?? this.password,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }
}
