# Supabase Integration for UniEats

This directory contains the necessary files and instructions for setting up and using Supabase as the backend for the UniEats application.

## Setup Instructions

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com/) and sign up or log in
2. Create a new project
3. Note your project URL and anon key (you'll need these later)

### 2. Set Up Database Schema

1. Navigate to the SQL Editor in your Supabase dashboard
2. Copy the contents of `migrations/schema.sql` and execute it
3. This will create all the necessary tables and set up Row Level Security (RLS) policies

### 3. Load Sample Data (Optional)

1. Navigate to the SQL Editor in your Supabase dashboard
2. Copy the contents of `migrations/sample_data.sql` and execute it
3. This will populate your database with sample cafeterias and menu items

### 4. Configure Storage Buckets

The application will automatically create the following storage buckets when it initializes:
- `profile_images` - For user profile pictures
- `menu_item_images` - For menu item images
- `cafeteria_images` - For cafeteria images

You can also create these manually in the Supabase dashboard under Storage.

### 5. Configure Authentication

1. Navigate to Authentication > Settings in your Supabase dashboard
2. Enable Email/Password sign-in method
3. Configure any additional authentication providers as needed

### 6. Update App Configuration

1. Open `lib/config/env_config.dart`
2. Update the `supabaseUrl` and `supabaseAnonKey` with your project's values

## Database Schema

### Tables

- **profiles** - User profiles linked to Supabase Auth
- **cafeterias** - Cafeterias available in the app
- **menu_items** - Menu items for each cafeteria
- **orders** - User orders
- **order_items** - Items within each order
- **favorites** - User favorite menu items
- **ratings** - User ratings for cafeterias
- **notifications** - User notifications

### Row Level Security (RLS)

The schema includes RLS policies to ensure data security:

- Users can only access their own profiles, orders, and favorites
- Cafeterias and menu items are publicly readable but only admins can modify them
- Ratings are publicly readable but users can only create/update their own ratings

## Migration from Firebase

If you're migrating from Firebase, the app includes a migration utility:

1. Navigate to the Migration screen in the app (Profile > Data Migration)
2. Follow the on-screen instructions to migrate your data

## API Reference

### Supabase Service

The `SupabaseService` class provides methods for interacting with Supabase:

- **Authentication**
  - `signUp()` - Register a new user
  - `signIn()` - Log in a user
  - `signOut()` - Log out the current user

- **Database**
  - `insert()` - Insert data into a table
  - `update()` - Update data in a table
  - `delete()` - Delete data from a table
  - `select()` - Query data from a table

- **Storage**
  - `uploadFile()` - Upload a file to Supabase Storage
  - `deleteFile()` - Delete a file from Supabase Storage

### Entity Services

The app includes service classes for each entity:

- `SupabaseUserService` - User management
- `SupabaseCafeteriaService` - Cafeteria management
- `SupabaseMenuService` - Menu item management
- `SupabaseOrderService` - Order management

## Troubleshooting

### Common Issues

1. **Connection Issues**
   - Verify your Supabase URL and anon key are correct
   - Check your internet connection
   - Ensure your Supabase project is active

2. **Authentication Issues**
   - Make sure Email/Password sign-in is enabled in Supabase
   - Check for any email confirmation requirements

3. **Permission Issues**
   - Review the RLS policies in `migrations/schema.sql`
   - Ensure users have the correct permissions for their actions

### Getting Help

If you encounter issues, you can:
1. Check the Supabase documentation at https://supabase.com/docs
2. Visit the Supabase GitHub repository at https://github.com/supabase/supabase
3. Join the Supabase Discord community at https://discord.supabase.com
