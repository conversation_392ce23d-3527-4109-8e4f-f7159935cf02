-- Insert sample cafeterias
INSERT INTO cafeterias (id, name, description, image_url, location, is_active)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Campus Cafe', 'The main campus cafe offering a variety of meals and snacks.', 'https://example.com/images/campus_cafe.jpg', 'Main Building, Ground Floor', true),
  ('*************-2222-2222-************', 'Science Bistro', 'Located in the science building, specializing in quick meals and coffee.', 'https://example.com/images/science_bistro.jpg', 'Science Building, 1st Floor', true),
  ('*************-3333-3333-************', 'Library Lounge', 'Quiet cafe in the library with study-friendly environment.', 'https://example.com/images/library_lounge.jpg', 'Central Library, 2nd Floor', true),
  ('*************-4444-4444-************', 'Sports Bar & Grill', 'Casual dining near the sports complex.', 'https://example.com/images/sports_bar.jpg', 'Sports Complex, East Wing', true);

-- Insert sample menu items for Campus Cafe
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Cheeseburger', 'Classic beef patty with cheese, lettuce, and tomato.', 8.99, 'https://example.com/images/cheeseburger.jpg', 'Burgers', true),
  ('11111111-1111-1111-1111-111111111111', 'Veggie Burger', 'Plant-based patty with fresh vegetables.', 7.99, 'https://example.com/images/veggie_burger.jpg', 'Burgers', true),
  ('11111111-1111-1111-1111-111111111111', 'Caesar Salad', 'Fresh romaine lettuce with Caesar dressing and croutons.', 6.99, 'https://example.com/images/caesar_salad.jpg', 'Salads', true),
  ('11111111-1111-1111-1111-111111111111', 'Greek Salad', 'Mixed greens with feta cheese, olives, and Greek dressing.', 7.99, 'https://example.com/images/greek_salad.jpg', 'Salads', true),
  ('11111111-1111-1111-1111-111111111111', 'Cappuccino', 'Espresso with steamed milk and foam.', 3.99, 'https://example.com/images/cappuccino.jpg', 'Beverages', true),
  ('11111111-1111-1111-1111-111111111111', 'Chocolate Brownie', 'Rich chocolate brownie with walnuts.', 2.99, 'https://example.com/images/brownie.jpg', 'Desserts', true);

-- Insert sample menu items for Science Bistro
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  ('*************-2222-2222-************', 'Chicken Wrap', 'Grilled chicken with vegetables in a tortilla wrap.', 7.99, 'https://example.com/images/chicken_wrap.jpg', 'Wraps', true),
  ('*************-2222-2222-************', 'Tuna Sandwich', 'Tuna salad with lettuce on whole grain bread.', 6.99, 'https://example.com/images/tuna_sandwich.jpg', 'Sandwiches', true),
  ('*************-2222-2222-************', 'Tomato Soup', 'Creamy tomato soup with basil.', 4.99, 'https://example.com/images/tomato_soup.jpg', 'Soups', true),
  ('*************-2222-2222-************', 'Latte', 'Espresso with steamed milk.', 3.49, 'https://example.com/images/latte.jpg', 'Beverages', true),
  ('*************-2222-2222-************', 'Blueberry Muffin', 'Freshly baked muffin with blueberries.', 2.49, 'https://example.com/images/blueberry_muffin.jpg', 'Pastries', true);

-- Insert sample menu items for Library Lounge
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  ('*************-3333-3333-************', 'Avocado Toast', 'Whole grain toast with avocado and cherry tomatoes.', 6.99, 'https://example.com/images/avocado_toast.jpg', 'Breakfast', true),
  ('*************-3333-3333-************', 'Granola Bowl', 'Greek yogurt with granola and fresh berries.', 5.99, 'https://example.com/images/granola_bowl.jpg', 'Breakfast', true),
  ('*************-3333-3333-************', 'Green Tea', 'Organic green tea.', 2.99, 'https://example.com/images/green_tea.jpg', 'Beverages', true),
  ('*************-3333-3333-************', 'Fruit Smoothie', 'Blend of seasonal fruits with yogurt.', 4.99, 'https://example.com/images/fruit_smoothie.jpg', 'Beverages', true),
  ('*************-3333-3333-************', 'Chocolate Chip Cookie', 'Freshly baked cookie with chocolate chips.', 1.99, 'https://example.com/images/chocolate_chip_cookie.jpg', 'Snacks', true);

-- Insert sample menu items for Sports Bar & Grill
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  ('*************-4444-4444-************', 'Chicken Wings', 'Spicy buffalo wings with blue cheese dip.', 9.99, 'https://example.com/images/chicken_wings.jpg', 'Appetizers', true),
  ('*************-4444-4444-************', 'Nachos', 'Tortilla chips with cheese, jalapeños, and salsa.', 8.99, 'https://example.com/images/nachos.jpg', 'Appetizers', true),
  ('*************-4444-4444-************', 'Steak Sandwich', 'Grilled steak with caramelized onions on a baguette.', 12.99, 'https://example.com/images/steak_sandwich.jpg', 'Sandwiches', true),
  ('*************-4444-4444-************', 'Fish and Chips', 'Battered fish with french fries and tartar sauce.', 11.99, 'https://example.com/images/fish_and_chips.jpg', 'Main Courses', true),
  ('*************-4444-4444-************', 'Craft Beer', 'Local IPA on tap.', 5.99, 'https://example.com/images/craft_beer.jpg', 'Beverages', true),
  ('*************-4444-4444-************', 'Cheesecake', 'New York style cheesecake with berry compote.', 6.99, 'https://example.com/images/cheesecake.jpg', 'Desserts', true);

-- Note: The following queries would be executed after a user has been created in the system
-- These are commented out as they depend on having actual user IDs in the system

/*
-- Insert sample ratings (assuming a user with ID exists)
INSERT INTO ratings (user_id, cafeteria_id, rating, comment)
VALUES
  ('user-uuid-here', '11111111-1111-1111-1111-111111111111', 4, 'Great food and service!'),
  ('user-uuid-here', '*************-2222-2222-************', 5, 'Excellent coffee and quick service.'),
  ('user-uuid-here', '*************-3333-3333-************', 3, 'Good place to study but limited menu options.');

-- Insert sample favorites (assuming a user with ID exists)
INSERT INTO favorites (user_id, menu_item_id)
SELECT 'user-uuid-here', id FROM menu_items WHERE name IN ('Cheeseburger', 'Cappuccino', 'Avocado Toast');

-- Insert sample orders and order items (assuming a user with ID exists)
-- First order
INSERT INTO orders (id, user_id, cafeteria_id, total_amount, status, created_at)
VALUES ('order-uuid-1', 'user-uuid-here', '11111111-1111-1111-1111-111111111111', 15.97, 'completed', NOW() - INTERVAL '2 days');

-- Order items for first order
INSERT INTO order_items (order_id, menu_item_id, quantity, price)
SELECT 
  'order-uuid-1', 
  id, 
  1, 
  price 
FROM menu_items 
WHERE name IN ('Cheeseburger', 'Caesar Salad', 'Cappuccino');

-- Second order
INSERT INTO orders (id, user_id, cafeteria_id, total_amount, status, created_at)
VALUES ('order-uuid-2', 'user-uuid-here', '*************-3333-3333-************', 13.97, 'pending', NOW() - INTERVAL '1 hour');

-- Order items for second order
INSERT INTO order_items (order_id, menu_item_id, quantity, price)
SELECT 
  'order-uuid-2', 
  id, 
  1, 
  price 
FROM menu_items 
WHERE name IN ('Avocado Toast', 'Fruit Smoothie', 'Chocolate Chip Cookie');
*/
