import { StyleSheet, Text, type TextProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?:
    | 'default'
    | 'title'
    | 'heading1'
    | 'heading2'
    | 'heading3'
    | 'subtitle'
    | 'body'
    | 'bodyBold'
    | 'caption'
    | 'button'
    | 'link';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');
  const primaryColor = useThemeColor({}, 'tint');

  return (
    <Text
      style={[
        { color },
        styles[type] || styles.default,
        type === 'link' ? { color: primaryColor } : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    lineHeight: 38,
    letterSpacing: -0.5,
  },
  heading1: {
    fontSize: 28,
    fontWeight: '600',
    lineHeight: 34,
    letterSpacing: -0.25,
  },
  heading2: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 30,
    letterSpacing: 0,
  },
  heading3: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 26,
    letterSpacing: 0.15,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 24,
    letterSpacing: 0.1,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyBold: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  caption: {
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  button: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
    letterSpacing: 1.25,
    textTransform: 'uppercase',
  },
  link: {
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
    textDecorationLine: 'underline',
  },
});
