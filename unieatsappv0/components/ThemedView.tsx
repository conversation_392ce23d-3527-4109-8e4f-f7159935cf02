import { View, type ViewProps, StyleSheet } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  variant?: 'default' | 'card' | 'surface' | 'elevated';
  rounded?: boolean;
  bordered?: boolean;
};

export function ThemedView({
  style,
  lightColor,
  darkColor,
  variant = 'default',
  rounded = false,
  bordered = false,
  ...otherProps
}: ThemedViewProps) {
  const backgroundColor = useThemeColor(
    { light: lightColor, dark: darkColor },
    variant === 'default' ? 'background' :
    variant === 'surface' ? 'surface' :
    variant === 'card' ? 'card' : 'surface'
  );

  const borderColor = useThemeColor({}, 'border');
  const shadowColor = useThemeColor({}, 'shadow');

  return (
    <View
      style={[
        { backgroundColor },
        rounded && styles.rounded,
        bordered && { ...styles.bordered, borderColor },
        variant === 'elevated' && { ...styles.elevated, shadowColor },
        style
      ]}
      {...otherProps}
    />
  );
}

const styles = StyleSheet.create({
  rounded: {
    borderRadius: 12,
  },
  bordered: {
    borderWidth: 1,
  },
  elevated: {
    borderRadius: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  }
});
