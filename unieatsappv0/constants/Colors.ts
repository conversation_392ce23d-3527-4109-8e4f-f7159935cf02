/**
 * Modern color palette for the app in light and dark mode.
 * These colors match the Flutter theme for a consistent look across platforms.
 */

// Primary colors
const primaryColor = '#6200EE';  // Deep purple
const secondaryColor = '#03DAC6'; // Teal
const accentColor = '#BB86FC';    // Light purple

export const Colors = {
  light: {
    text: '#121212',              // Near black
    background: '#F5F5F5',        // Light grey background
    surface: '#FFFFFF',           // White surface
    tint: primaryColor,           // Primary color as tint
    icon: '#666666',              // Dark grey for icons
    tabIconDefault: '#666666',    // Dark grey for inactive tabs
    tabIconSelected: primaryColor, // Primary color for active tabs
    error: '#B00020',             // Error color
    border: '#E0E0E0',            // Light grey border
    card: '#FFFFFF',              // White card background
    shadow: 'rgba(0, 0, 0, 0.1)',  // Shadow color
  },
  dark: {
    text: '#FFFFFF',              // White text
    background: '#121212',        // Dark background
    surface: '#1E1E1E',           // Dark surface
    tint: accentColor,            // Accent color as tint in dark mode
    icon: '#BBBBBB',              // Light grey for icons
    tabIconDefault: '#BBBBBB',    // Light grey for inactive tabs
    tabIconSelected: accentColor, // Accent color for active tabs
    error: '#CF6679',             // Error color for dark mode
    border: '#333333',            // Dark border
    card: '#1E1E1E',              // Dark card background
    shadow: 'rgba(0, 0, 0, 0.3)',  // Shadow color
  },
};
