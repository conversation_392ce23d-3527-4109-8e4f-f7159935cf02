# UniEats Notification System Updates

## Overview

The notification system in the UniEats mobile app has been enhanced to properly connect with Supabase and provide real-time updates for order status changes. This document explains the changes made and how the system now works.

## Key Changes

1. **Enhanced NotificationProvider**
   - Now fetches notifications from the Supabase database
   - Subscribes to real-time updates when notifications change
   - <PERSON><PERSON>ly syncs with the database for marking notifications as read/unread
   - Hand<PERSON> adding and removing notifications

2. **Dashboard Screen Updates**
   - Added notification refresh functionality to the pull-to-refresh action
   - Notifications are now refreshed when the user pulls down to refresh the dashboard

3. **Supabase Integration**
   - Notifications are stored in the `notifications` table in Supabase
   - Real-time subscriptions ensure notifications are updated instantly
   - Database triggers create notifications when order status changes

## How It Works

### Notification Flow

1. **Order Status Change**:
   - When an order status changes (preparing, ready, completed, cancelled), a trigger in the database creates a notification
   - The notification is stored in the `notifications` table
   - The mobile app receives the notification through Supabase's real-time API

2. **Rating Submission**:
   - When a user rates an order, a trigger in the database creates a notification for the cafeteria owner
   - The notification is stored in the `notifications` table
   - The web app receives the notification through Supabase's real-time API

### Mobile App Integration

The mobile app now:
- Displays notifications in the notifications screen
- Shows a badge with the number of unread notifications
- Updates notifications in real-time when order status changes
- Allows users to mark notifications as read/unread

### Database Structure

The notification system uses the following tables in Supabase:

- `notifications`: Stores all user notifications
  - `id`: UUID primary key
  - `user_id`: UUID of the user the notification is for
  - `title`: Notification title
  - `message`: Notification message
  - `is_read`: Boolean indicating if the notification has been read
  - `related_order_id`: UUID of the related order (if applicable)
  - `created_at`: Timestamp when the notification was created

## Testing the System

To test the notification system:

1. Place an order from any cafeteria
2. In the Supabase dashboard, go to the Table Editor and find the `orders` table
3. Find your order and update its status to "preparing", "ready", or "completed"
4. You should receive a notification in the app

## Future Enhancements

1. **Push Notifications**: Add support for push notifications using Firebase Cloud Messaging (FCM) or a similar service.
2. **Notification Categories**: Add support for categorizing notifications (order updates, promotions, system messages, etc.).
3. **Notification Preferences**: Allow users to customize which notifications they want to receive.
4. **Rich Notifications**: Add support for rich notifications with images and action buttons.
5. **Notification History**: Add a way to view archived notifications.

## Conclusion

The enhanced notification system provides a robust way to keep users informed about their orders and other important events in the app. It uses Supabase's real-time capabilities to ensure notifications are delivered promptly and reliably.
