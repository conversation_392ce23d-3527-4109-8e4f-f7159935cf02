-- Clear existing data (optional - only use if you want to start fresh)
-- DELETE FROM menu_items;
-- DELETE FROM cafeterias;

-- Insert real cafeterias with actual images
INSERT INTO cafeterias (id, name, description, image_url, location, is_active, rating)
VALUES
  ('c1000000-0000-0000-0000-000000000001', 'Beanos Cafe', 'Popular campus cafe serving fresh coffee, pastries, and light meals in a cozy atmosphere.', 'https://images.unsplash.com/photo-**********-1e0d58224f24?q=80&w=2047&auto=format&fit=crop', 'Main Campus, Building A', true, 4.7),
  
  ('c2000000-0000-0000-0000-000000000002', 'Cinnamon Factory', 'Specialty bakery known for its freshly baked cinnamon rolls, pastries, and artisanal coffee.', 'https://images.unsplash.com/photo-1608649226842-f39257c9085f?q=80&w=2070&auto=format&fit=crop', 'Science Building, Ground Floor', true, 4.5),
  
  ('c3000000-0000-0000-0000-000000000003', 'Green Garden', 'Health-focused eatery offering fresh salads, grain bowls, smoothies, and vegetarian options.', 'https://images.unsplash.com/photo-1615361200141-f45961202b05?q=80&w=1964&auto=format&fit=crop', 'Library Complex, East Wing', true, 4.3),
  
  ('c4000000-0000-0000-0000-000000000004', 'Campus Grill', 'Classic grill serving burgers, sandwiches, and comfort food favorites with indoor and outdoor seating.', 'https://images.unsplash.com/photo-**********-fb0d29498b13?q=80&w=2064&auto=format&fit=crop', 'Student Center, 1st Floor', true, 4.2),
  
  ('c5000000-0000-0000-0000-000000000005', 'Bytes & Bites', 'Tech-themed cafe with fast WiFi, power outlets at every table, and a menu of quick bites and coffee.', 'https://images.unsplash.com/photo-1521017432531-fbd92d768814?q=80&w=2070&auto=format&fit=crop', 'Computer Science Building, 2nd Floor', true, 4.6);

-- Insert menu items for Beanos Cafe
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  -- Coffee & Beverages
  ('c1000000-0000-0000-0000-000000000001', 'Cappuccino', 'Rich espresso with steamed milk and velvety foam.', 4.50, 'https://images.unsplash.com/photo-1534778101976-62847782c213?q=80&w=2070&auto=format&fit=crop', 'Coffee', true),
  
  ('c1000000-0000-0000-0000-000000000001', 'Americano', 'Espresso diluted with hot water for a smooth, rich taste.', 3.75, 'https://images.unsplash.com/photo-1551030173-122aabc4489c?q=80&w=1974&auto=format&fit=crop', 'Coffee', true),
  
  ('c1000000-0000-0000-0000-000000000001', 'Iced Caramel Latte', 'Espresso with milk, caramel syrup, and ice, topped with whipped cream.', 5.25, 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?q=80&w=2069&auto=format&fit=crop', 'Coffee', true),
  
  ('c1000000-0000-0000-0000-000000000001', 'Chai Tea Latte', 'Spiced black tea with steamed milk and a hint of honey.', 4.75, 'https://images.unsplash.com/photo-1571658734974-e513dfb8b86b?q=80&w=2065&auto=format&fit=crop', 'Tea', true),
  
  -- Pastries & Breakfast
  ('c1000000-0000-0000-0000-000000000001', 'Butter Croissant', 'Flaky, buttery French pastry, baked fresh daily.', 3.25, 'https://images.unsplash.com/photo-1555507036-ab1f4038808a?q=80&w=2026&auto=format&fit=crop', 'Pastries', true),
  
  ('c1000000-0000-0000-0000-000000000001', 'Blueberry Muffin', 'Moist muffin packed with fresh blueberries and topped with sugar.', 3.50, 'https://images.unsplash.com/photo-1607958996333-41aef7caefaa?q=80&w=2070&auto=format&fit=crop', 'Pastries', true),
  
  ('c1000000-0000-0000-0000-000000000001', 'Avocado Toast', 'Whole grain toast topped with smashed avocado, cherry tomatoes, and microgreens.', 7.95, 'https://images.unsplash.com/photo-1603046891744-76e6300f82ef?q=80&w=1974&auto=format&fit=crop', 'Breakfast', true);

-- Insert menu items for Cinnamon Factory
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  -- Signature Cinnamon Rolls
  ('c2000000-0000-0000-0000-000000000002', 'Classic Cinnamon Roll', 'Our signature roll with cinnamon-sugar filling and cream cheese frosting.', 4.95, 'https://images.unsplash.com/photo-1509365465985-25d11c17e812?q=80&w=1935&auto=format&fit=crop', 'Cinnamon Rolls', true),
  
  ('c2000000-0000-0000-0000-000000000002', 'Caramel Pecan Roll', 'Cinnamon roll topped with caramel sauce and toasted pecans.', 5.50, 'https://images.unsplash.com/photo-1586724237569-f3d0c1dee8c6?q=80&w=2070&auto=format&fit=crop', 'Cinnamon Rolls', true),
  
  ('c2000000-0000-0000-0000-000000000002', 'Chocolate Cinnamon Roll', 'Cinnamon roll with chocolate chips and chocolate drizzle.', 5.25, 'https://images.unsplash.com/photo-1618411640018-972400a01458?q=80&w=1974&auto=format&fit=crop', 'Cinnamon Rolls', true),
  
  -- Other Pastries
  ('c2000000-0000-0000-0000-000000000002', 'Almond Croissant', 'Buttery croissant filled with almond cream and topped with sliced almonds.', 4.75, 'https://images.unsplash.com/photo-1623334044303-241021148842?q=80&w=1974&auto=format&fit=crop', 'Pastries', true),
  
  ('c2000000-0000-0000-0000-000000000002', 'Cheese Danish', 'Flaky pastry filled with sweet cream cheese and vanilla.', 4.25, 'https://images.unsplash.com/photo-1600617953089-218e1b0b4c34?q=80&w=1974&auto=format&fit=crop', 'Pastries', true),
  
  -- Beverages
  ('c2000000-0000-0000-0000-000000000002', 'Cinnamon Dolce Latte', 'Espresso with steamed milk, cinnamon syrup, and whipped cream.', 5.50, 'https://images.unsplash.com/photo-1572286258217-215cf8e8e48d?q=80&w=1974&auto=format&fit=crop', 'Coffee', true);

-- Insert menu items for Green Garden
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  -- Salads
  ('c3000000-0000-0000-0000-000000000003', 'Mediterranean Salad', 'Mixed greens with feta, olives, cucumbers, tomatoes, and lemon-herb dressing.', 9.95, 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=2070&auto=format&fit=crop', 'Salads', true),
  
  ('c3000000-0000-0000-0000-000000000003', 'Quinoa Power Bowl', 'Quinoa with roasted vegetables, chickpeas, avocado, and tahini dressing.', 10.50, 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1780&auto=format&fit=crop', 'Bowls', true),
  
  ('c3000000-0000-0000-0000-000000000003', 'Harvest Grain Bowl', 'Brown rice, sweet potatoes, kale, black beans, and cilantro-lime dressing.', 10.95, 'https://images.unsplash.com/photo-1543339308-43e59d6b73a6?q=80&w=2070&auto=format&fit=crop', 'Bowls', true),
  
  -- Smoothies
  ('c3000000-0000-0000-0000-000000000003', 'Green Machine Smoothie', 'Spinach, kale, banana, pineapple, and coconut water.', 6.50, 'https://images.unsplash.com/photo-1610970881699-44a5587cabec?q=80&w=1974&auto=format&fit=crop', 'Smoothies', true),
  
  ('c3000000-0000-0000-0000-000000000003', 'Berry Blast Smoothie', 'Mixed berries, banana, Greek yogurt, and almond milk.', 6.50, 'https://images.unsplash.com/photo-1553530666-ba11a90bb0ae?q=80&w=1936&auto=format&fit=crop', 'Smoothies', true),
  
  -- Snacks
  ('c3000000-0000-0000-0000-000000000003', 'Hummus & Veggie Plate', 'House-made hummus with fresh vegetables and whole grain pita.', 7.25, 'https://images.unsplash.com/photo-1505576399279-565b52d4ac71?q=80&w=1935&auto=format&fit=crop', 'Snacks', true);

-- Insert menu items for Campus Grill
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  -- Burgers
  ('c4000000-0000-0000-0000-000000000004', 'Classic Cheeseburger', 'Beef patty with cheddar, lettuce, tomato, onion, and special sauce on a brioche bun.', 9.95, 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?q=80&w=1899&auto=format&fit=crop', 'Burgers', true),
  
  ('c4000000-0000-0000-0000-000000000004', 'Bacon BBQ Burger', 'Beef patty with bacon, cheddar, onion rings, and BBQ sauce.', 11.50, 'https://images.unsplash.com/photo-1553979459-d2229ba7433b?q=80&w=1968&auto=format&fit=crop', 'Burgers', true),
  
  ('c4000000-0000-0000-0000-000000000004', 'Veggie Burger', 'House-made black bean patty with avocado, sprouts, and chipotle mayo.', 10.50, 'https://images.unsplash.com/photo-1520072959219-c595dc870360?q=80&w=2070&auto=format&fit=crop', 'Burgers', true),
  
  -- Sandwiches
  ('c4000000-0000-0000-0000-000000000004', 'Grilled Chicken Sandwich', 'Grilled chicken breast with lettuce, tomato, and honey mustard on ciabatta.', 9.75, 'https://images.unsplash.com/photo-1521390188846-e2a3a97453a0?q=80&w=2070&auto=format&fit=crop', 'Sandwiches', true),
  
  -- Sides
  ('c4000000-0000-0000-0000-000000000004', 'French Fries', 'Crispy golden fries seasoned with sea salt.', 3.95, 'https://images.unsplash.com/photo-1630384060421-cb20d0e0649d?q=80&w=1925&auto=format&fit=crop', 'Sides', true),
  
  ('c4000000-0000-0000-0000-000000000004', 'Onion Rings', 'Beer-battered onion rings with spicy dipping sauce.', 4.50, 'https://images.unsplash.com/photo-1639024471283-03518883512d?q=80&w=1974&auto=format&fit=crop', 'Sides', true);

-- Insert menu items for Bytes & Bites
INSERT INTO menu_items (cafeteria_id, name, description, price, image_url, category, is_available)
VALUES
  -- Quick Bites
  ('c5000000-0000-0000-0000-000000000005', 'Code Combo Sandwich', 'Turkey, bacon, avocado, and Swiss on sourdough with chips.', 8.95, 'https://images.unsplash.com/photo-1553909489-cd47e0907980?q=80&w=1925&auto=format&fit=crop', 'Sandwiches', true),
  
  ('c5000000-0000-0000-0000-000000000005', 'Programmer\'s Panini', 'Grilled chicken, pesto, mozzarella, and roasted red peppers.', 9.50, 'https://images.unsplash.com/photo-1509722747041-616f39b57569?q=80&w=2070&auto=format&fit=crop', 'Sandwiches', true),
  
  ('c5000000-0000-0000-0000-000000000005', 'Data Crunch Wrap', 'Spinach wrap with hummus, feta, cucumber, and mixed greens.', 7.95, 'https://images.unsplash.com/photo-1626700051175-6818013e1d4f?q=80&w=1964&auto=format&fit=crop', 'Wraps', true),
  
  -- Specialty Drinks
  ('c5000000-0000-0000-0000-000000000005', 'Java Chip Frappuccino', 'Blended coffee with chocolate chips, milk, and whipped cream.', 5.75, 'https://images.unsplash.com/photo-1577805947697-89e18249d767?q=80&w=1998&auto=format&fit=crop', 'Coffee', true),
  
  ('c5000000-0000-0000-0000-000000000005', 'Matcha Energy Latte', 'Ceremonial grade matcha with steamed milk and a touch of honey.', 5.50, 'https://images.unsplash.com/photo-1536256263959-770b48d82b0a?q=80&w=1935&auto=format&fit=crop', 'Tea', true),
  
  -- Snacks
  ('c5000000-0000-0000-0000-000000000005', 'Brain Food Trail Mix', 'House-made mix of nuts, dark chocolate, and dried fruits.', 4.25, 'https://images.unsplash.com/photo-1604068549290-dea0e4a305ca?q=80&w=1974&auto=format&fit=crop', 'Snacks', true),
  
  ('c5000000-0000-0000-0000-000000000005', 'Chocolate Chip Cookie', 'Large, soft-baked cookie with semi-sweet chocolate chunks.', 2.95, 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?q=80&w=1964&auto=format&fit=crop', 'Desserts', true);
