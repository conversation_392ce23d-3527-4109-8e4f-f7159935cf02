class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String profileImage;
  final bool hasPenalty;
  final double penaltyAmount;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.profileImage,
    this.hasPenalty = false,
    this.penaltyAmount = 0.0,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'] ?? '',
      profileImage: json['profileImage'] ?? '',
      hasPenalty: json['hasPenalty'] ?? false,
      penaltyAmount: (json['penaltyAmount'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'profileImage': profileImage,
      'hasPenalty': hasPenalty,
      'penaltyAmount': penaltyAmount,
    };
  }
}