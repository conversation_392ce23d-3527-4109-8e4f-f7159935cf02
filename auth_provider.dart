import 'package:flutter/material.dart';
import 'user.dart';

class AuthProvider extends ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _error;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;
  String? get error => _error;

  // Mock login for demonstration
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      if (email == '<EMAIL>' && password == 'password') {
        _currentUser = User(
          id: '1',
          name: '<PERSON>',
          email: email,
          phone: '01000000000',
          profileImage: 'assets/images/user-profile.png',
          hasPenalty: false,
          penaltyAmount: 0.0,
        );
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = 'Invalid email or password';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> register(String name, String email, String phone, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      _currentUser = User(
        id: '1',
        name: name,
        email: email,
        phone: phone,
        profileImage: 'assets/images/user-profile.png',
      );
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      _currentUser = null;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateProfile({String? name, String? email, String? phone}) async {
    if (_currentUser == null) return;
    _isLoading = true;
    notifyListeners();
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      _currentUser = User(
        id: _currentUser!.id,
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        phone: phone ?? _currentUser!.phone,
        profileImage: _currentUser!.profileImage,
        hasPenalty: _currentUser!.hasPenalty,
        penaltyAmount: _currentUser!.penaltyAmount,
      );
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Check if user has penalties
  bool get hasPenalty => _currentUser?.hasPenalty ?? false;
  double get penaltyAmount => _currentUser?.penaltyAmount ?? 0.0;
}