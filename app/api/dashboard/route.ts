import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseAdmin } from '@/lib/supabase'

// Revenue model: 4% service fee from users (capped at 20 EGP) + 10% commission from cafeterias
// Revenue is now calculated and stored in the orders table as user_service_fee, cafeteria_commission, admin_revenue

export async function GET(request: NextRequest) {
  try {
    const supabaseAdmin = createSupabaseAdmin()
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || 'This Month'
    const cafeteriaId = searchParams.get('cafeteriaId')

    // Calculate date range based on timeRange
    const now = new Date()
    let startDate: Date
    let endDate = now

    switch (timeRange) {
      case 'Today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        break
      case 'This Week':
        const dayOfWeek = now.getDay()
        startDate = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000)
        startDate.setHours(0, 0, 0, 0)
        break
      case 'This Month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        break
      case 'This Quarter':
        const quarter = Math.floor(now.getMonth() / 3)
        startDate = new Date(now.getFullYear(), quarter * 3, 1)
        break
      case 'This Year':
        startDate = new Date(now.getFullYear(), 0, 1)
        break
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
    }

    // Fetch all orders (including all statuses for comprehensive data)
    let ordersQuery = supabaseAdmin
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false })

    // Apply date filter based on time range
    if (timeRange === 'This Year') {
      // For "This Year", show all data from the beginning of current year
      const yearStart = new Date(now.getFullYear(), 0, 1)
      ordersQuery = ordersQuery.gte('created_at', yearStart.toISOString())
    } else if (timeRange !== 'All Time') {
      // For other ranges, apply the calculated date filter
      ordersQuery = ordersQuery
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
    }
    // For "All Time", no date filter is applied

    if (cafeteriaId && cafeteriaId !== 'all') {
      ordersQuery = ordersQuery.eq('cafeteria_id', cafeteriaId)
    }

    const { data: allOrders, error: ordersError } = await ordersQuery

    if (ordersError) {
      console.error('Error fetching orders:', ordersError)
      return NextResponse.json({ error: 'Failed to fetch orders' }, { status: 500 })
    }

    // Filter orders for metrics (exclude ALL cancelled orders)
    const orders = allOrders?.filter(order =>
      order.status !== 'cancelled'
    ) || []

    console.log('Admin Dashboard - Orders fetched:', {
      total: allOrders?.length || 0,
      filtered: orders.length,
      timeRange,
      dateRange: `${startDate.toISOString()} to ${endDate.toISOString()}`,
      sampleOrder: orders[0]
    })

    // Fetch cafeterias
    const { data: cafeterias, error: cafeteriasError } = await supabaseAdmin
      .from('cafeterias')
      .select('id, name, is_active, approval_status')

    if (cafeteriasError) {
      console.error('Error fetching cafeterias:', cafeteriasError)
    }

    // Fetch total users count from profiles
    const { count: totalUsers, error: usersError } = await supabaseAdmin
      .from('profiles')
      .select('*', { count: 'exact', head: true })

    if (usersError) {
      console.error('Error fetching users count:', usersError)
    }

    // Calculate metrics
    const totalOrders = orders?.length || 0
    const totalOrderValue = orders?.reduce((sum, order) => {
      const amount = parseFloat(order.total_amount) || 0
      return sum + amount
    }, 0) || 0

    // Calculate revenue using the admin_revenue field (which already contains userFee + commission)
    const totalRevenue = orders?.reduce((sum, order) => {
      const adminRevenue = parseFloat(order.admin_revenue) || 0
      return sum + adminRevenue
    }, 0) || 0

    console.log('Admin Dashboard - Final metrics:', {
      totalOrders,
      totalOrderValue,
      totalRevenue,
      activeCafeterias: cafeterias?.filter(c => c.is_active && c.approval_status === 'approved').length || 0,
      totalUsers: totalUsers || 0,
      hasData: orders.length > 0
    })

    // Generate chart data for the last 12 months
    const chartMonths = Array.from({ length: 12 }, (_, i) => {
      const date = new Date()
      date.setMonth(date.getMonth() - (11 - i))
      return date
    })

    // Fetch orders for chart data (last 12 months) - include all meaningful orders
    const chartStartDate = new Date()
    chartStartDate.setMonth(chartStartDate.getMonth() - 11)
    chartStartDate.setDate(1)
    chartStartDate.setHours(0, 0, 0, 0)

    const { data: allChartOrders } = await supabaseAdmin
      .from('orders')
      .select('total_amount, created_at, cafeteria_id, user_service_fee, cafeteria_commission, admin_revenue, status')
      .gte('created_at', chartStartDate.toISOString())
      .order('created_at', { ascending: false })

    // Filter chart orders (exclude ALL cancelled orders)
    const chartOrders = allChartOrders?.filter(order =>
      order.status !== 'cancelled'
    ) || []

    console.log('Admin Dashboard - Chart data:', {
      totalChartOrders: allChartOrders?.length || 0,
      filteredChartOrders: chartOrders.length,
      chartStartDate: chartStartDate.toISOString()
    })

    // Process chart data
    const revenueChart = chartMonths.map(month => {
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1)
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59)

      const monthOrders = chartOrders?.filter(order => {
        const orderDate = new Date(order.created_at)
        return orderDate >= monthStart && orderDate <= monthEnd
      }) || []

      return monthOrders.reduce((sum, order) => {
        const adminRevenue = parseFloat(order.admin_revenue) || 0
        return sum + adminRevenue
      }, 0)
    })

    const ordersChart = chartMonths.map(month => {
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1)
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59)

      return chartOrders?.filter(order => {
        const orderDate = new Date(order.created_at)
        return orderDate >= monthStart && orderDate <= monthEnd
      }).length || 0
    })

    // For users chart, simulate growth over time
    const usersChart = chartMonths.map((_, index) => {
      const baseUsers = totalUsers || 0
      const growthFactor = 0.7 + (index * 0.025) // Gradual growth
      return Math.floor(baseUsers * growthFactor)
    })

    // Calculate cafeteria-specific metrics
    const cafeteriaMetrics = cafeterias?.map(cafeteria => {
      const cafeteriaOrders = orders?.filter(order => order.cafeteria_id === cafeteria.id) || []
      const cafeteriaOrderValue = cafeteriaOrders.reduce((sum, order) => {
        const amount = parseFloat(order.total_amount) || 0
        return sum + amount
      }, 0)

      // Admin revenue (platform's cut)
      const adminRevenue = cafeteriaOrders.reduce((sum, order) => {
        const adminRev = parseFloat(order.admin_revenue) || 0
        return sum + adminRev
      }, 0)

      // Actual cafeteria revenue (total - admin cut)
      const actualCafeteriaRevenue = cafeteriaOrderValue - adminRevenue

      // Estimate users per cafeteria (simplified)
      const estimatedUsers = Math.floor((totalUsers || 0) / (cafeterias?.length || 1))

      return {
        id: cafeteria.id,
        name: cafeteria.name,
        status: cafeteria.is_active ? 'active' : 'inactive',
        users: estimatedUsers,
        orders: cafeteriaOrders.length,
        orderValue: cafeteriaOrderValue,
        revenue: adminRevenue, // Platform's revenue from this cafeteria
        cafeteriaRevenue: actualCafeteriaRevenue // Cafeteria's actual revenue
      }
    }) || []

    // Get active cafeterias count
    const activeCafeterias = cafeterias?.filter(c => c.is_active && c.approval_status === 'approved').length || 0

    // Calculate revenue breakdown using existing fields
    const userServiceFees = orders?.reduce((sum, order) => {
      return sum + (parseFloat(order.user_service_fee) || 0)
    }, 0) || 0

    const cafeteriaCommissions = orders?.reduce((sum, order) => {
      return sum + (parseFloat(order.cafeteria_commission) || 0)
    }, 0) || 0

    return NextResponse.json({
      success: true,
      metrics: {
        totalCafeterias: cafeterias?.length || 0,
        activeCafeterias,
        totalUsers: totalUsers || 0,
        totalOrders,
        totalOrderValue,
        totalRevenue,
        userServiceFees,
        cafeteriaCommissions
      },
      charts: {
        revenue: revenueChart,
        orders: ordersChart,
        users: usersChart,
        months: chartMonths.map(month => month.toLocaleDateString('en-US', { month: 'short' }))
      },
      cafeterias: cafeteriaMetrics,
      timeRange,
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      }
    })

  } catch (error) {
    console.error('Error in dashboard API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Update dashboard settings or trigger data refresh
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    if (action === 'refresh') {
      // Trigger a data refresh
      return NextResponse.json({
        success: true,
        message: 'Dashboard data refreshed successfully'
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('Error in dashboard POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
